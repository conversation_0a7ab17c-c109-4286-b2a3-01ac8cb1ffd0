# DMA状态监控指南

## 概述

为了确保Timer+DMA自动采集系统的可靠性，实现了多层次的DMA状态监控机制，可以实时检测DMA传输是否成功。

## 监控方法

### 1. DMA中断状态监控

在DMA中断处理函数中实时记录传输状态：

```c
// DMA状态变量
static volatile uint32_t dma_transfer_count = 0;    // 总传输次数
static volatile uint32_t dma_success_count = 0;     // 成功传输次数  
static volatile uint32_t dma_error_count = 0;       // 错误传输次数
static volatile bool dma_transfer_complete = false; // 传输完成标志

void DMA1_Stream5_IRQHandler(void)
{
    // 检查传输完成
    if (DMA_GetITStatus(DMA1_Stream5, DMA_IT_TCIF5)) {
        DMA_ClearITPendingBit(DMA1_Stream5, DMA_IT_TCIF5);
        
        dma_transfer_count++;
        dma_success_count++;
        dma_transfer_complete = true;
        
        // 数据验证
        uint16_t shunt_data = dma_read_buffer[0];
        if (shunt_data != 0xFFFF && shunt_data != 0x0000) {
            // 数据有效，存储到缓冲区
            samples_buf[active_idx][wr_pos] = shunt_data;
        }
    }
    
    // 检查传输错误
    if (DMA_GetITStatus(DMA1_Stream5, DMA_IT_TEIF5)) {
        DMA_ClearITPendingBit(DMA1_Stream5, DMA_IT_TEIF5);
        dma_error_count++;
        // 错误时重启DMA
    }
}
```

### 2. 状态查询接口

提供API函数查询DMA状态：

```c
// 获取DMA统计信息
bool INA226_GetDMAStatus(uint32_t *total_transfers, 
                         uint32_t *success_transfers, 
                         uint32_t *error_transfers);

// 检查最近是否有传输完成
bool INA226_IsDMATransferComplete(void);

// 应用层状态检查
bool App_Monitor_Check_DMA_Status(void);
```

### 3. 主循环监控

在主循环中定期检查DMA状态：

```c
// 每5秒检查一次DMA状态
if ((main_sys_tick_num % 5000) == 0) {
    App_Monitor_Check_DMA_Status();
}
```

## 状态指标

### 1. 传输成功率

```c
float success_rate = (float)success_transfers / total_transfers * 100.0f;

// 正常情况下应该 > 95%
if (success_rate < 90.0f) {
    logi("WARNING: DMA error rate too high!\r\n");
}
```

### 2. 传输活跃度

```c
static uint32_t last_total = 0;
if (total_transfers == last_total && total_transfers > 0) {
    logi("WARNING: DMA appears to be stalled!\r\n");
}
```

### 3. 数据有效性

```c
// 检查接收到的数据是否合理
uint16_t shunt_data = dma_read_buffer[0];
if (shunt_data == 0xFFFF || shunt_data == 0x0000) {
    // 可能是无效数据
}

// 检查数据变化
static uint16_t last_data = 0;
if (shunt_data == last_data) {
    // 数据可能没有更新
}
```

## 故障诊断

### 1. DMA传输失败

**症状**: `dma_error_count` 增加
**可能原因**:
- I2C总线冲突
- INA226设备无响应
- DMA配置错误

**解决方法**:
```c
// 重启DMA
DMA_Cmd(DMA1_Stream5, DISABLE);
DMA_SetCurrDataCounter(DMA1_Stream5, 2);
DMA_Cmd(DMA1_Stream5, ENABLE);
```

### 2. DMA停止工作

**症状**: `total_transfers` 不再增加
**可能原因**:
- 定时器停止
- DMA通道被禁用
- I2C外设故障

**解决方法**:
```c
// 检查定时器状态
if (!(TIM2->CR1 & TIM_CR1_CEN)) {
    TIM_Cmd(TIM2, ENABLE);
}

// 检查DMA状态
if (!(DMA1_Stream5->CR & DMA_SxCR_EN)) {
    DMA_Cmd(DMA1_Stream5, ENABLE);
}
```

### 3. 数据无效

**症状**: 接收到0xFFFF或0x0000
**可能原因**:
- INA226未正确配置
- I2C通信错误
- 电源问题

**解决方法**:
```c
// 重新初始化INA226
INA226_Init();
INA226_ConfigureAlert();
```

## 监控输出示例

### 正常工作状态
```
DMA Status: Total=1250, Success=1248, Errors=2, Rate=99.8%
```

### 异常状态
```
DMA Status: Total=1250, Success=1100, Errors=150, Rate=88.0%
WARNING: DMA error rate too high!
```

### 停止工作
```
WARNING: DMA appears to be stalled!
```

## 性能监控

### 1. 传输频率检查

```c
// 检查实际传输频率是否符合预期 (1kHz)
static uint32_t last_check_time = 0;
static uint32_t last_transfer_count = 0;

uint32_t time_diff = current_time - last_check_time;
uint32_t transfer_diff = total_transfers - last_transfer_count;

if (time_diff >= 1000) { // 每秒检查
    float actual_freq = (float)transfer_diff / (time_diff / 1000.0f);
    logi("DMA Frequency: %.1f Hz (Expected: 1000 Hz)\r\n", actual_freq);
}
```

### 2. 缓冲区利用率

```c
// 检查ping-pong缓冲区的使用情况
float buffer_usage = (float)wr_pos / INA226_BUFFER_SIZE * 100.0f;
logi("Buffer Usage: %.1f%%\r\n", buffer_usage);
```

## 自动恢复机制

### 1. 错误率过高时重启

```c
if (success_rate < 80.0f && total_transfers > 100) {
    logi("Auto-restarting DMA due to high error rate...\r\n");
    INA226_Stop_Timer_DMA();
    vTaskDelay(pdMS_TO_TICKS(100));
    INA226_Start_Timer_DMA();
}
```

### 2. 停止工作时重启

```c
static uint32_t stall_count = 0;
if (total_transfers == last_total) {
    stall_count++;
    if (stall_count >= 3) { // 连续3次检查都没有新传输
        logi("Auto-restarting stalled DMA...\r\n");
        INA226_Stop_Timer_DMA();
        INA226_Start_Timer_DMA();
        stall_count = 0;
    }
} else {
    stall_count = 0;
}
```

## 使用建议

1. **定期监控**: 每5-10秒检查一次DMA状态
2. **设置阈值**: 成功率 < 90% 时报警
3. **自动恢复**: 实现自动重启机制
4. **日志记录**: 保存关键状态信息用于调试
5. **性能分析**: 监控实际传输频率和缓冲区使用率

通过这些监控机制，可以确保DMA自动采集系统的高可靠性运行。
