#include "stm32f4xx.h"
#include "font.h" 
#include "main.h"
#include <stdio.h>
#include "lvgl.h"

void ST7789V_DC_HIGH(void);
void ST7789V_DC_LOW(void);
void ST7789V_BL_HIGH(void);
void ST7789V_BL_LOW(void);

// /*记录上一次传输数据长度*/
// uint16_t Tx_Len = 0;

// LCD显示模式设置
WRCACE_InitTypedef WRCACE_InitStruct = {
  .Adaptive_Brightness_Goal = 0x10,
  .Color_Enhancement_Cmd = 1,
  .Color_Enhancement_Extent = 11,
};

void ST7789V_DC_HIGH(void) {
  GPIO_SetBits(ST7789V_DC_GPIO_PORT, ST7789V_DC_PIN);
}

void ST7789V_DC_LOW(void) {
  GPIO_ResetBits(ST7789V_DC_GPIO_PORT, ST7789V_DC_PIN);
}

void ST7789V_BL_HIGH(void) {
  GPIO_SetBits(ST7789V_BL_GPIO_PORT, ST7789V_BL_PIN);
}

void ST7789V_BL_LOW(void) {
  GPIO_ResetBits(ST7789V_BL_GPIO_PORT, ST7789V_BL_PIN);
}

void ST7789V_RST_HIGH(void) {
  GPIO_SetBits(ST7789V_GPIO_PORT_RST, ST7789V_PIN_RST);
}

void ST7789V_RST_LOW(void) {
  GPIO_ResetBits(ST7789V_GPIO_PORT_RST, ST7789V_PIN_RST);
}
// GPIO初始化
void ST7789V_GPIO_Init(void) {

  // RCC_AHB1PeriphClockCmd(RCC_AHB1Periph_GPIOA, ENABLE);  // 使能GPIOB时钟
  // RCC_AHB1PeriphClockCmd(RCC_AHB1Periph_GPIOB, ENABLE);  // 使能GPIOB时钟

  GPIO_InitTypeDef GPIO_InitStructure;

  // 配置CS引脚
  GPIO_InitStructure.GPIO_Pin = SPI_ST7789V_PIN_CS;
  GPIO_InitStructure.GPIO_Speed = GPIO_Speed_100MHz;
  GPIO_InitStructure.GPIO_Mode = GPIO_Mode_OUT;
  GPIO_InitStructure.GPIO_OType = GPIO_OType_PP;
  GPIO_InitStructure.GPIO_PuPd = GPIO_PuPd_DOWN;//上拉
  GPIO_Init(SPI_ST7789V_GPIO_CS, &GPIO_InitStructure);

  // 配置DC引脚
  GPIO_InitStructure.GPIO_Pin = ST7789V_DC_PIN;
  GPIO_InitStructure.GPIO_Speed = GPIO_Speed_100MHz;
  GPIO_InitStructure.GPIO_Mode = GPIO_Mode_OUT;
  GPIO_InitStructure.GPIO_OType = GPIO_OType_PP;
  GPIO_InitStructure.GPIO_PuPd = GPIO_PuPd_DOWN;//上拉
  GPIO_Init(ST7789V_DC_GPIO_PORT, &GPIO_InitStructure);

  // 配置BL引脚
  GPIO_InitStructure.GPIO_Pin = ST7789V_BL_PIN;
  GPIO_InitStructure.GPIO_Speed = GPIO_Speed_100MHz;
  GPIO_InitStructure.GPIO_Mode = GPIO_Mode_OUT;
  GPIO_InitStructure.GPIO_OType = GPIO_OType_PP;
  GPIO_InitStructure.GPIO_PuPd = GPIO_PuPd_DOWN;//上拉
  GPIO_Init(ST7789V_BL_GPIO_PORT, &GPIO_InitStructure);

  // 配置RST引脚
  GPIO_InitStructure.GPIO_Pin = ST7789V_PIN_RST;
  GPIO_InitStructure.GPIO_Speed = GPIO_Speed_100MHz;
  GPIO_InitStructure.GPIO_Mode = GPIO_Mode_OUT;
  GPIO_InitStructure.GPIO_OType = GPIO_OType_PP;
  GPIO_InitStructure.GPIO_PuPd = GPIO_PuPd_DOWN;//上拉
  GPIO_Init(ST7789V_GPIO_PORT_RST, &GPIO_InitStructure);

  ST7789V_RST_LOW();  // 复位
  s_delay_ms(200);
  ST7789V_RST_HIGH();
  s_delay_ms(200);

  // 端口输出初始化为1
  ST7789V_CS_HIGH();
  ST7789V_DC_HIGH();
  ST7789V_BL_LOW(); 

  ST7789V_CS_LOW(); // 拉低 NSS 
}

/*SPI发送函数*/
// void ST7789V_SPISend(uint8_t data) {
//   // // ST7789V_CS_LOW(); // 拉低 NSS 
  
//   // // 等待发送缓冲区为空
//   // while (SPI_I2S_GetFlagStatus(SPI1, SPI_I2S_FLAG_TXE) == RESET);
//   // // 发送数据
//   // SPI_I2S_SendData(SPI1, data);
//   // // 等待传输完成
//   // while (SPI_I2S_GetFlagStatus(SPI1, SPI_I2S_FLAG_BSY) == SET);	

//   // // ST7789V_CS_HIGH(); // 拉高 NSS

//   SPI1_SendByte(data);
// }

void ST7789V_SPISendDataByte(uint8_t Data) {
  // ST7789V_SPISend(Data); 
  SPI1_DMA_StartTransfer(( uint8_t *)&Data, 1, TYPE_DATA);
}
void ST7789V_SPISendDataBytes(uint8_t *Data, uint32_t len) {
  // for (size_t i = 0; i < len; i++) {
  //   ST7789V_SPISendDataByte(Data[i]);
  // }
  SPI1_DMA_StartTransfer(Data, len, TYPE_DATA);
}

void ST7789V_SPISendData2Bytes(uint16_t Data) {
  uint8_t dat[2] =  {Data >> 8, Data & 0xFF};
  //  ST7789V_SPISend(Data >> 8);
  //  ST7789V_SPISend(Data & 0xFF);  
  SPI1_DMA_StartTransfer(dat, 2, TYPE_DATA);

}

void ST7789V_SPISendCmdByte(uint8_t Data) {
  ST7789V_DC_LOW();
  // ST7789V_SPISend(Data); 
  SPI1_DMA_StartTransfer( ( uint8_t *)&Data, 1, TYPE_CMD);
  ST7789V_DC_HIGH();
}

// /*ST7789V应用函数*/
// void ST7789V_SendOneColor(uint16_t Color) {
//   ST7789V_SPISendData2Bytes(Color);
//   // SPI1_DMA_StartTransfer(( uint8_t *)&Color, 2, TYPE_DATA);
// }

/*ST7789V应用函数*/
void ST7789V_SendColorLen(uint16_t color, uint32_t len) {
  // ST7789V_CS_LOW();  // 拉低 NSS
  // for (uint32_t i = 0; i < len; i++) {
  //   ST7789V_SPISendData2Bytes(color);
  // }
  // ST7789V_CS_HIGH();  // 拉高 NSS

  // logi("send color len %ld \n", len);
  // uint16_t *color_buf = (uint16_t *)malloc(len * 2);
  // if (color_buf == NULL) {
  //   return;
  // }
  uint16_t color_buf[320*12]={0x00};
  for (uint32_t i = 0; i < len; i++) {
    color_buf[i] = color;
  }
  SPI1_DMA_StartTransfer((uint8_t *)color_buf, len * 2, TYPE_DATA);
}

void ST7789V_SetLcdAddress(uint16_t Start_X, uint16_t Start_Y, uint16_t End_X, uint16_t End_Y, uint8_t Dir_Mode) {
  uint32_t width_offset_x = WIDTH_OFFSET;
  uint32_t height_offset_y = HIGH_OFFSET;

  switch (Dir_Mode) {
  case LV_DISP_ROT_NONE:
    width_offset_x = WIDTH_OFFSET;
    height_offset_y = HIGH_OFFSET;
    break;
  case LV_DISP_ROT_90:
    width_offset_x = WIDTH_OFFSET;
    height_offset_y = HIGH_OFFSET;
    break;
  case LV_DISP_ROT_180:
    width_offset_x = WIDTH_OFFSET;
    height_offset_y = HIGH_OFFSET;
    break;
  case LV_DISP_ROT_270:
    width_offset_x = WIDTH_OFFSET;
    height_offset_y = HIGH_OFFSET;
    break;
  }

  // 	ST7789V_SPISendCmdByte(0x2c);//储存器写

  if (Dir_Mode == 0) {
    ST7789V_SPISendCmdByte(0x2a);  // 列地址设置
    ST7789V_SPISendData2Bytes(Start_X + width_offset_x);
    ST7789V_SPISendData2Bytes(End_X + width_offset_x);
    ST7789V_SPISendCmdByte(0x2b);  // 行地址设置
    ST7789V_SPISendData2Bytes(Start_Y + height_offset_y);
    ST7789V_SPISendData2Bytes(End_Y + height_offset_y);
    ST7789V_SPISendCmdByte(0x2c);  // 储存器写
  } else if (Dir_Mode == 1) {
    ST7789V_SPISendCmdByte(0x2a);  // 列地址设置
    ST7789V_SPISendData2Bytes(Start_X + width_offset_x);
    ST7789V_SPISendData2Bytes(End_X + width_offset_x);
    ST7789V_SPISendCmdByte(0x2b);  // 行地址设置
    ST7789V_SPISendData2Bytes(Start_Y + height_offset_y);
    ST7789V_SPISendData2Bytes(End_Y + height_offset_y);
    ST7789V_SPISendCmdByte(0x2c);  // 储存器写
  } else if (Dir_Mode == 2) {
    ST7789V_SPISendCmdByte(0x2a);  // 列地址设置
    ST7789V_SPISendData2Bytes(Start_X + height_offset_y);
    ST7789V_SPISendData2Bytes(End_X + height_offset_y);
    ST7789V_SPISendCmdByte(0x2b);  // 行地址设置
    ST7789V_SPISendData2Bytes(Start_Y + width_offset_x);
    ST7789V_SPISendData2Bytes(End_Y + width_offset_x);
    ST7789V_SPISendCmdByte(0x2c);  // 储存器写
  } else {
    ST7789V_SPISendCmdByte(0x2a);  // 列地址设置
    ST7789V_SPISendData2Bytes(Start_X + height_offset_y);
    ST7789V_SPISendData2Bytes(End_X + height_offset_y);
    ST7789V_SPISendCmdByte(0x2b);  // 行地址设置
    ST7789V_SPISendData2Bytes(Start_Y + width_offset_x);
    ST7789V_SPISendData2Bytes(End_Y + width_offset_x);
    ST7789V_SPISendCmdByte(0x2c);  // 储存器写
  }
}

void ST7789V_SetLcdXY(uint16_t Start_X, uint16_t Start_Y, uint8_t Dir_Mode) {
  ST7789V_SetLcdAddress(Start_X, Start_Y, LCD_W - Start_X, LCD_H - Start_Y, Dir_Mode);
}
// extern void CST816T_Reset(void);
void ST7789V_Reset(void) {
  // CST816T_Reset();
}

/**
 * @brief Set the rotation direction of the display
 * @param m -> rotation parameter(please refer it in st7789.h)
 * @return none
 */
void ST7789_SetRotation(uint8_t m) {
  ST7789V_SPISendCmdByte(ST7789_MADCTL);  // MADCTL
  switch (m) {
  case 0:
    ST7789V_SPISendDataByte(ST7789_MADCTL_MX | ST7789_MADCTL_MY | ST7789_MADCTL_RGB);
    break;
  case 1:
    ST7789V_SPISendDataByte(ST7789_MADCTL_MY | ST7789_MADCTL_MV | ST7789_MADCTL_RGB);
    break;
  case 2:
    ST7789V_SPISendDataByte(ST7789_MADCTL_RGB);
    break;
  case 3:
    ST7789V_SPISendDataByte(ST7789_MADCTL_MX | ST7789_MADCTL_MV | ST7789_MADCTL_RGB);
    break;
  default:
    break;
  }
}

void ST7789V_SetDir(uint8_t Dir_Mode) {
  ST7789V_SPISendCmdByte(0x36); /*显示方向*/
  if (Dir_Mode == 0)
    ST7789V_SPISendDataByte(0x00);
  else if (Dir_Mode == 1)
    ST7789V_SPISendDataByte(0xC0);
  else if (Dir_Mode == 2)
    ST7789V_SPISendDataByte(0x70);
  else
    ST7789V_SPISendDataByte(0xA0);
}

void ST7789V_LcdInit(void) {
  ST7789V_GPIO_Init();  // 初始化GPIO
  ST7789V_Reset();

  ST7789V_BL_HIGH();

  // 以下为ST7789V寄存器配置
  ST7789V_SPISendCmdByte(0x11);
  s_delay_ms(60);

  ST7789V_SetDir(USE_HORIZONTAL);

  /*12位颜色为03h，16位颜色位05h，18位为06h，代表了颜色更多，显示更好*/
  ST7789V_SPISendCmdByte(0x3A);
  ST7789V_SPISendDataByte(0x55);

  ST7789V_SPISendCmdByte(0xB2);  //	Porch control
  {
    uint8_t data[] = {0x0C, 0x0C, 0x00, 0x33, 0x33};
    ST7789V_SPISendDataBytes(data, sizeof(data));
  }
  ST7789_SetRotation(USE_HORIZONTAL);  //	MADCTL (Display Rotation)

  /* Internal LCD Voltage generator settings */
  ST7789V_SPISendCmdByte(0XB7);   //	Gate Control
  ST7789V_SPISendDataByte(0x35);  //	Default value
  ST7789V_SPISendCmdByte(0xBB);   //	VCOM setting
  ST7789V_SPISendDataByte(0x19);  //	0.725v (default 0.75v for 0x20)
  ST7789V_SPISendCmdByte(0xC0);   //	LCMCTRL
  ST7789V_SPISendDataByte(0x2C);  //	Default value
  ST7789V_SPISendCmdByte(0xC2);   //	VDV and VRH command Enable
  ST7789V_SPISendDataByte(0x01);  //	Default value
  ST7789V_SPISendCmdByte(0xC3);   //	VRH set
  ST7789V_SPISendDataByte(0x12);  //	+-4.45v (defalut +-4.1v for 0x0B)
  ST7789V_SPISendCmdByte(0xC4);   //	VDV set
  ST7789V_SPISendDataByte(0x20);  //	Default value
  ST7789V_SPISendCmdByte(0xC6);   //	Frame rate control in normal mode
  ST7789V_SPISendDataByte(0x0F);  //	Default value (60HZ)
  ST7789V_SPISendCmdByte(0xD0);   //	Power control
  ST7789V_SPISendDataByte(0xA4);  //	Default value
  ST7789V_SPISendDataByte(0xA1);  //	Default value
  /**************** Division line ****************/

  ST7789V_SPISendCmdByte(0xE0);
  {
    uint8_t data[] = {0xD0, 0x04, 0x0D, 0x11, 0x13, 0x2B, 0x3F, 0x54, 0x4C, 0x18, 0x0D, 0x0B, 0x1F, 0x23};
    ST7789V_SPISendDataBytes(data, sizeof(data));
  }

  ST7789V_SPISendCmdByte(0xE1);
  {
    uint8_t data[] = {0xD0, 0x04, 0x0C, 0x11, 0x13, 0x2C, 0x3F, 0x44, 0x51, 0x2F, 0x1F, 0x1F, 0x20, 0x23};
    ST7789V_SPISendDataBytes(data, sizeof(data));
  }
  ST7789V_SPISendCmdByte(ST7789_INVON);   //	Inversion ON
  ST7789V_SPISendCmdByte(ST7789_SLPOUT);  //	Out of sleep mode
  ST7789V_SPISendCmdByte(ST7789_NORON);   //	Normal Display on
  ST7789V_SPISendCmdByte(ST7789_DISPON);  //	Main screen turned on

  ST7789V_SPISendCmdByte(0x21);  // 此命令用于从显示反转模式恢复。

  ST7789V_SPISendCmdByte(0x29);  // 显示打开

  ST7789V_SPISendCmdByte(0x2c);
  /*
  此命令用于将数据从MCU传输到帧存储器。
  -接受此命令时，列寄存器和页面寄存器将重置为开始列/开始
  页面位置。
  -根据 MADCTL 设置，起始列/起始页位置不同。
  - 发送任何其他命令都可以停止帧写入。
  */

  logi("st7789v init \r\n");
}

uint16_t ST7789V_VerticalScrollMode(
  uint16_t TopStill_H,
  uint16_t VerticalScroll_H,
  uint16_t BottomStill_H,
  uint16_t StartAddr,
  uint16_t ScrollTime) {
  static uint16_t i = 0;
  static uint8_t k = 1;
  if (k == 1) {
    k = 0;
    if ((TopStill_H + VerticalScroll_H + BottomStill_H) != 320)
      return VerticalScroll_H + 1;
    ST7789V_SPISendCmdByte(0x33);  // 设置滚动条件的各个区域高度，三部分相加=320
    ST7789V_SPISendData2Bytes(TopStill_H);
    ST7789V_SPISendData2Bytes(VerticalScroll_H);
    ST7789V_SPISendData2Bytes(BottomStill_H);
    ST7789V_SPISendCmdByte(0x37);
    ST7789V_SPISendData2Bytes(StartAddr);
    i = StartAddr;
  }
  if (i++ > VerticalScroll_H + TopStill_H) {
    i = TopStill_H;
  }
  ST7789V_SPISendCmdByte(0x37);
  ST7789V_SPISendData2Bytes(i);
  s_delay_ms(ScrollTime);
  return i;
}

void ST7789V_PartialDisplayModeCmd(FunctionalState NewState, uint16_t Start_Line, uint16_t End_Line) {
  if (NewState == ENABLE) {
    ST7789V_SPISendCmdByte(0x13);
  } else {
    ST7789V_SPISendCmdByte(0x30);
    ST7789V_SPISendDataByte((uint8_t)Start_Line >> 8);
    ST7789V_SPISendDataByte((uint8_t)Start_Line);
    ST7789V_SPISendDataByte((uint8_t)End_Line >> 8);
    ST7789V_SPISendDataByte((uint8_t)End_Line);
    ST7789V_SPISendCmdByte(0x12);
  }
}

void ST7789V_IdleModeCmd(FunctionalState NewState) {
  if (NewState == ENABLE) {
    ST7789V_SPISendCmdByte(0x39);
  } else if (NewState == DISABLE) {
    ST7789V_SPISendCmdByte(0x38);
  }
}

void ST7789V_DisplayCmd(FunctionalState NewState) {
  if (NewState == ENABLE) {
    ST7789V_SPISendCmdByte(0x29);
  } else if (NewState == DISABLE) {
    ST7789V_SPISendCmdByte(0x28);
  }
}

void ST7789V_SleepModeCmd(FunctionalState NewState) {
  if (NewState == ENABLE) {
    ST7789V_SPISendCmdByte(0x10);
    s_delay_ms(120);
  } else if (NewState == DISABLE) {
    ST7789V_SPISendCmdByte(0x11);
    s_delay_ms(120);
  }
}

void ST7789V_AdaptiveBrightnessColorEnhancementInit(WRCACE_InitTypedef *WRCACE_InitStruct) {
  ST7789V_SPISendCmdByte(0x55);
  ST7789V_SPISendDataByte(
    (WRCACE_InitStruct->Color_Enhancement_Extent << 4) | (WRCACE_InitStruct->Color_Enhancement_Cmd << 7) |
    WRCACE_InitStruct->Adaptive_Brightness_Goal);
}

void ST7789V_FillLcdScreen_ColorPtr(
  uint16_t Start_X,
  uint16_t Start_Y,
  uint16_t End_X,
  uint16_t End_Y,
  uint16_t *Color,
  uint8_t Dir_Mode) {
  uint32_t num;
  num = (End_X - Start_X + 1) * (End_Y - Start_Y + 1);
  ST7789V_SetLcdAddress(Start_X, Start_Y, End_X, End_Y, Dir_Mode);  // 设置显示范围

  // uint32_t i;
  // uint16_t tmp;
  // for (i = 0; i < num; i++) {
  //   tmp = (uint16_t)((*Color) << 8) + ((*Color) >> 8);
  //   Color++;
  //   ST7789V_SPISendData2Bytes(tmp);
  // }

  ST7789V_SPISendDataBytes((uint8_t *)Color, num*2);

}
