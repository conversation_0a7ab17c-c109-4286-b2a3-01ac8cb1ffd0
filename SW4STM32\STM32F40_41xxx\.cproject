<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<?fileVersion 4.0.0?><cproject storage_type_id="org.eclipse.cdt.core.XmlProjectDescriptionStorage">
	<storageModule moduleId="org.eclipse.cdt.core.settings">
		<cconfiguration id="fr.ac6.managedbuild.config.gnu.cross.exe.debug.**********">
			<storageModule buildSystemId="org.eclipse.cdt.managedbuilder.core.configurationDataProvider" id="fr.ac6.managedbuild.config.gnu.cross.exe.debug.**********" moduleId="org.eclipse.cdt.core.settings" name="Debug">
				<externalSettings/>
				<extensions>
					<extension id="org.eclipse.cdt.core.ELF" point="org.eclipse.cdt.core.BinaryParser"/>
					<extension id="org.eclipse.cdt.core.GASErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
					<extension id="org.eclipse.cdt.core.GmakeErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
					<extension id="org.eclipse.cdt.core.GLDErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
					<extension id="org.eclipse.cdt.core.CWDLocator" point="org.eclipse.cdt.core.ErrorParser"/>
					<extension id="org.eclipse.cdt.core.GCCErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
				</extensions>
			</storageModule>
			<storageModule moduleId="cdtBuildSystem" version="4.0.0">
				<configuration artifactExtension="elf" artifactName="STM32F40_41xxx" buildArtefactType="org.eclipse.cdt.build.core.buildArtefactType.exe" buildProperties="org.eclipse.cdt.build.core.buildArtefactType=org.eclipse.cdt.build.core.buildArtefactType.exe,org.eclipse.cdt.build.core.buildType=org.eclipse.cdt.build.core.buildType.debug" cleanCommand="rm -rf" description="" id="fr.ac6.managedbuild.config.gnu.cross.exe.debug.**********" name="Debug" parent="fr.ac6.managedbuild.config.gnu.cross.exe.debug" postannouncebuildStep="Generating binary and Printing size information:" postbuildStep="arm-none-eabi-objcopy -O binary &quot;${BuildArtifactFileBaseName}.elf&quot; &quot;${BuildArtifactFileBaseName}.bin&quot; &amp;&amp; arm-none-eabi-size &quot;${BuildArtifactFileName}&quot;">
					<folderInfo id="fr.ac6.managedbuild.config.gnu.cross.exe.debug.**********." name="/" resourcePath="">
						<toolChain id="fr.ac6.managedbuild.toolchain.gnu.cross.exe.debug.405671605" name="Ac6 STM32 MCU GCC" superClass="fr.ac6.managedbuild.toolchain.gnu.cross.exe.debug">
							<option id="fr.ac6.managedbuild.option.gnu.cross.prefix.1160904482" name="Prefix" superClass="fr.ac6.managedbuild.option.gnu.cross.prefix" value="arm-none-eabi-" valueType="string"/>
							<option id="fr.ac6.managedbuild.option.gnu.cross.mcu.1179342523" name="Mcu" superClass="fr.ac6.managedbuild.option.gnu.cross.mcu" value="STM32F417IGHx" valueType="string"/>
							<option id="fr.ac6.managedbuild.option.gnu.cross.board.751383727" name="Board" superClass="fr.ac6.managedbuild.option.gnu.cross.board" value="STM3241G-EVAL" valueType="string"/>
							<option id="fr.ac6.managedbuild.option.gnu.cross.instructionSet.1424765087" name="Instruction Set" superClass="fr.ac6.managedbuild.option.gnu.cross.instructionSet" value="fr.ac6.managedbuild.option.gnu.cross.instructionSet.thumbII" valueType="enumerated"/>
							<option id="fr.ac6.managedbuild.option.gnu.cross.fpu.418758642" name="Floating point hardware" superClass="fr.ac6.managedbuild.option.gnu.cross.fpu" value="fr.ac6.managedbuild.option.gnu.cross.fpu.fpv4-sp-d16" valueType="enumerated"/>
							<option id="fr.ac6.managedbuild.option.gnu.cross.floatabi.1523646580" name="Floating-point ABI" superClass="fr.ac6.managedbuild.option.gnu.cross.floatabi" value="fr.ac6.managedbuild.option.gnu.cross.floatabi.hard" valueType="enumerated"/>
							<targetPlatform archList="all" binaryParser="org.eclipse.cdt.core.ELF" id="fr.ac6.managedbuild.targetPlatform.gnu.cross.1035389891" isAbstract="false" osList="all" superClass="fr.ac6.managedbuild.targetPlatform.gnu.cross"/>
							<builder buildPath="${workspace_loc:/STM32F40_41xxx}/Debug" id="fr.ac6.managedbuild.builder.gnu.cross.2016236807" keepEnvironmentInBuildfile="false" managedBuildOn="true" name="Gnu Make Builder" superClass="fr.ac6.managedbuild.builder.gnu.cross">
								<outputEntries>
									<entry flags="VALUE_WORKSPACE_PATH|RESOLVED" kind="outputPath" name="Debug"/>
								</outputEntries>
							</builder>
							<tool id="fr.ac6.managedbuild.tool.gnu.cross.c.compiler.252097906" name="MCU GCC Compiler" superClass="fr.ac6.managedbuild.tool.gnu.cross.c.compiler">
								<option defaultValue="gnu.c.optimization.level.none" id="fr.ac6.managedbuild.gnu.c.compiler.option.optimization.level.137855300" name="Optimization Level" superClass="fr.ac6.managedbuild.gnu.c.compiler.option.optimization.level" useByScannerDiscovery="false" value="fr.ac6.managedbuild.gnu.c.optimization.level.size" valueType="enumerated"/>
								<option id="gnu.c.compiler.option.debugging.level.576972536" name="Debug Level" superClass="gnu.c.compiler.option.debugging.level" useByScannerDiscovery="false" value="gnu.c.debugging.level.max" valueType="enumerated"/>
								<option id="gnu.c.compiler.option.include.paths.812079907" name="Include paths (-I)" superClass="gnu.c.compiler.option.include.paths" useByScannerDiscovery="false" valueType="includePath">
									<listOptionValue builtIn="false" value="../../../"/>
									<listOptionValue builtIn="false" value="../../../../../Libraries/CMSIS/Device/ST/STM32F4xx/Include"/>
									<listOptionValue builtIn="false" value="../../../../../Libraries/STM32F4xx_StdPeriph_Driver/inc"/>
									<listOptionValue builtIn="false" value="../../../../../Utilities/STM32_EVAL/Common"/>
									<listOptionValue builtIn="false" value="../../../../../Utilities/STM32_EVAL/STM3240_41_G_EVAL"/>
									<listOptionValue builtIn="false" value="../../../../../Libraries/CMSIS/Include"/>
								</option>
								<option id="gnu.c.compiler.option.preprocessor.def.symbols.1229561551" name="Defined symbols (-D)" superClass="gnu.c.compiler.option.preprocessor.def.symbols" useByScannerDiscovery="false" valueType="definedSymbols">
									<listOptionValue builtIn="false" value="USE_STDPERIPH_DRIVER"/>
									<listOptionValue builtIn="false" value="STM32F40_41xxx"/>
									<listOptionValue builtIn="false" value="USE_STM324xG_EVAL"/>
								</option>
								<option id="fr.ac6.managedbuild.gnu.c.compiler.option.misc.other.2069025578" superClass="fr.ac6.managedbuild.gnu.c.compiler.option.misc.other" useByScannerDiscovery="false" value="-fmessage-length=0" valueType="string"/>
								<option id="gnu.c.compiler.option.dialect.std.421783750" name="Language standard" superClass="gnu.c.compiler.option.dialect.std" useByScannerDiscovery="true" value="gnu.c.compiler.dialect.default" valueType="enumerated"/>
								<inputType id="fr.ac6.managedbuild.tool.gnu.cross.c.compiler.input.c.680754886" superClass="fr.ac6.managedbuild.tool.gnu.cross.c.compiler.input.c"/>
								<inputType id="fr.ac6.managedbuild.tool.gnu.cross.c.compiler.input.s.1603052562" superClass="fr.ac6.managedbuild.tool.gnu.cross.c.compiler.input.s"/>
							</tool>
							<tool id="fr.ac6.managedbuild.tool.gnu.cross.cpp.compiler.1453025502" name="MCU G++ Compiler" superClass="fr.ac6.managedbuild.tool.gnu.cross.cpp.compiler">
								<option id="gnu.cpp.compiler.option.optimization.level.469765843" name="Optimization Level" superClass="gnu.cpp.compiler.option.optimization.level" useByScannerDiscovery="false" value="gnu.cpp.compiler.optimization.level.none" valueType="enumerated"/>
								<option id="gnu.cpp.compiler.option.debugging.level.715118395" name="Debug Level" superClass="gnu.cpp.compiler.option.debugging.level" useByScannerDiscovery="false" value="gnu.cpp.compiler.debugging.level.max" valueType="enumerated"/>
							</tool>
							<tool id="fr.ac6.managedbuild.tool.gnu.cross.c.linker.1084888723" name="MCU GCC Linker" superClass="fr.ac6.managedbuild.tool.gnu.cross.c.linker">
								<option id="fr.ac6.managedbuild.tool.gnu.cross.c.linker.script.1185625233" name="Linker Script (-T)" superClass="fr.ac6.managedbuild.tool.gnu.cross.c.linker.script" value="../STM32F417IGHx_FLASH.ld" valueType="string"/>
								<option id="gnu.c.link.option.libs.1021168964" name="Libraries (-l)" superClass="gnu.c.link.option.libs"/>
								<option id="gnu.c.link.option.paths.1754776863" name="Library search path (-L)" superClass="gnu.c.link.option.paths"/>
								<option id="gnu.c.link.option.ldflags.653508722" name="Linker flags" superClass="gnu.c.link.option.ldflags" value="-specs=nosys.specs -specs=nano.specs" valueType="string"/>
								<inputType id="cdt.managedbuild.tool.gnu.c.linker.input.1702198183" superClass="cdt.managedbuild.tool.gnu.c.linker.input">
									<additionalInput kind="additionalinputdependency" paths="$(USER_OBJS)"/>
									<additionalInput kind="additionalinput" paths="$(LIBS)"/>
								</inputType>
							</tool>
							<tool id="fr.ac6.managedbuild.tool.gnu.cross.cpp.linker.1376255903" name="MCU G++ Linker" superClass="fr.ac6.managedbuild.tool.gnu.cross.cpp.linker"/>
							<tool id="fr.ac6.managedbuild.tool.gnu.archiver.1311662500" name="MCU GCC Archiver" superClass="fr.ac6.managedbuild.tool.gnu.archiver"/>
							<tool id="fr.ac6.managedbuild.tool.gnu.cross.assembler.1479374182" name="MCU GCC Assembler" superClass="fr.ac6.managedbuild.tool.gnu.cross.assembler">
								<option id="gnu.both.asm.option.include.paths.1115845166" name="Include paths (-I)" superClass="gnu.both.asm.option.include.paths"/>
								<inputType id="cdt.managedbuild.tool.gnu.assembler.input.779888889" superClass="cdt.managedbuild.tool.gnu.assembler.input"/>
								<inputType id="fr.ac6.managedbuild.tool.gnu.cross.assembler.input.992496996" superClass="fr.ac6.managedbuild.tool.gnu.cross.assembler.input"/>
							</tool>
						</toolChain>
					</folderInfo>
					<fileInfo id="fr.ac6.managedbuild.config.gnu.cross.exe.debug.**********.671596356" name="system_stm32f4xx.c" rcbsApplicability="disable" resourcePath="CMSIS/system_stm32f4xx.c" toolsToInvoke="fr.ac6.managedbuild.tool.gnu.cross.c.compiler.252097906.586054866">
						<tool id="fr.ac6.managedbuild.tool.gnu.cross.c.compiler.252097906.586054866" name="MCU GCC Compiler" superClass="fr.ac6.managedbuild.tool.gnu.cross.c.compiler.252097906">
							<option id="gnu.c.compiler.option.preprocessor.def.symbols.1271499379" name="Defined symbols (-D)" superClass="gnu.c.compiler.option.preprocessor.def.symbols" valueType="definedSymbols">
								<listOptionValue builtIn="false" value="USE_STDPERIPH_DRIVER"/>
								<listOptionValue builtIn="false" value="STM32F40_41xxx"/>
								<listOptionValue builtIn="false" value="USE_STM324xG_EVAL"/>
							</option>
							<inputType id="fr.ac6.managedbuild.tool.gnu.cross.c.compiler.input.c.2133748545" superClass="fr.ac6.managedbuild.tool.gnu.cross.c.compiler.input.c"/>
							<inputType id="fr.ac6.managedbuild.tool.gnu.cross.c.compiler.input.s.722664385" superClass="fr.ac6.managedbuild.tool.gnu.cross.c.compiler.input.s"/>
						</tool>
					</fileInfo>
					<sourceEntries>
						<entry excluding="STM32F4xx_StdPeriph_Driver/stm32f4xx_flash_ramfunc.c|STM32F4xx_StdPeriph_Driver/stm32f4xx_cec.c|STM32F4xx_StdPeriph_Driver/stm32f4xx_qspi.c|STM32F4xx_StdPeriph_Driver/stm32f4xx_dsi.c|STM32F4xx_StdPeriph_Driver/stm32f4xx_fmc.c|SW4STM32/startup_stm32f469_479xx.s|SW4STM32/startup_stm32f446xx.s|SW4STM32/startup_stm32f429_439xx.s|SW4STM32/startup_stm32f427_437xx.s|SW4STM32/startup_stm32f412xg.s|SW4STM32/startup_stm32f411xe.s|SW4STM32/startup_stm32f410xx.s|SW4STM32/startup_stm32f401xx.s|../../Libraries/CMSIS/Device/ST/STM32F4xx/Source/Templates/iar/startup_stm32f446xx.s|../../Libraries/CMSIS/Device/ST/STM32F4xx/Source/Templates/iar/startup_stm32f412xg.s|../../Libraries/STM32F4xx_StdPeriph_Driver/src/stm32f4xx_flash_ramfunc.c|../../Libraries/STM32F4xx_StdPeriph_Driver/src/stm32f4xx_dsi.c|../../Libraries/STM32F4xx_StdPeriph_Driver/src/stm32f4xx_qspi.c|../../Libraries/CMSIS/Device/ST/STM32F4xx/Source/Templates/iar/startup_stm32f410xx.s|../../Libraries/STM32F4xx_StdPeriph_Driver/src/stm32f4xx_fmc.c|../../Libraries/CMSIS/Device/ST/STM32F4xx/Source/Templates/iar/startup_stm32f411xe.s|../../Libraries/CMSIS/Device/ST/STM32F4xx/Source/Templates/iar/startup_stm32f429_439xx.s|../../Libraries/STM32F4xx_StdPeriph_Driver/src/stm32f4xx_cec.c|../../Libraries/CMSIS/Device/ST/STM32F4xx/Source/Templates/iar/startup_stm32f401xx.s|../../Libraries/CMSIS/Device/ST/STM32F4xx/Source/Templates/iar/startup_stm32f427_437xx.s|../../Libraries/CMSIS/Device/ST/STM32F4xx/Source/Templates/iar/startup_stm32f469_479xx.s" flags="VALUE_WORKSPACE_PATH|RESOLVED" kind="sourcePath" name=""/>
					</sourceEntries>
				</configuration>
			</storageModule>
			<storageModule moduleId="org.eclipse.cdt.core.externalSettings"/>
		</cconfiguration>
	</storageModule>
	<storageModule moduleId="cdtBuildSystem" version="4.0.0">
		<project id="STM32F429_439xx.fr.ac6.managedbuild.target.gnu.cross.exe.489149579" name="Executable" projectType="fr.ac6.managedbuild.target.gnu.cross.exe"/>
	</storageModule>
	<storageModule moduleId="scannerConfiguration">
		<autodiscovery enabled="true" problemReportingEnabled="true" selectedProfileId=""/>
		<scannerConfigBuildInfo instanceId="fr.ac6.managedbuild.config.gnu.cross.exe.debug.**********;fr.ac6.managedbuild.config.gnu.cross.exe.debug.**********.;fr.ac6.managedbuild.tool.gnu.cross.c.compiler.252097906;fr.ac6.managedbuild.tool.gnu.cross.c.compiler.input.c.680754886">
			<autodiscovery enabled="false" problemReportingEnabled="true" selectedProfileId=""/>
		</scannerConfigBuildInfo>
		<!--scannerConfigBuildInfo instanceId="fr.ac6.managedbuild.config.gnu.cross.exe.release.$(RELEASE_CONFIG_UID);fr.ac6.managedbuild.config.gnu.cross.exe.release.$(RELEASE_CONFIG_UID).;fr.ac6.managedbuild.tool.gnu.cross.c.compiler.$(RELEASE_TOOL_COMPILER_UID);cdt.managedbuild.tool.gnu.c.compiler.input.$(RELEASE_TOOL_COMPILER_INPUT_UID)">
			<autodiscovery enabled="false" problemReportingEnabled="true" selectedProfileId=""/>
		</scannerConfigBuildInfo-->
	</storageModule>
	<storageModule moduleId="org.eclipse.cdt.core.LanguageSettingsProviders"/>
	<storageModule moduleId="refreshScope" versionNumber="2">
		<configuration configurationName="Debug">
			<resource resourceType="PROJECT" workspacePath="/STM32F40_41xxx"/>
		</configuration>
	</storageModule>
</cproject>
