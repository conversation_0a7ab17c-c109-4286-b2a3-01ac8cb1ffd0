
#include "stm32f4xx.h"
#include "app_spi.h"
#include <string.h>
#include "FreeRTOS.h"
#include "queue.h"


/* 定义数据缓冲区大小 */
#define BUFFER_SIZE  320*12

/* 定义发送和接收缓冲区 */
uint8_t SPI1_TxBuffer[BUFFER_SIZE];
uint8_t SPI1_RxBuffer[BUFFER_SIZE];


/* 全局标志，用于指示 DMA 传输完成 */
volatile uint8_t dmaTxComplete = 0;
volatile uint8_t dmaRxComplete = 0;

/* SPI1 数据寄存器地址 */
#define SPI1_DR_ADDRESS   ((uint32_t)&(SPI1->DR))


// --- dms queue 发送
#define SPI_QUEUE_SIZE  4  // 队列深度，根据实际需求调整

// // 待发送数据队列项
// typedef struct {
//     uint8_t *pData;        // 数据缓冲区指针
//     uint16_t length;       // 数据字节数
//     CommandType dataType;  // 数据类型（数据或命令）
//     SPIDataFree freeFlag;  // 传输完成后是否需要释放 pData
// } SPI_QueueItem;

// SPI_QueueItem spiQueue[SPI_QUEUE_SIZE];
// volatile uint16_t queueHead = 0; // 入队索引
// volatile uint16_t queueTail = 0; // 出队索引

// 标记 DMA 是否处于忙状态：1-忙，0-空闲
volatile uint8_t dmaBusy = 0;



void APP_SPI_Init(void) {
  /* 初始化SPI和相对应的GPIO口 */
 
  GPIO_InitTypeDef GPIO_InitStruct;
  /* 打开SPI1和GPIOA的时钟 */ 
  RCC_APB2PeriphClockCmd(RCC_APB2Periph_SPI1, ENABLE);

  // SPI1(PA4/5/6/7), SPI2(PB12/13/14/15) or SPI3(PA15, PC10/11/12)

  // /* 将NSS配置为普通推挽模式 SCK、MISO、MOSI配置为复用推挽模式 */
  // GPIO_InitStruct.GPIO_Mode = SPI_SCK_GPIO_MODE;
  // GPIO_InitStruct.GPIO_OType = GPIO_OType_PP;
  // GPIO_InitStruct.GPIO_Pin = SPI_SCK_GPIO_PIN;
  // GPIO_InitStruct.GPIO_Speed = SPI_SCK_GPIO_SPEED;
  // GPIO_InitStruct.GPIO_PuPd = GPIO_PuPd_DOWN;//上拉
  // GPIO_Init(SPI_SCK_GPIO_PORT, &GPIO_InitStruct);

  // GPIO_InitStruct.GPIO_Mode = SPI_MOSI_GPIO_MODE;
  // GPIO_InitStruct.GPIO_OType = GPIO_OType_PP;
  // GPIO_InitStruct.GPIO_Pin = SPI_MOSI_GPIO_PIN;
  // GPIO_InitStruct.GPIO_Speed = SPI_MOSI_GPIO_SPEED;
  // GPIO_InitStruct.GPIO_PuPd = GPIO_PuPd_DOWN;//上拉
  // GPIO_Init(SPI_MOSI_GPIO_PORT, &GPIO_InitStruct);

  // GPIO_InitStruct.GPIO_Mode = SPI_MISO_GPIO_MODE;
  // GPIO_InitStruct.GPIO_OType = GPIO_OType_PP;
  // GPIO_InitStruct.GPIO_Pin = SPI_MISO_GPIO_PIN;
  // GPIO_InitStruct.GPIO_Speed = SPI_MISO_GPIO_SPEED;
  // GPIO_InitStruct.GPIO_PuPd = GPIO_PuPd_DOWN;//上拉
  // GPIO_Init(SPI_MISO_GPIO_PORT, &GPIO_InitStruct);

  GPIO_InitStruct.GPIO_Mode = SPI_NSS_GPIO_MODE;
  GPIO_InitStruct.GPIO_OType = GPIO_OType_PP;
  GPIO_InitStruct.GPIO_Pin = SPI_NSS_GPIO_PIN;
  GPIO_InitStruct.GPIO_Speed = SPI_NSS_GPIO_SPEED;
  GPIO_InitStruct.GPIO_PuPd = GPIO_PuPd_DOWN;//上拉
  GPIO_Init(SPI_NSS_GPIO_PORT, &GPIO_InitStruct);


  GPIO_InitTypeDef GPIO_InitStructure;

  /* 将 PA5、PA6、PA7 配置为复用功能，并映射到 SPI1 (AF5) */
  GPIO_PinAFConfig(SPI_SCK_GPIO_PORT, GPIO_PinSource5, GPIO_AF_SPI1); // SCK
  GPIO_PinAFConfig(SPI_MOSI_GPIO_PORT, GPIO_PinSource6, GPIO_AF_SPI1); // MISO
  GPIO_PinAFConfig(SPI_MISO_GPIO_PORT, GPIO_PinSource7, GPIO_AF_SPI1); // MOSI

  GPIO_InitStructure.GPIO_Mode = GPIO_Mode_AF;
  GPIO_InitStructure.GPIO_Speed = GPIO_Speed_100MHz;
  GPIO_InitStructure.GPIO_OType = GPIO_OType_PP;
  GPIO_InitStructure.GPIO_PuPd  = GPIO_PuPd_DOWN;
        
  /*!< SPI SCK pin configuration */
  GPIO_InitStructure.GPIO_Pin = SPI_SCK_GPIO_PIN;
  GPIO_Init(SPI_SCK_GPIO_PORT, &GPIO_InitStructure);

  /*!< SPI MOSI pin configuration */
  GPIO_InitStructure.GPIO_Pin =  SPI_MOSI_GPIO_PIN;
  GPIO_Init(SPI_MOSI_GPIO_PORT, &GPIO_InitStructure);

  /*!< SPI MISO pin configuration */
  GPIO_InitStructure.GPIO_Pin =  SPI_MISO_GPIO_PIN;
  GPIO_Init(SPI_MISO_GPIO_PORT, &GPIO_InitStructure);

 

  RCC_APB2PeriphResetCmd(RCC_APB2Periph_SPI1,ENABLE);//复位SPI1
  RCC_APB2PeriphResetCmd(RCC_APB2Periph_SPI1,DISABLE);//停止复位SPI1

  /* 配置SPI为四分频、SCK空闲高电平、偶数边沿采样、8位数据帧、MSB先行、软件NSS、双线全双工模式,SPI外设为主机端 */
  SPI_InitTypeDef SPI_InitStruct;
  SPI_InitStruct.SPI_BaudRatePrescaler = SPIx_BaudRatePrescaler;
  SPI_InitStruct.SPI_CPHA = SPIx_CPHA;
  SPI_InitStruct.SPI_CPOL = SPIx_CPOL;
  SPI_InitStruct.SPI_CRCPolynomial = SPIx_CRCPolynomial;
  SPI_InitStruct.SPI_DataSize = SPIx_DataSize;
  SPI_InitStruct.SPI_Direction = SPIx_Direction;
  SPI_InitStruct.SPI_FirstBit = SPIx_FirstBit;
  SPI_InitStruct.SPI_Mode = SPIx_Mode;
  SPI_InitStruct.SPI_NSS = SPIx_NSS; 

  /* 将SPI外设配置信息写入寄存器,并且使能SPI外设 */
  SPI_Init(SPIx, &SPI_InitStruct);

  /* 使能 SPI1 的 DMA 请求 */
  SPI_I2S_DMACmd(SPI1, SPI_I2S_DMAReq_Tx, ENABLE);
  // SPI_I2S_DMACmd(SPI1, SPI_I2S_DMAReq_Rx, ENABLE);

  /* 使能 SPI1 */
  SPI_Cmd(SPIx, ENABLE);

  // SPI_I2S_ClearITPendingBit(SPIx, SPI_I2S_IT_RXNE);
  /* 拉高NSS */
  // SPI_NSS_Stop();


  // //这里只针对SPI口初始化
  // SPI_InitTypeDef SPI_InitStructure;
  // RCC_APB2PeriphResetCmd(RCC_APB2Periph_SPI1,ENABLE);//复位SPI1
  // RCC_APB2PeriphResetCmd(RCC_APB2Periph_SPI1,DISABLE);//停止复位SPI1
  // SPI_InitStructure.SPI_Direction = SPI_Direction_2Lines_FullDuplex; //设置SPI单向或者双向的数据模式：SPI设置为双线双向全双工
  // SPI_InitStructure.SPI_Mode = SPI_Mode_Slave; //设置SPI工作模式：设置为主SPI
  // SPI_InitStructure.SPI_DataSize = SPI_DataSize_8b; //设置SPI的数据大小：SPI发送接收8位帧结构
  // SPI_InitStructure.SPI_CPOL = SPI_CPOL_Low; //串行同步时钟的空闲状态为高电平
  // SPI_InitStructure.SPI_CPHA = SPI_CPHA_1Edge; //串行同步时钟的第二个跳变沿(上升或下降)数据被采样
  // SPI_InitStructure.SPI_NSS = SPI_NSS_Soft; //NSS信号由硬件(NSS管脚)还是软件(使用SSI位)管理：内部NSS信号有SSI位控制
  // SPI_InitStructure.SPI_BaudRatePrescaler = SPI_BaudRatePrescaler_2; //定义波特率预分频的值：波特率预分频值为256
  // SPI_InitStructure.SPI_FirstBit = SPI_FirstBit_MSB; //指定数据传输从MSB位还是LSB位开始：数据传输从MSB位开始
  // SPI_InitStructure.SPI_CRCPolynomial = 7; //CRC值计算的多项式
  // SPI_Init(SPIx, &SPI_InitStructure); //根据SPI_InitStruct中指定的参数初始化外设SPIx寄存器
  // SPI_Cmd(SPIx, ENABLE); //使能SPI外设
}


/*******************************************************************************
* 函数名称  : NVIC_Config
* 描述      : 配置 NVIC，用于使能 DMA 中断
*******************************************************************************/
void SPI1_DMA_NVIC_Config(void)
{
    NVIC_InitTypeDef NVIC_InitStructure;
    
    /* 配置 DMA2_Stream3 中断（SPI1 TX） */
    NVIC_InitStructure.NVIC_IRQChannel = DMA2_Stream3_IRQn;
    NVIC_InitStructure.NVIC_IRQChannelPreemptionPriority = 2;  // 根据实际需要设置优先级
    NVIC_InitStructure.NVIC_IRQChannelSubPriority = 0;
    NVIC_InitStructure.NVIC_IRQChannelCmd = ENABLE;
    NVIC_Init(&NVIC_InitStructure);
    
    /* 配置 DMA2_Stream0 中断（SPI1 RX） */
    NVIC_InitStructure.NVIC_IRQChannel = DMA2_Stream0_IRQn;
    NVIC_InitStructure.NVIC_IRQChannelPreemptionPriority = 3;  // 可根据需要设置不同子优先级
    NVIC_InitStructure.NVIC_IRQChannelSubPriority = 1;
    NVIC_Init(&NVIC_InitStructure);
}


/*******************************************************************************
* 函数名称  : SPI1_DMA_Config
* 描述      : 配置 DMA 进行 SPI1 数据传输，并使能传输完成中断
*******************************************************************************/
void SPI1_DMA_Config(void)
{
    DMA_InitTypeDef DMA_InitStructure;
    
    /* 使能 DMA2 时钟 (SPI1 的 DMA 通道在 DMA2) */
    RCC_AHB1PeriphClockCmd(RCC_AHB1Periph_DMA2, ENABLE);
    
    /*********************** 配置 SPI1 TX 的 DMA ****************************/
    /* 使用 DMA2_Stream3, Channel 3 (TX通道，参考手册确认通道号) */
    DMA_DeInit(DMA2_Stream3);
    while(DMA_GetCmdStatus(DMA2_Stream3) != DISABLE);
    
    DMA_InitStructure.DMA_Channel = DMA_Channel_3;
    DMA_InitStructure.DMA_PeripheralBaseAddr = SPI1_DR_ADDRESS;
    DMA_InitStructure.DMA_Memory0BaseAddr = (uint32_t)SPI1_TxBuffer;
    DMA_InitStructure.DMA_DIR = DMA_DIR_MemoryToPeripheral;
    DMA_InitStructure.DMA_BufferSize = BUFFER_SIZE;
    DMA_InitStructure.DMA_PeripheralInc = DMA_PeripheralInc_Disable;
    DMA_InitStructure.DMA_MemoryInc = DMA_MemoryInc_Enable;
    DMA_InitStructure.DMA_PeripheralDataSize = DMA_PeripheralDataSize_Byte;
    DMA_InitStructure.DMA_MemoryDataSize = DMA_MemoryDataSize_Byte;
    DMA_InitStructure.DMA_Mode = DMA_Mode_Normal;
    DMA_InitStructure.DMA_Priority = DMA_Priority_High;
    DMA_InitStructure.DMA_FIFOMode = DMA_FIFOMode_Disable;
    DMA_InitStructure.DMA_FIFOThreshold = DMA_FIFOThreshold_Full;
    DMA_InitStructure.DMA_MemoryBurst = DMA_MemoryBurst_Single;
    DMA_InitStructure.DMA_PeripheralBurst = DMA_PeripheralBurst_Single;
    DMA_Init(DMA2_Stream3, &DMA_InitStructure);
    
    /* 使能 DMA2_Stream3 的传输完成中断 */
    DMA_ITConfig(DMA2_Stream3, DMA_IT_TC, ENABLE);
    
    /*********************** 配置 SPI1 RX 的 DMA ****************************/
    /* 使用 DMA2_Stream0, Channel 3 (RX通道，参考手册确认) */
    DMA_DeInit(DMA2_Stream0);
    while(DMA_GetCmdStatus(DMA2_Stream0) != DISABLE);
    
    DMA_InitStructure.DMA_Channel = DMA_Channel_3;
    DMA_InitStructure.DMA_PeripheralBaseAddr = SPI1_DR_ADDRESS;
    DMA_InitStructure.DMA_Memory0BaseAddr = (uint32_t)SPI1_RxBuffer;
    DMA_InitStructure.DMA_DIR = DMA_DIR_PeripheralToMemory;
    DMA_InitStructure.DMA_BufferSize = BUFFER_SIZE;
    DMA_InitStructure.DMA_PeripheralInc = DMA_PeripheralInc_Disable;
    DMA_InitStructure.DMA_MemoryInc = DMA_MemoryInc_Enable;
    DMA_InitStructure.DMA_PeripheralDataSize = DMA_PeripheralDataSize_Byte;
    DMA_InitStructure.DMA_MemoryDataSize = DMA_MemoryDataSize_Byte;
    DMA_InitStructure.DMA_Mode = DMA_Mode_Normal;
    DMA_InitStructure.DMA_Priority = DMA_Priority_High;
    DMA_InitStructure.DMA_FIFOMode = DMA_FIFOMode_Disable;
    DMA_InitStructure.DMA_FIFOThreshold = DMA_FIFOThreshold_Full;
    DMA_InitStructure.DMA_MemoryBurst = DMA_MemoryBurst_Single;
    DMA_InitStructure.DMA_PeripheralBurst = DMA_PeripheralBurst_Single;
    DMA_Init(DMA2_Stream0, &DMA_InitStructure);
    
    // /* 使能 DMA2_Stream0 的传输完成中断 */
    // DMA_ITConfig(DMA2_Stream0, DMA_IT_TC, ENABLE);
}

// QueueHandle_t xSPIQueue;  // 用于存放 SPI_QueueItem 项的队列


// void vSPISendTask(void *pvParameters)
// {
//     SPI_QueueItem item;
//     for (;;) {
//         // 阻塞等待队列中有数据（等待时间可以设为 portMAX_DELAY）
//         if (xQueueReceive(xSPIQueue, &item, portMAX_DELAY) == pdPASS)
//         {
//             // 根据数据类型设置控制信号（例如：命令时将 DC 拉低，数据时拉高）
//             if (item.dataType == TYPE_CMD) {
//                 ST7789V_DC_LOW();
//                 // 可打印日志：send cmd
//             } else {
//                 ST7789V_DC_HIGH();
//                 // 可打印日志：send data
//             }
            
//             // 启动 DMA 传输（将 item.pData、item.length、item.dataType、item.freeFlag 传入）
//             // 这里调用我们之前写的 SPI1_DMA_StartTransfer 函数
//             SPI1_DMA_StartTransfer(item.pData, item.length, item.dataType, item.freeFlag);
            
//             // 等待当前 DMA 传输完成
//             // 这里采用轮询 dmaBusy 状态，也可以使用同步信号（如二值信号量）代替
//             while (dmaBusy) {
//                 vTaskDelay(1);  // 延时 1 个节拍
//             }
//         }
//     }
// }

void SPI_Send_Queue_init(void) {
  // // 在初始化时创建队列，假设队列长度为 20
  // xSPIQueue = xQueueCreate(512, sizeof(SPI_QueueItem));
  // if (xSPIQueue == NULL) {
  //   // 队列创建失败，处理错误
  //   logi("SPI_Queue fail \n");
  // }

  // xTaskCreate(vSPISendTask, "SPISendTask", configMINIMAL_STACK_SIZE * 2, NULL, tskIDLE_PRIORITY + 2, NULL);

}

//SPI2 读写一个字节

//TxData：要写入的字节

//返回值：读取到的字节

uint8_t SPI2_ReadByte() {
  // while(SPI_I2S_GetFlagStatus(SPIx， SPI_I2S_FLAG_TXE) == RESET){}//等待发送区空
  // SPI_I2S_SendData(SPIx， TxData); //通过外设SPIx发送一个byte 数据

  while (SPI_I2S_GetFlagStatus(SPIx, SPI_I2S_FLAG_RXNE) == RESET) {
  }  // 等待接收完一个byte

  return SPI_I2S_ReceiveData(SPIx);  // 返回通过SPIx最近接收的数据
}

void SPI1_SendByte(uint8_t data) {
  #if 1
    // 等待发送缓冲区为空
    while (SPI_I2S_GetFlagStatus(SPIx, SPI_I2S_FLAG_TXE) == RESET) ;
    // 发送数据
    SPI_I2S_SendData(SPIx, data);
    // 等待传输完成
    while (SPI_I2S_GetFlagStatus(SPIx, SPI_I2S_FLAG_BSY) == SET) ;
    // while (SPI_I2S_GetFlagStatus(SPIx, SPI_I2S_FLAG_RXNE) == RESET) ;
  #endif 
}


/*******************************************************************************
* DMA2_Stream3 中断处理函数 (SPI1 TX)
*******************************************************************************/
void DMA2_Stream3_IRQHandler(void)
{
// 检查传输完成标志（请根据实际宏定义确认标志位名称）
    if(DMA_GetFlagStatus(DMA2_Stream3, DMA_FLAG_TCIF3) != RESET)
    {
        // 清除传输完成标志
        DMA_ClearFlag(DMA2_Stream3, DMA_FLAG_TCIF3);
        
        // 标记 DMA 传输完成
        dmaBusy = 0;

    }
}

/*******************************************************************************
* DMA2_Stream0 中断处理函数 (SPI1 RX)
*******************************************************************************/
void DMA2_Stream0_IRQHandler(void)
{
    if(DMA_GetFlagStatus(DMA2_Stream0, DMA_FLAG_TCIF0) != RESET)
    {
        DMA_ClearFlag(DMA2_Stream0, DMA_FLAG_TCIF0);
        dmaRxComplete = 1;
    }
}

/**
  * @brief  使用 DMA 启动 SPI1 传输一次数据块
  * @param  pData: 待发送数据缓冲区指针
  * @param  length: 数据长度（字节）
  * @retval 无
  */
void SPI1_DMA_StartTransfer(uint8_t *pData, uint16_t length, CommandType dataType )
{
    // 禁用 DMA 流，等待其处于空闲状态
    DMA_Cmd(DMA2_Stream3, DISABLE);
    while(DMA_GetCmdStatus(DMA2_Stream3) != DISABLE);
    
    // 清除传输完成标志
    DMA_ClearFlag(DMA2_Stream3, DMA_FLAG_TCIF3);
    
    // 更新 DMA 内存地址和传输计数
    DMA2_Stream3->M0AR = (uint32_t)pData;
    DMA2_Stream3->NDTR = length;

    if(dataType==TYPE_CMD){
      ST7789V_DC_LOW();
    } else {
      ST7789V_DC_HIGH();
    }

    // 标记 DMA 正在传输
    dmaBusy = 1; 
    // 启动 DMA 传输
    DMA_Cmd(DMA2_Stream3, ENABLE);
    
    // wait
    while (dmaBusy==1) ;
    
}
 



