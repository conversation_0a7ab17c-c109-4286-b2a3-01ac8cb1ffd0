
#ifndef _APP_API_H
#define _APP_API_H

#include "stm32f4xx.h"
#include "stm32f4xx_spi.h"

// NSS (片选信号): PA4 或 PB4 或 PB12
// SCK (时钟信号): PA5 或 PB3 或 PB13
// MISO (主输入从输出): PA6 或 PB4 或 PB14
// MOSI (主输出从输入): PA7 或 PB5 或 PB15

#define SPIx                        SPI1
 

/* SCK/MOSI/MISO都配置为复用推挽输出，NSS由软件控制配置为普通的推挽输出 */
#define SPI_SCK_GPIO_PORT           GPIOA
#define SPI_SCK_GPIO_MODE           GPIO_Mode_AF
#define SPI_SCK_GPIO_SPEED          GPIO_Speed_100MHz
#define SPI_SCK_GPIO_PIN            GPIO_Pin_5

#define SPI_MOSI_GPIO_PORT          GPIOA
#define SPI_MOSI_GPIO_MODE          GPIO_Mode_AF
#define SPI_MOSI_GPIO_SPEED         GPIO_Speed_100MHz
#define SPI_MOSI_GPIO_PIN           GPIO_Pin_7

#define SPI_MISO_GPIO_PORT          GPIOA
#define SPI_MISO_GPIO_MODE          GPIO_Mode_AF
#define SPI_MISO_GPIO_SPEED         GPIO_Speed_100MHz
#define SPI_MISO_GPIO_PIN           GPIO_Pin_6

#define SPI_NSS_GPIO_PORT           GPIOA
#define SPI_NSS_GPIO_MODE           GPIO_Mode_OUT
#define SPI_NSS_GPIO_SPEED          GPIO_Speed_100MHz
#define SPI_NSS_GPIO_PIN            GPIO_Pin_4         //因为串行FLASH的CS引脚是PA4，SPI的NSS要和串行的一致

/* 配置SPI信息 */
#define SPIx_BaudRatePrescaler     SPI_BaudRatePrescaler_2//四分频，SPI1挂载在APB2上，四分频后波特率为25MHz
#define SPIx_CPHA                  SPI_CPHA_2Edge//偶数边沿采样
#define SPIx_CPOL                  SPI_CPOL_High//空闲时SCK高电平
#define SPIx_CRCPolynomial         7//不使用CRC功能，所以无所谓
#define SPIx_DataSize              SPI_DataSize_8b//数据帧格式为8位
#define SPIx_Direction             SPI_Direction_2Lines_FullDuplex
#define SPIx_FirstBit              SPI_FirstBit_MSB//高位先行
#define SPIx_Mode                  SPI_Mode_Master//主机模式
#define SPIx_NSS                   SPI_NSS_Soft//软件模拟

// 命令类型（例如：1 表示数据，2 表示命令）
typedef enum {
    TYPE_DATA = 1,
    TYPE_CMD = 2
} CommandType;

// 数据是否需要在传输后释放（例如：AfterFree 表示需要释放，NoFree 表示不释放）
typedef enum {
    AfterNone = 0,
    AfterFree = 1
} SPIDataFree;

/***************************************************************************************/
// #define SPI_NSS_Begin()            GPIO_ResetBits(SPI_NSS_GPIO_PORT, SPI_NSS_GPIO_PIN)
// #define SPI_NSS_Stop()             GPIO_SetBits(SPI_NSS_GPIO_PORT, SPI_NSS_GPIO_PIN)

void APP_SPI_Init(void);

void SPI1_DMA_Config(void);

void SPI1_DMA_NVIC_Config(void);

void SPI1_SendByte(uint8_t data);

void SPI_Send_Queue_init(void);

void SPI1_DMA_StartTransfer(uint8_t *pData, uint16_t length, CommandType dataType);

#endif /* _APP_API_H */


