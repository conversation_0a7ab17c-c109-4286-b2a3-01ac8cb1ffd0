<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<?fileVersion 4.0.0?><cproject storage_type_id="org.eclipse.cdt.core.XmlProjectDescriptionStorage">
	<storageModule moduleId="org.eclipse.cdt.core.settings">
		<cconfiguration id="com.atollic.truestudio.exe.debug.**********">
			<storageModule buildSystemId="org.eclipse.cdt.managedbuilder.core.configurationDataProvider" id="com.atollic.truestudio.exe.debug.**********" moduleId="org.eclipse.cdt.core.settings" name="Debug">
				<externalSettings/>
				<extensions>
					<extension id="org.eclipse.cdt.core.ELF" point="org.eclipse.cdt.core.BinaryParser"/>
					<extension id="org.eclipse.cdt.core.GCCErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
					<extension id="org.eclipse.cdt.core.GASErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
					<extension id="org.eclipse.cdt.core.GLDErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
				</extensions>
			</storageModule>
			<storageModule moduleId="cdtBuildSystem" version="4.0.0">
				<configuration artifactExtension="elf" artifactName="STM32F40_41xxx" buildArtefactType="org.eclipse.cdt.build.core.buildArtefactType.exe" buildProperties="org.eclipse.cdt.build.core.buildArtefactType=org.eclipse.cdt.build.core.buildArtefactType.exe" cleanCommand="rm -rf" description="" id="com.atollic.truestudio.exe.debug.**********" name="Debug" parent="com.atollic.truestudio.exe.debug" postbuildStep="" prebuildStep="">
					<folderInfo id="com.atollic.truestudio.exe.debug.**********.**********" name="/" resourcePath="">
						<toolChain id="com.atollic.truestudio.exe.debug.toolchain.683225115" name="Atollic ARM Tools" superClass="com.atollic.truestudio.exe.debug.toolchain">
							<option id="com.atollic.truestudio.toolchain_options.mcu.1437204172" name="Microcontroller" superClass="com.atollic.truestudio.toolchain_options.mcu" value="STM32F417IG" valueType="string"/>
							<option id="com.atollic.truestudio.toolchain_options.vendor.1286984724" name="Vendor name" superClass="com.atollic.truestudio.toolchain_options.vendor" value="STMicroelectronics" valueType="string"/>
							<targetPlatform archList="all" binaryParser="org.eclipse.cdt.core.ELF" id="com.atollic.truestudio.exe.debug.toolchain.platform.2054752829" isAbstract="false" name="Debug platform" superClass="com.atollic.truestudio.exe.debug.toolchain.platform"/>
							<builder buildPath="${workspace_loc:/STM32100B-EVAL/Debug}" customBuilderProperties="toolChainpathString=C:/Program Files (x86)/Atollic/TrueSTUDIO for ARM 5.4.2/ARMTools/bin|toolChainpathType=1|com.atollic.truestudio.common_options.target.vendor=STMicroelectronics|com.atollic.truestudio.common_options.target.mcu=STM32F417IG|" id="com.atollic.truestudio.mbs.builder1.1222194476" keepEnvironmentInBuildfile="false" managedBuildOn="true" name="CDT Internal Builder" superClass="com.atollic.truestudio.mbs.builder1">
								<outputEntries>
									<entry flags="VALUE_WORKSPACE_PATH|RESOLVED" kind="outputPath" name="Debug"/>
								</outputEntries>
							</builder>
							<tool command="arm-atollic-eabi-gcc -c" commandLinePattern="${COMMAND} ${FLAGS} ${OUTPUT_FLAG}${OUTPUT_PREFIX} ${OUTPUT} ${INPUTS}" id="com.atollic.truestudio.exe.debug.toolchain.as.1545899283" name="Assembler" superClass="com.atollic.truestudio.exe.debug.toolchain.as">
								<option id="com.atollic.truestudio.common_options.target.endianess.1608144567" name="Endianess" superClass="com.atollic.truestudio.common_options.target.endianess" value="com.atollic.truestudio.common_options.target.endianess.little" valueType="enumerated"/>
								<option id="com.atollic.truestudio.common_options.target.mcpu.286463162" name="Microcontroller" superClass="com.atollic.truestudio.common_options.target.mcpu" value="STM32F417IG" valueType="enumerated"/>
								<option id="com.atollic.truestudio.common_options.target.instr_set.2033833414" name="Instruction set" superClass="com.atollic.truestudio.common_options.target.instr_set" value="Thumb2" valueType="enumerated"/>
								<option id="com.atollic.truestudio.as.general.incpath.770237934" name="Include path" superClass="com.atollic.truestudio.as.general.incpath"/>
								<option id="com.atollic.truestudio.gcc.symbols.defined.1670122205" name="Defined symbols" superClass="com.atollic.truestudio.gcc.symbols.defined" valueType="definedSymbols">
									<listOptionValue builtIn="false" value=""/>
									<listOptionValue builtIn="false" value=""/>
								</option>
								<option id="com.atollic.truestudio.as.general.otherflags.51384659" name="Other options" superClass="com.atollic.truestudio.as.general.otherflags" value="" valueType="string"/>
								<option id="com.atollic.truestudio.as.general.warnings.1324541152" name="Suppress warnings " superClass="com.atollic.truestudio.as.general.warnings" value="true" valueType="boolean"/>
								<option id="com.atollic.truestudio.common_options.target.fpu.829683864" name="Floating point" superClass="com.atollic.truestudio.common_options.target.fpu" value="com.atollic.truestudio.common_options.target.fpu.hard" valueType="enumerated"/>
								<option id="com.atollic.truestudio.common_options.target.fpucore.1297069055" name="FPU" superClass="com.atollic.truestudio.common_options.target.fpucore" value="FPv4-SP-D16" valueType="enumerated"/>
								<inputType id="com.atollic.truestudio.as.input.374932590" name="Input" superClass="com.atollic.truestudio.as.input"/>
							</tool>
							<tool command="arm-atollic-eabi-gcc -c " commandLinePattern="${COMMAND} ${FLAGS} ${OUTPUT_FLAG}${OUTPUT_PREFIX} ${OUTPUT} ${INPUTS}" id="com.atollic.truestudio.exe.debug.toolchain.gcc.**********" name="C Compiler" superClass="com.atollic.truestudio.exe.debug.toolchain.gcc">
								<option id="com.atollic.truestudio.gcc.directories.select.330946435" name="Include path" superClass="com.atollic.truestudio.gcc.directories.select" valueType="includePath">
									<listOptionValue builtIn="false" value="../../../"/>
									<listOptionValue builtIn="false" value="../../../../../Libraries/CMSIS/Device/ST/STM32F4xx/Include"/>
									<listOptionValue builtIn="false" value="../../../../../Libraries/STM32F4xx_StdPeriph_Driver/inc"/>
									<listOptionValue builtIn="false" value="../../../../../Utilities/STM32_EVAL/Common"/>
									<listOptionValue builtIn="false" value="../../../../../Utilities/STM32_EVAL/STM3240_41_G_EVAL"/>
									<listOptionValue builtIn="false" value="../../../../../Libraries/CMSIS/Include"/>
								</option>
								<option id="com.atollic.truestudio.gcc.symbols.defined.1670122205" name="Defined symbols" superClass="com.atollic.truestudio.gcc.symbols.defined" valueType="definedSymbols">
									<listOptionValue builtIn="false" value="USE_STDPERIPH_DRIVER"/>
									<listOptionValue builtIn="false" value="STM32F40_41xxx"/>
									<listOptionValue builtIn="false" value="USE_STM324xG_EVAL"/>
								</option>
								<option id="com.atollic.truestudio.common_options.target.endianess.1854274048" name="Endianess" superClass="com.atollic.truestudio.common_options.target.endianess" value="com.atollic.truestudio.common_options.target.endianess.little" valueType="enumerated"/>
								<option id="com.atollic.truestudio.common_options.target.mcpu.191909026" name="Microcontroller" superClass="com.atollic.truestudio.common_options.target.mcpu" value="STM32F417IG" valueType="enumerated"/>
								<option id="com.atollic.truestudio.common_options.target.instr_set.1503159865" name="Instruction set" superClass="com.atollic.truestudio.common_options.target.instr_set" value="Thumb2" valueType="enumerated"/>
								<option id="com.atollic.truestudio.gcc.optimization.prep_garbage.586275206" name="Prepare dead code removal " superClass="com.atollic.truestudio.gcc.optimization.prep_garbage" value="true" valueType="boolean"/>
								<option id="com.atollic.truestudio.gcc.optimization.prep_data.1044766448" name="Prepare dead data removal" superClass="com.atollic.truestudio.gcc.optimization.prep_data" value="true" valueType="boolean"/>
								<option id="com.atollic.truestudio.gcc.misc.otherflags.895097519" name="Other options" superClass="com.atollic.truestudio.gcc.misc.otherflags" value="" valueType="string"/>
								<option id="com.atollic.truestudio.exe.debug.toolchain.gcc.optimization.level.1763412676" name="Optimization Level" superClass="com.atollic.truestudio.exe.debug.toolchain.gcc.optimization.level" value="com.atollic.truestudio.gcc.optimization.level.0s" valueType="enumerated"/>
								<option id="com.atollic.truestudio.common_options.target.fpu.829683864" name="Floating point" superClass="com.atollic.truestudio.common_options.target.fpu" value="com.atollic.truestudio.common_options.target.fpu.hard" valueType="enumerated"/>
								<option id="com.atollic.truestudio.common_options.target.fpucore.1297069055" name="FPU" superClass="com.atollic.truestudio.common_options.target.fpucore" value="FPv4-SP-D16" valueType="enumerated"/>
								<option id="com.atollic.truestudio.common_options.target.interwork.1173495076" name="Mix ARM/Thumb" superClass="com.atollic.truestudio.common_options.target.interwork"/>
								<inputType id="com.atollic.truestudio.gcc.input.850396938" superClass="com.atollic.truestudio.gcc.input"/>
							</tool>
							<tool id="com.atollic.truestudio.exe.debug.toolchain.ld.1444973427" name="C Linker" superClass="com.atollic.truestudio.exe.debug.toolchain.ld">
								<option id="com.atollic.truestudio.common_options.target.endianess.662778612" name="Endianess" superClass="com.atollic.truestudio.common_options.target.endianess" value="com.atollic.truestudio.common_options.target.endianess.little" valueType="enumerated"/>
								<option id="com.atollic.truestudio.common_options.target.mcpu.35700531" name="Microcontroller" superClass="com.atollic.truestudio.common_options.target.mcpu" value="STM32F417IG" valueType="enumerated"/>
								<option id="com.atollic.truestudio.common_options.target.instr_set.566688004" name="Instruction set" superClass="com.atollic.truestudio.common_options.target.instr_set" value="Thumb2" valueType="enumerated"/>
								<option id="com.atollic.truestudio.ld.general.scriptfile.1350476438" name="Linker script" superClass="com.atollic.truestudio.ld.general.scriptfile" value="..\STM32F417IG_FLASH.ld" valueType="string"/>
								<option id="com.atollic.truestudio.ld.optimization.do_garbage.1816004776" name="Dead code removal " superClass="com.atollic.truestudio.ld.optimization.do_garbage" value="true" valueType="boolean"/>
								<option id="com.atollic.truestudio.ld.libraries.list.848669414" name="Libraries" superClass="com.atollic.truestudio.ld.libraries.list"/>
								<option id="com.atollic.truestudio.ld.libraries.searchpath.1273009461" name="Library search path" superClass="com.atollic.truestudio.ld.libraries.searchpath"/>
								<option id="com.atollic.truestudio.ld.misc.linkerflags.1948405714" name="Other options" superClass="com.atollic.truestudio.ld.misc.linkerflags" value="" valueType="string"/>
								<option id="com.atollic.truestudio.common_options.target.fpu.829683864" name="Floating point" superClass="com.atollic.truestudio.common_options.target.fpu" value="com.atollic.truestudio.common_options.target.fpu.hard" valueType="enumerated"/>
								<option id="com.atollic.truestudio.common_options.target.fpucore.1297069055" name="FPU" superClass="com.atollic.truestudio.common_options.target.fpucore" value="FPv4-SP-D16" valueType="enumerated"/>
								<inputType id="com.atollic.truestudio.ld.input.1372975870" name="Input" superClass="com.atollic.truestudio.ld.input">
									<additionalInput kind="additionalinputdependency" paths="$(USER_OBJS)"/>
									<additionalInput kind="additionalinput" paths="$(LIBS)"/>
								</inputType>
							</tool>
							<tool id="com.atollic.truestudio.exe.debug.toolchain.gpp.981298185" name="C++ Compiler" superClass="com.atollic.truestudio.exe.debug.toolchain.gpp">
								<option id="com.atollic.truestudio.gpp.symbols.defined.1619552487" name="Defined symbols" superClass="com.atollic.truestudio.gpp.symbols.defined" valueType="stringList">
									<listOptionValue builtIn="false" value="STM32F10X_MD"/>
									<listOptionValue builtIn="false" value="USE_STDPERIPH_DRIVER"/>
								</option>
								<option id="com.atollic.truestudio.common_options.target.endianess.176262651" name="Endianess" superClass="com.atollic.truestudio.common_options.target.endianess" value="com.atollic.truestudio.common_options.target.endianess.little" valueType="enumerated"/>
								<option id="com.atollic.truestudio.common_options.target.mcpu.630487808" name="Microcontroller" superClass="com.atollic.truestudio.common_options.target.mcpu" value="STM32F417IG" valueType="enumerated"/>
								<option id="com.atollic.truestudio.common_options.target.instr_set.227324580" name="Instruction set" superClass="com.atollic.truestudio.common_options.target.instr_set" value="Thumb2" valueType="enumerated"/>
								<option id="com.atollic.truestudio.gpp.optimization.prep_garbage.26464025" name="Prepare dead code removal" superClass="com.atollic.truestudio.gpp.optimization.prep_garbage" value="true" valueType="boolean"/>
								<option id="com.atollic.truestudio.gpp.optimization.fno_rtti.1009504734" name="Disable RTTI" superClass="com.atollic.truestudio.gpp.optimization.fno_rtti"/>
								<option id="com.atollic.truestudio.gpp.optimization.fno_exceptions.342217745" name="Disable exception handling" superClass="com.atollic.truestudio.gpp.optimization.fno_exceptions"/>
								<option id="com.atollic.truestudio.common_options.target.fpu.829683864" name="Floating point" superClass="com.atollic.truestudio.common_options.target.fpu" value="com.atollic.truestudio.common_options.target.fpu.hard" valueType="enumerated"/>
								<option id="com.atollic.truestudio.common_options.target.fpucore.1297069055" name="FPU" superClass="com.atollic.truestudio.common_options.target.fpucore" value="FPv4-SP-D16" valueType="enumerated"/>
							</tool>
							<tool id="com.atollic.truestudio.exe.debug.toolchain.ldcc.428260" name="C++ Linker" superClass="com.atollic.truestudio.exe.debug.toolchain.ldcc">
								<option id="com.atollic.truestudio.common_options.target.endianess.2051741049" name="Endianess" superClass="com.atollic.truestudio.common_options.target.endianess" value="com.atollic.truestudio.common_options.target.endianess.little" valueType="enumerated"/>
								<option id="com.atollic.truestudio.common_options.target.mcpu.1701726850" name="Microcontroller" superClass="com.atollic.truestudio.common_options.target.mcpu" value="STM32F417IG" valueType="enumerated"/>
								<option id="com.atollic.truestudio.common_options.target.instr_set.1600237525" name="Instruction set" superClass="com.atollic.truestudio.common_options.target.instr_set" value="Thumb2" valueType="enumerated"/>
								<option id="com.atollic.truestudio.ldcc.optimization.do_garbage.956844137" name="Dead code removal" superClass="com.atollic.truestudio.ldcc.optimization.do_garbage" value="true" valueType="boolean"/>
								<option id="com.atollic.truestudio.ldcc.general.scriptfile.561304432" name="Linker script" superClass="com.atollic.truestudio.ldcc.general.scriptfile" value="${workspace_loc:\STM32100B-EVAL\stm32_flash.ld}" valueType="string"/>
								<option id="com.atollic.truestudio.common_options.target.fpu.829683864" name="Floating point" superClass="com.atollic.truestudio.common_options.target.fpu" value="com.atollic.truestudio.common_options.target.fpu.hard" valueType="enumerated"/>
								<option id="com.atollic.truestudio.common_options.target.fpucore.1297069055" name="FPU" superClass="com.atollic.truestudio.common_options.target.fpucore" value="FPv4-SP-D16" valueType="enumerated"/>
							</tool>
							<tool id="com.atollic.truestudio.exe.debug.toolchain.secoutput.839033486" name="Other" superClass="com.atollic.truestudio.exe.debug.toolchain.secoutput"/>
							<tool id="com.atollic.truestudio.ar.base.1865637305" name="Archiver" superClass="com.atollic.truestudio.ar.base"/>
						</toolChain>
					</folderInfo>
					<fileInfo id="com.atollic.truestudio.exe.debug.311825581.983937533" name="" rcbsApplicability="disable" resourcePath="nofile" toolsToInvoke="com.atollic.truestudio.exe.debug.toolchain.gcc.**********.1976695455">
						<tool id="com.atollic.truestudio.exe.debug.toolchain.gcc.**********.1976695455" name="C Compiler" superClass="com.atollic.truestudio.exe.debug.toolchain.gcc.**********">
							<inputType id="com.atollic.truestudio.gcc.input.1274347818" superClass="com.atollic.truestudio.gcc.input"/>
						</tool>
					</fileInfo>
					<fileInfo id="com.atollic.truestudio.exe.debug.**********.1109897533" name="startup_stm32f401xx.s" rcbsApplicability="disable" resourcePath="TrueSTUDIO/startup_stm32f401xx.s" toolsToInvoke="com.atollic.truestudio.exe.debug.toolchain.as.1545899283.999021955">
						<tool command="arm-atollic-eabi-gcc -c" commandLinePattern="${COMMAND} ${FLAGS} ${OUTPUT_FLAG}${OUTPUT_PREFIX} ${OUTPUT} ${INPUTS}" id="com.atollic.truestudio.exe.debug.toolchain.as.1545899283.999021955" name="Assembler" superClass="com.atollic.truestudio.exe.debug.toolchain.as.1545899283">
							<option id="com.atollic.truestudio.common_options.target.instr_set.1477550732" name="Instruction set" superClass="com.atollic.truestudio.common_options.target.instr_set" value="com.atollic.truestudio.common_options.target.instr_set.thumb2" valueType="enumerated"/>
							<option id="com.atollic.truestudio.common_options.target.mcpu.1062054251" name="Microcontroller" superClass="com.atollic.truestudio.common_options.target.mcpu" value="com.atollic.truestudio.common_options.target.mcpu.STM32F417IG" valueType="enumerated"/>
							<option id="com.atollic.truestudio.common_options.target.fpucore.468576201" name="FPU" superClass="com.atollic.truestudio.common_options.target.fpucore" value="com.atollic.truestudio.common_options.target.fpucore.fpv4-sp-d16" valueType="enumerated"/>
							<inputType id="com.atollic.truestudio.as.input.361043177" name="Input" superClass="com.atollic.truestudio.as.input"/>
						</tool>
						<tool announcement="Invoking: Resource Custom Build Step" command="" customBuildStep="true" id="org.eclipse.cdt.managedbuilder.ui.rcbs.354167670" name="Resource Custom Build Step">
							<inputType id="org.eclipse.cdt.managedbuilder.ui.rcbs.inputtype.974796371" name="Resource Custom Build Step Input Type">
								<additionalInput kind="additionalinputdependency" paths=""/>
							</inputType>
							<outputType id="org.eclipse.cdt.managedbuilder.ui.rcbs.outputtype.920090535" name="Resource Custom Build Step Output Type" outputNames=""/>
						</tool>
					</fileInfo>
					<sourceEntries>
						<entry excluding="TrueSTUDIO/startup_stm32f401xx.s|TrueSTUDIO/startup_stm32f446xx.s|TrueSTUDIO/startup_stm32f412xg.s|STM32F4xx_StdPeriph_Driver/stm32f4xx_flash_ramfunc.c|STM32F4xx_StdPeriph_Driver/stm32f4xx_dsi.c|STM32F4xx_StdPeriph_Driver/stm32f4xx_qspi.c|TrueSTUDIO/startup_stm32f410xx.s|STM32F4xx_StdPeriph_Driver/stm32f4xx_fmc.c|TrueSTUDIO/startup_stm32f411xe.s|TrueSTUDIO/startup_stm32f429_439xx.s|STM32F4xx_StdPeriph_Driver/stm32f4xx_cec.c|TrueSTUDIO/startup_stm32f427_437xx.s|TrueSTUDIO/startup_stm32f469_479xx.s" flags="VALUE_WORKSPACE_PATH|RESOLVED" kind="sourcePath" name=""/>
					</sourceEntries>
				</configuration>
			</storageModule>
			<storageModule moduleId="org.eclipse.cdt.core.externalSettings"/>
			<storageModule moduleId="org.eclipse.cdt.core.language.mapping"/>
			<storageModule moduleId="org.eclipse.cdt.internal.ui.text.commentOwnerProjectMappings"/>
		</cconfiguration>
	</storageModule>
	<storageModule moduleId="cdtBuildSystem" version="4.0.0">
		<project id="STM32100B-EVAL.com.atollic.truestudio.exe.**********" name="Executable" projectType="com.atollic.truestudio.exe"/>
	</storageModule>
	<storageModule moduleId="refreshScope"/>
	<storageModule moduleId="scannerConfiguration">
		<autodiscovery enabled="true" problemReportingEnabled="true" selectedProfileId=""/>
		<profile id="org.eclipse.cdt.make.core.GCCStandardMakePerFileProfile">
			<buildOutputProvider>
				<openAction enabled="true" filePath=""/>
				<parser enabled="true"/>
			</buildOutputProvider>
			<scannerInfoProvider id="makefileGenerator">
				<runAction arguments="-f ${project_name}_scd.mk" command="make" useDefault="true"/>
				<parser enabled="true"/>
			</scannerInfoProvider>
		</profile>
		<scannerConfigBuildInfo instanceId="com.atollic.truestudio.exe.debug.**********;com.atollic.truestudio.exe.debug.**********.303277065;com.atollic.truestudio.exe.debug.toolchain.gcc.393815702;com.atollic.truestudio.gcc.input.**********">
			<autodiscovery enabled="true" problemReportingEnabled="true" selectedProfileId="org.eclipse.cdt.managedbuilder.core.GCCManagedMakePerProjectProfileC"/>
			<profile id="org.eclipse.cdt.make.core.GCCStandardMakePerFileProfile">
				<buildOutputProvider>
					<openAction enabled="true" filePath=""/>
					<parser enabled="true"/>
				</buildOutputProvider>
				<scannerInfoProvider id="makefileGenerator">
					<runAction arguments="-f ${project_name}_scd.mk" command="make" useDefault="true"/>
					<parser enabled="true"/>
				</scannerInfoProvider>
			</profile>
		</scannerConfigBuildInfo>
		<scannerConfigBuildInfo instanceId="com.atollic.truestudio.exe.debug.**********;com.atollic.truestudio.exe.debug.**********.;com.atollic.truestudio.exe.debug.toolchain.gcc.**********;com.atollic.truestudio.gcc.input.850396938">
			<autodiscovery enabled="true" problemReportingEnabled="true" selectedProfileId="org.eclipse.cdt.managedbuilder.core.GCCManagedMakePerProjectProfileC"/>
			<profile id="org.eclipse.cdt.make.core.GCCStandardMakePerFileProfile">
				<buildOutputProvider>
					<openAction enabled="true" filePath=""/>
					<parser enabled="true"/>
				</buildOutputProvider>
				<scannerInfoProvider id="makefileGenerator">
					<runAction arguments="-f ${project_name}_scd.mk" command="make" useDefault="true"/>
					<parser enabled="true"/>
				</scannerInfoProvider>
			</profile>
		</scannerConfigBuildInfo>
		<scannerConfigBuildInfo instanceId="com.atollic.truestudio.exe.debug.**********;com.atollic.truestudio.exe.debug.**********.**********;com.atollic.truestudio.exe.debug.toolchain.gcc.**********;com.atollic.truestudio.gcc.input.850396938">
			<autodiscovery enabled="true" problemReportingEnabled="true" selectedProfileId="com.atollic.truestudio.mbs.ARMToolsPerProjectProfileC"/>
			<profile id="org.eclipse.cdt.make.core.GCCStandardMakePerFileProfile">
				<buildOutputProvider>
					<openAction enabled="true" filePath=""/>
					<parser enabled="true"/>
				</buildOutputProvider>
				<scannerInfoProvider id="makefileGenerator">
					<runAction arguments="-f ${project_name}_scd.mk" command="make" useDefault="true"/>
					<parser enabled="true"/>
				</scannerInfoProvider>
			</profile>
		</scannerConfigBuildInfo>
	</storageModule>
	<storageModule moduleId="org.eclipse.cdt.core.LanguageSettingsProviders"/>
</cproject>
