

#ifndef _APP_DISP_H
#define _APP_DISP_H 

#include "st7789v.h"
#include "stm32f4xx.h"  
#include <stdio.h>

#define DISP_DIR USE_HORIZONTAL

#if USE_HORIZONTAL == 2 || USE_HORIZONTAL == 3
#define LCD_W 				320
#define LCD_H 				170
#else
#define LCD_W 				170
#define LCD_H 				320
#endif


void app_disp_init(void);

void lcdClear(uint16_t color);

void lcdDrawLine(uint16_t x0, uint16_t y0, uint16_t x1, uint16_t y1, uint16_t color);

void lcdDrawRectangle(uint16_t x1, uint16_t y1, uint16_t x2, uint16_t y2, uint16_t color);

void lcdDisplayChar(uint16_t x, uint16_t y, uint8_t char_s, u8 size, uint8_t mode,uint16_t color);

void lcdDisPlayString(uint16_t x, uint16_t y, uint16_t width, uint16_t height, uint8_t *p, uint8_t size, uint8_t mode,uint16_t color);

#endif /* _APP_DISP_H */


