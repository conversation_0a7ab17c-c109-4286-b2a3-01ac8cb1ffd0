<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html xmlns:v="urn:schemas-microsoft-com:vml" xmlns:o="urn:schemas-microsoft-com:office:office" xmlns:w="urn:schemas-microsoft-com:office:word" xmlns="http://www.w3.org/TR/REC-html40"><head>





<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
<link rel="File-List" href="Library_files/filelist.xml">
<link rel="Edit-Time-Data" href="Library_files/editdata.mso"><!--[if !mso]> <style> v\:* {behavior:url(#default#VML);} o\:* {behavior:url(#default#VML);} w\:* {behavior:url(#default#VML);} .shape {behavior:url(#default#VML);} </style> <![endif]--><title>Release Notes for STM32F4xx Standard Peripherals Library Templates</title><!--[if gte mso 9]><xml> <o:DocumentProperties> <o:Author>STMicroelectronics</o:Author> <o:LastAuthor>STMicroelectronics</o:LastAuthor> <o:Revision>37</o:Revision> <o:TotalTime>136</o:TotalTime> <o:Created>2009-02-27T19:26:00Z</o:Created> <o:LastSaved>2009-03-01T17:56:00Z</o:LastSaved> <o:Pages>1</o:Pages> <o:Words>522</o:Words> <o:Characters>2977</o:Characters> <o:Company>STMicroelectronics</o:Company> <o:Lines>24</o:Lines> <o:Paragraphs>6</o:Paragraphs> <o:CharactersWithSpaces>3493</o:CharactersWithSpaces> <o:Version>11.6568</o:Version> </o:DocumentProperties> </xml><![endif]--><!--[if gte mso 9]><xml> <w:WordDocument> <w:Zoom>110</w:Zoom> <w:ValidateAgainstSchemas/> <w:SaveIfXMLInvalid>false</w:SaveIfXMLInvalid> <w:IgnoreMixedContent>false</w:IgnoreMixedContent> <w:AlwaysShowPlaceholderText>false</w:AlwaysShowPlaceholderText> <w:BrowserLevel>MicrosoftInternetExplorer4</w:BrowserLevel> </w:WordDocument> </xml><![endif]--><!--[if gte mso 9]><xml> <w:LatentStyles DefLockedState="false" LatentStyleCount="156"> </w:LatentStyles> </xml><![endif]-->



<style>
<!--
/* Style Definitions */
p.MsoNormal, li.MsoNormal, div.MsoNormal
{mso-style-parent:"";
margin:0in;
margin-bottom:.0001pt;
mso-pagination:widow-orphan;
font-size:12.0pt;
font-family:"Times New Roman";
mso-fareast-font-family:"Times New Roman";}
h2
{mso-style-next:Normal;
margin-top:12.0pt;
margin-right:0in;
margin-bottom:3.0pt;
margin-left:0in;
mso-pagination:widow-orphan;
page-break-after:avoid;
mso-outline-level:2;
font-size:14.0pt;
font-family:Arial;
font-weight:bold;
font-style:italic;}
a:link, span.MsoHyperlink
{color:blue;
text-decoration:underline;
text-underline:single;}
a:visited, span.MsoHyperlinkFollowed
{color:blue;
text-decoration:underline;
text-underline:single;}
p
{mso-margin-top-alt:auto;
margin-right:0in;
mso-margin-bottom-alt:auto;
margin-left:0in;
mso-pagination:widow-orphan;
font-size:12.0pt;
font-family:"Times New Roman";
mso-fareast-font-family:"Times New Roman";}
@page Section1
{size:8.5in 11.0in;
margin:1.0in 1.25in 1.0in 1.25in;
mso-header-margin:.5in;
mso-footer-margin:.5in;
mso-paper-source:0;}
div.Section1
{page:Section1;}
-->
</style><!--[if gte mso 10]> <style> /* Style Definitions */ table.MsoNormalTable {mso-style-name:"Table Normal"; mso-tstyle-rowband-size:0; mso-tstyle-colband-size:0; mso-style-noshow:yes; mso-style-parent:""; mso-padding-alt:0in 5.4pt 0in 5.4pt; mso-para-margin:0in; mso-para-margin-bottom:.0001pt; mso-pagination:widow-orphan; font-size:10.0pt; font-family:"Times New Roman"; mso-ansi-language:#0400; mso-fareast-language:#0400; mso-bidi-language:#0400;} </style> <![endif]--><!--[if gte mso 9]><xml> <o:shapedefaults v:ext="edit" spidmax="5122"/> </xml><![endif]--><!--[if gte mso 9]><xml> <o:shapelayout v:ext="edit"> <o:idmap v:ext="edit" data="1"/> </o:shapelayout></xml><![endif]--></head><body style="" lang="EN-US" link="blue" vlink="blue">
<div class="Section1">
<p class="MsoNormal"><span style="font-family: Arial;"><o:p><br>
</o:p></span></p>
<div align="center">
<table class="MsoNormalTable" style="width: 675pt;" border="0" cellpadding="0" cellspacing="0" width="900">
<tbody>
<tr style="">
<td style="padding: 0cm;" valign="top">
<table class="MsoNormalTable" style="width: 675pt;" border="0" cellpadding="0" cellspacing="0" width="900">
<tbody>
          <tr>
            <td style="vertical-align: top;"><span style="font-size: 8pt; font-family: Arial; color: blue;"><a href="../../Release_Notes.html">Back to Release page</a></span></td>
          </tr>
<tr style="">
<td style="padding: 1.5pt;">
<h1 style="margin-bottom: 18pt; text-align: center;" align="center"><span style="font-size: 20pt; font-family: Verdana; color: rgb(51, 102, 255);">Release
Notes for STM32F4xx Standard Peripherals Library Templates</span><span style="font-size: 20pt; font-family: Verdana;"><o:p></o:p></span></h1>
<p class="MsoNormal" style="text-align: center;" align="center"><span style="font-size: 10pt; font-family: Arial; color: black;">Copyright 2016 STMicroelectronics</span><span style="color: black;"><u1:p></u1:p><o:p></o:p></span></p>
<p class="MsoNormal" style="text-align: center;" align="center"><span style="font-size: 10pt; font-family: Arial; color: black;"><img alt="" id="_x0000_i1025" src="../../_htmresc/logo.bmp" style="border: 0px solid ; width: 86px; height: 65px;"></span><span style="font-size: 10pt;"><o:p></o:p></span></p>
</td>
</tr>
</tbody>
</table>
<p class="MsoNormal"><span style="font-family: Arial; display: none;"><o:p>&nbsp;</o:p></span></p>
<table class="MsoNormalTable" style="width: 675pt;" border="0" cellpadding="0" width="900">
<tbody>
<tr style="">
<td style="padding: 0cm;" valign="top">
<h2 style="background: rgb(51, 102, 255) none repeat scroll 0% 50%; -moz-background-clip: -moz-initial; -moz-background-origin: -moz-initial; -moz-background-inline-policy: -moz-initial;"><span style="font-size: 12pt; color: white;">Contents<o:p></o:p></span></h2>
            <span style="font-size: 10pt; font-family: Verdana;"><a href="#History">STM32F4xx&nbsp;Standard Peripherals Library
Templates
update History</a><o:p></o:p></span><br>
            <span style="font-size: 10pt; font-family: Verdana;"><o:p></o:p></span><ol style="margin-top: 0cm;" start="1" type="1">


</ol>
<span style="font-family: &quot;Times New Roman&quot;;">
</span>
<h2 style="background: rgb(51, 102, 255) none repeat scroll 0% 50%; -moz-background-clip: -moz-initial; -moz-background-origin: -moz-initial; -moz-background-inline-policy: -moz-initial;"><a name="History"></a><span style="font-size: 12pt; color: white;">STM32F4xx Standard
Peripherals Library Templates update History</span></h2>
            <h3 style="background: rgb(51, 102, 255) none repeat scroll 0% 50%; -moz-background-clip: -moz-initial; -moz-background-origin: -moz-initial; -moz-background-inline-policy: -moz-initial; margin-right: 500pt; width: 181px;"><span style="font-size: 10pt; color: white; font-family: Arial;">V1.8.1 / 27-January-2022</span></h3>


            
            <p class="MsoNormal" style="margin: 4.5pt 0cm 4.5pt 18pt;"><b><u><span style="font-size: 10pt; color: black; font-family: Verdana;">Main 
Changes<o:p></o:p></span></u></b>

            <span style="font-size: 10pt; font-family: Verdana;"><br>
            </span></p>

            
            <ul>
              <li><span style="font-size: 10pt; font-family: Verdana;">All source files: update disclaimer to add reference to the&nbsp;new license agreement.</span></li>
            </ul>
            <h3 style="background: rgb(51, 102, 255) none repeat scroll 0% 50%; -moz-background-clip: -moz-initial; -moz-background-origin: -moz-initial; -moz-background-inline-policy: -moz-initial; margin-right: 500pt; width: 181px;"><span style="font-size: 10pt; color: white; font-family: Arial;">V1.8.0 / 09-November-2016</span></h3>
<span style="font-size: 10pt; font-family: Verdana;"></span><p class="MsoNormal" style="margin: 4.5pt 0cm 4.5pt 18pt;"><b><u><span style="font-size: 10pt; color: black; font-family: Verdana;">Main 
Changes<o:p></o:p></span></u></b></p>
<ul style="margin-top: 0cm;" type="square"><li class="MsoNormal" style="margin: 4.5pt 0in; font-size: 12pt; font-family: 'Times New Roman'; color: black;"><p class="MsoNormal" style="margin: 0in 0in 0.0001pt; font-size: 12pt; font-family: 'Times New Roman';"><span style="text-align: left; widows: 2; text-transform: none; text-indent: 0px; letter-spacing: normal; display: inline ! important; font-family: Verdana; font-style: normal; font-variant: normal; font-weight: normal; font-size: 13px; line-height: normal; font-size-adjust: none; font-stretch: normal; white-space: normal; orphans: 2; float: none; color: rgb(0, 0, 0); word-spacing: 0px;">Update 
project templates for all the supported toolchains to add the support of <span style="font-weight: bold;">STM32F413/</span><span style="font-weight: bold;">423xx</span><span style="font-weight: bold;"> </span></span><span style="font-size: 10pt; font-family: Verdana,sans-serif;"><span class="Apple-converted-space"></span>devices</span></p></li><li class="MsoNormal" style="margin: 4.5pt 0in; font-size: 12pt; font-family: 'Times New Roman'; color: black;"><b><u><span style="font-size: 10pt; color: black; font-family: Verdana;">Development 
Toolchains<o:p></o:p></span></u></b><br><span style="font-size: 10pt; font-family: Verdana;"></span>
</li><ul><li class="MsoNormal"><span style="font-size: 10pt; font-family: Verdana;">IAR 
Embedded Workbench for ARM (EWARM)&nbsp;toolchain</span></li></ul><ul><ul><li class="MsoNormal"><span style="font-size: 10pt; font-family: Verdana;"><span style="text-decoration: underline;">Used version:</span> <span style="font-style: italic;">v7.70.1&nbsp;</span></span><span style="color: rgb(0, 0, 0); font-family: Verdana; font-size: 13.3333px; font-style: normal; font-variant: normal; font-weight: normal; letter-spacing: normal; line-height: normal; text-align: left; text-indent: 0px; text-transform: none; white-space: normal; widows: 1; word-spacing: 0px; display: inline ! important; float: none;"><span class="Apple-converted-space"></span>+ ST-Link</span></li></ul></ul><ul><li class="MsoNormal"><span style="font-size: 10pt; font-family: Verdana;">RealView 
Microcontroller Development Kit (MDK-ARM)&nbsp;toolchain</span></li></ul><ul><ul><li class="MsoNormal"><span style="font-size: 10pt; font-family: Verdana;"><span style="text-decoration: underline;">Used version:</span> <span style="font-style: italic;">v</span></span><span style="font-size: 10pt; font-style: italic; font-family: Verdana;">5.21a</span><span style="color: rgb(0, 0, 0); font-family: Verdana; font-size: 13.3333px; font-style: normal; font-variant: normal; font-weight: normal; letter-spacing: normal; line-height: normal; text-align: left; text-indent: 0px; text-transform: none; white-space: normal; widows: 1; word-spacing: 0px; display: inline ! important; float: none;"><span class="Apple-converted-space"> </span>+ ST-Link</span></li></ul></ul><ul><li class="MsoNormal"><span style="font-size: 10pt; font-family: Verdana;">STM32 
TrueSTUDIO software (</span><span style="font-size: 10pt; font-family: Verdana;">TrueSTUDIO</span><span style="font-size: 10pt; font-family: Verdana;">)&nbsp;toolchain</span><span style="font-size: 10pt; font-family: Verdana;"><span style="text-decoration: underline;"></span></span> 
<ul><li class="MsoNormal"><span style="font-size: 10pt; font-family: Verdana;"><span style="text-decoration: underline;">Used version:</span> <span style="font-style: italic;">v</span></span><span style="font-size: 10pt; font-style: italic; font-family: Verdana;">7.0.0</span><span style="color: rgb(0, 0, 0); font-family: Verdana; font-size: 13.3333px; font-style: normal; font-variant: normal; font-weight: normal; letter-spacing: normal; line-height: normal; text-align: left; text-indent: 0px; text-transform: none; white-space: normal; widows: 1; word-spacing: 0px; display: inline ! important; float: none;"><span class="Apple-converted-space">&nbsp;</span>+ ST-Link</span></li></ul></li><li class="MsoNormal"><span style="font-size: 10pt; font-family: Verdana;">System Workbench for STM32 (SW4STM32) toolchain</span></li><ul><li class="MsoNormal"><span style="font-size: 10pt; font-family: Verdana;"><span style="text-decoration: underline;">Used version:</span> <span style="font-style: italic;">v</span></span><span style="font-size: 10pt; font-style: italic; font-family: Verdana;">1.12.0 + ST-Link</span></li></ul></ul></ul><h3 style="background: rgb(51, 102, 255) none repeat scroll 0% 50%; -moz-background-clip: -moz-initial; -moz-background-origin: -moz-initial; -moz-background-inline-policy: -moz-initial; margin-right: 500pt; width: 181px;"><span style="font-size: 10pt; color: white; font-family: Arial;">V1.7.1 / 20-May-2016</span></h3><p class="MsoNormal" style="margin: 4.5pt 0cm 4.5pt 18pt;"><b><u><span style="font-size: 10pt; color: black; font-family: Verdana;">Main 
Changes<o:p></o:p></span></u></b></p>
<ul style="margin-top: 0cm;" type="square"><li class="MsoNormal" style="margin: 4.5pt 0in; font-size: 12pt; font-family: 'Times New Roman'; color: black;"><p class="MsoNormal" style="margin: 0in 0in 0.0001pt; font-size: 12pt; font-family: 'Times New Roman';"><span style="font-size: 10pt; font-family: Verdana; font-weight: normal;">Update&nbsp;to enable "require prototype" feature for EWARM template project.</span><span style="font-size: 10pt; font-family: Verdana,sans-serif;"></span></p></li></ul><h3 style="background: rgb(51, 102, 255) none repeat scroll 0% 50%; -moz-background-clip: -moz-initial; -moz-background-origin: -moz-initial; -moz-background-inline-policy: -moz-initial; margin-right: 500pt; width: 181px;"><span style="font-size: 10pt; color: white; font-family: Arial;">V1.7.0 / 22-April-2016</span></h3><span style="font-size: 10pt; font-family: Verdana;"></span><p class="MsoNormal" style="margin: 4.5pt 0cm 4.5pt 18pt;"><b><u><span style="font-size: 10pt; color: black; font-family: Verdana;">Main 
Changes<o:p></o:p></span></u></b></p>
<ul style="margin-top: 0cm;" type="square"><li class="MsoNormal" style="margin: 4.5pt 0in; font-size: 12pt; font-family: 'Times New Roman'; color: black;"><p class="MsoNormal" style="margin: 0in 0in 0.0001pt; font-size: 12pt; font-family: 'Times New Roman';"><span style="text-align: left; widows: 2; text-transform: none; text-indent: 0px; letter-spacing: normal; display: inline ! important; font-family: Verdana; font-style: normal; font-variant: normal; font-weight: normal; font-size: 13px; line-height: normal; font-size-adjust: none; font-stretch: normal; white-space: normal; orphans: 2; float: none; color: rgb(0, 0, 0); word-spacing: 0px;">Update 
project templates for all the supported toolchains to add the support of <span style="font-weight: bold;">STM32F412xG</span><span style="font-weight: bold;"> </span></span><span style="font-size: 10pt; font-family: Verdana,sans-serif;"><span class="Apple-converted-space"></span>devices</span></p></li><li class="MsoNormal" style="margin: 4.5pt 0in; font-size: 12pt; font-family: 'Times New Roman'; color: black;"><b><u><span style="font-size: 10pt; color: black; font-family: Verdana;">Development 
Toolchains<o:p></o:p></span></u></b><br><span style="font-size: 10pt; font-family: Verdana;"></span>
</li><ul><li class="MsoNormal"><span style="font-size: 10pt; font-family: Verdana;">IAR 
Embedded Workbench for ARM (EWARM)&nbsp;toolchain</span></li></ul><ul><ul><li class="MsoNormal"><span style="font-size: 10pt; font-family: Verdana;"><span style="text-decoration: underline;">Used version:</span> <span style="font-style: italic;">v7.60.1&nbsp;</span></span><span style="color: rgb(0, 0, 0); font-family: Verdana; font-size: 13.3333px; font-style: normal; font-variant: normal; font-weight: normal; letter-spacing: normal; line-height: normal; text-align: left; text-indent: 0px; text-transform: none; white-space: normal; widows: 1; word-spacing: 0px; display: inline ! important; float: none;"><span class="Apple-converted-space"></span>+ ST-Link</span></li></ul></ul><ul><li class="MsoNormal"><span style="font-size: 10pt; font-family: Verdana;">RealView 
Microcontroller Development Kit (MDK-ARM)&nbsp;toolchain</span></li></ul><ul><ul><li class="MsoNormal"><span style="font-size: 10pt; font-family: Verdana;"><span style="text-decoration: underline;">Used version:</span> <span style="font-style: italic;">v</span></span><span style="font-size: 10pt; font-style: italic; font-family: Verdana;">5.17</span><span style="color: rgb(0, 0, 0); font-family: Verdana; font-size: 13.3333px; font-style: normal; font-variant: normal; font-weight: normal; letter-spacing: normal; line-height: normal; text-align: left; text-indent: 0px; text-transform: none; white-space: normal; widows: 1; word-spacing: 0px; display: inline ! important; float: none;"><span class="Apple-converted-space">&nbsp;</span>+ ST-Link</span></li></ul></ul><ul><li class="MsoNormal"><span style="font-size: 10pt; font-family: Verdana;">STM32 
TrueSTUDIO software (</span><span style="font-size: 10pt; font-family: Verdana;">TrueSTUDIO</span><span style="font-size: 10pt; font-family: Verdana;">)&nbsp;toolchain</span><span style="font-size: 10pt; font-family: Verdana;"><span style="text-decoration: underline;"></span></span> 
<ul><li class="MsoNormal"><span style="font-size: 10pt; font-family: Verdana;"><span style="text-decoration: underline;">Used version:</span> <span style="font-style: italic;">v</span></span><span style="font-size: 10pt; font-style: italic; font-family: Verdana;">5.5.0</span><span style="color: rgb(0, 0, 0); font-family: Verdana; font-size: 13.3333px; font-style: normal; font-variant: normal; font-weight: normal; letter-spacing: normal; line-height: normal; text-align: left; text-indent: 0px; text-transform: none; white-space: normal; widows: 1; word-spacing: 0px; display: inline ! important; float: none;"><span class="Apple-converted-space">&nbsp;</span>+ ST-Link</span></li></ul></li><li class="MsoNormal"><span style="font-size: 10pt; font-family: Verdana;">System Workbench for STM32 (SW4STM32) toolchain</span></li><ul><li class="MsoNormal"><span style="font-size: 10pt; font-family: Verdana;"><span style="text-decoration: underline;">Used version:</span> <span style="font-style: italic;">v</span></span><span style="font-size: 10pt; font-style: italic; font-family: Verdana;">1.8.0 + ST-Link</span></li></ul></ul></ul><h3 style="background: rgb(51, 102, 255) none repeat scroll 0% 50%; -moz-background-clip: -moz-initial; -moz-background-origin: -moz-initial; -moz-background-inline-policy: -moz-initial; margin-right: 500pt; width: 181px;"><span style="font-size: 10pt; color: white; font-family: Arial;">V1.6.0 / 04-September-2015</span></h3><span style="font-size: 10pt; font-family: Verdana;"></span><p class="MsoNormal" style="margin: 4.5pt 0cm 4.5pt 18pt;"><b><u><span style="font-size: 10pt; color: black; font-family: Verdana;">Main 
Changes<o:p></o:p></span></u></b></p>
<ul style="margin-top: 0cm;" type="square"><li class="MsoNormal" style="margin: 4.5pt 0in; font-size: 12pt; font-family: 'Times New Roman'; color: black;"><p class="MsoNormal" style="margin: 0in 0in 0.0001pt; font-size: 12pt; font-family: 'Times New Roman';"><span style="text-align: left; widows: 2; text-transform: none; text-indent: 0px; letter-spacing: normal; display: inline ! important; font-family: Verdana; font-style: normal; font-variant: normal; font-weight: normal; font-size: 13px; line-height: normal; font-size-adjust: none; font-stretch: normal; white-space: normal; orphans: 2; float: none; color: rgb(0, 0, 0); word-spacing: 0px;">Update 
project templates for all the supported toolchains to add the support of <span style="font-weight: bold;">STM32F410xx</span>,<span style="font-weight: bold;"> STM32F469xx </span>and<span style="font-weight: bold;"> STM32F479xx&nbsp;</span></span><span style="font-size: 10pt; font-family: Verdana,sans-serif;"><span class="Apple-converted-space"></span>devices</span></p></li><li class="MsoNormal" style="margin: 4.5pt 0in; font-size: 12pt; font-family: 'Times New Roman'; color: black;"><span style="color: rgb(0, 0, 0); font-style: normal; font-variant: normal; font-weight: normal; letter-spacing: normal; line-height: normal; text-align: left; text-indent: 0px; text-transform: none; white-space: normal; widows: 1; word-spacing: 0px; font-size: 10pt; font-family: Verdana;"><span style="font-weight: normal;">Add the support<span class="Apple-converted-space">&nbsp;</span></span></span><span style="color: rgb(0, 0, 0); font-style: normal; font-variant: normal; font-weight: normal; letter-spacing: normal; line-height: normal; text-align: left; text-indent: 0px; text-transform: none; white-space: normal; widows: 1; word-spacing: 0px; font-size: 10pt; font-family: Verdana,sans-serif;">System Workbench for STM32 (SW4STM32) toolchain</span></li><li class="MsoNormal" style="margin: 4.5pt 0in; font-size: 12pt; font-family: 'Times New Roman'; color: black;"><b><u><span style="font-size: 10pt; color: black; font-family: Verdana;">Development 
Toolchains<o:p></o:p></span></u></b><br><span style="font-size: 10pt; font-family: Verdana;"></span>
</li></ul><ul><ul><li class="MsoNormal"><span style="font-size: 10pt; font-family: Verdana;">IAR 
Embedded Workbench for ARM (EWARM)&nbsp;toolchain</span></li></ul><ul><ul><li class="MsoNormal"><span style="font-size: 10pt; font-family: Verdana;"><span style="text-decoration: underline;">Used version:</span> <span style="font-style: italic;">v7.40.1</span></span></li></ul></ul><ul><li class="MsoNormal"><span style="font-size: 10pt; font-family: Verdana;">RealView 
Microcontroller Development Kit (MDK-ARM)&nbsp;toolchain</span></li></ul><ul><ul><li class="MsoNormal"><span style="font-size: 10pt; font-family: Verdana;"><span style="text-decoration: underline;">Used version:</span> <span style="font-style: italic;">v</span></span><span style="font-size: 10pt; font-style: italic; font-family: Verdana;">5.14</span></li></ul></ul><ul><li class="MsoNormal"><span style="font-size: 10pt; font-family: Verdana;">STM32 
TrueSTUDIO software (</span><span style="font-size: 10pt; font-family: Verdana;">TrueSTUDIO</span><span style="font-size: 10pt; font-family: Verdana;">)&nbsp;toolchain</span><span style="font-size: 10pt; font-family: Verdana;"><span style="text-decoration: underline;"></span></span> 
<ul><li class="MsoNormal"><span style="font-size: 10pt; font-family: Verdana;"><span style="text-decoration: underline;">Used version:</span> <span style="font-style: italic;">v</span></span><span style="font-size: 10pt; font-style: italic; font-family: Verdana;">5.3.0</span></li></ul></li><li class="MsoNormal" style="margin: 4.5pt 0in; font-size: 12pt; font-family: 'Times New Roman'; color: black;"><span style="font-size: 10pt; font-family: Verdana;">System Workbench for STM32 (SW4STM32) toolchain</span></li><ul><li class="MsoNormal"><span style="font-size: 10pt; font-family: Verdana;"><span style="text-decoration: underline;">Used version:</span> <span style="font-style: italic;">v</span></span><span style="font-size: 10pt; font-style: italic; font-family: Verdana;">1.1.0 + ST-Link</span></li></ul></ul></ul><span style="font-size: 10pt; font-family: Verdana;"></span><h3 style="background: rgb(51, 102, 255) none repeat scroll 0% 50%; -moz-background-clip: -moz-initial; -moz-background-origin: -moz-initial; -moz-background-inline-policy: -moz-initial; margin-right: 500pt; width: 181px;"><span style="font-size: 10pt; color: white; font-family: Arial;">V1.5.0 / 06-March-2015</span></h3><span style="font-size: 10pt; font-family: Verdana;"></span><b><u><span style="font-size: 10pt; color: black; font-family: Verdana;">Main 
Changes</span></u></b>
<ul style="margin-top: 0cm;" type="square"><li class="MsoNormal" style="margin-top: 4.5pt; margin-bottom: 4.5pt; color: black;"><span style="font-size: 10pt; font-family: &quot;Verdana&quot;,&quot;sans-serif&quot;;">Add
support of <span style="font-weight: bold;">STM32F446xx</span> devices</span></li><li class="MsoNormal" style="margin-top: 4.5pt; margin-bottom: 4.5pt; color: black;">

<p class="MsoNormal"><span style="font-size: 10pt; font-family: &quot;Verdana&quot;,&quot;sans-serif&quot;;">Add
STM32F446xx Workspace for STM32F446xx devices</span></p></li></ul><ul style="margin-top: 0cm;" type="square"><li class="MsoNormal"><span style="font-size: 10pt; font-family: Verdana;"><span style="font-weight: bold; font-style: italic; text-decoration: underline;">Important 
Note</span><span style="font-weight: bold;"> <br></span></span>
<ul><li class="MsoNormal"><span style="font-size: 10pt; font-family: Verdana;"><span style="font-weight: bold;"></span>The example provided within this&nbsp;version 
assumes that the HSE </span><span style="font-size: 10pt; font-family: Verdana;">value is set to 25 MHz on all Eval-boards except for STM32F446E_EVAL board which is 8MHz. If you 
have a different HSE value, you have to adapt the Library as follows:</span> 
</li></ul>
<ul><ul><li class="MsoNormal" style="margin-top: 4.5pt; margin-bottom: 4.5pt; color: black;"><span style="font-size: 10pt; font-family: Verdana;"><span style="font-style: italic;">stm32f4xx.h</span> file (under 
Libraries\CMSIS\Device\ST\STM32F4xx\Include): at line129, adapt the HSE value 
(HSE_VALUE) to your own configuration.</span></li></ul></ul></li></ul><h3 style="background: rgb(51, 102, 255) none repeat scroll 0% 50%; -moz-background-clip: -moz-initial; -moz-background-origin: -moz-initial; -moz-background-inline-policy: -moz-initial; width: 197px; margin-right: 500pt;"><span style="font-size: 10pt; color: white; font-family: Arial;">V1.4.0 / 04-August-2014</span></h3><span style="font-size: 10pt; font-family: Verdana;"></span><p class="MsoNormal" style="margin: 4.5pt 0cm 4.5pt 18pt;"><b><u><span style="font-size: 10pt; color: black; font-family: Verdana;">Main 
Changes<o:p></o:p></span></u></b></p><ul style="margin-top: 0cm;" type="square"><li style="margin-top: 4.5pt; margin-bottom: 4.5pt; color: black;" class="MsoNormal"><span style="text-align: left; widows: 2; text-transform: none; text-indent: 0px; letter-spacing: normal; display: inline ! important; font-family: Verdana; font-style: normal; font-variant: normal; font-weight: normal; font-size: 13px; line-height: normal; font-size-adjust: none; font-stretch: normal; white-space: normal; orphans: 2; float: none; color: rgb(0, 0, 0); word-spacing: 0px;">Update 
project templates for all the supported toolchains to add the support of 
the <span class="Apple-converted-space"></span></span><span style="text-align: left; widows: 2; text-transform: none; text-indent: 0px; letter-spacing: normal; font-family: Verdana; font-style: italic; font-variant: normal; font-weight: bold; font-size: 13px; line-height: normal; font-size-adjust: none; font-stretch: normal; white-space: normal; orphans: 2; color: rgb(0, 0, 0); word-spacing: 0px;">STM32F411xE 
devices.</span></li><li style="margin-top: 4.5pt; margin-bottom: 4.5pt; color: black;" class="MsoNormal"><span style="font-family: 'Verdana','sans-serif'; font-size: 10pt;">Remove 
references to RIDE and TASKING Toolchaines</span>
</li><ul><li><span style="font-family: 'Verdana','sans-serif'; font-size: 10pt;">RIDE and 
TASKING will not be supported by the STM32F4xx Standard Library</span><span style="font-family: 'Verdana','sans-serif'; font-size: 10pt;"> firmware package</span></li></ul></ul><h3 style="background: rgb(51, 102, 255) none repeat scroll 0% 50%; -moz-background-clip: -moz-initial; -moz-background-origin: -moz-initial; -moz-background-inline-policy: -moz-initial; width: 197px; margin-right: 500pt;"><span style="font-size: 10pt; color: white; font-family: Arial;">V1.3.0 / 
13-November-2013</span></h3><span style="font-size: 10pt; font-family: Verdana;"></span><p class="MsoNormal" style="margin: 4.5pt 0cm 4.5pt 18pt;"><b><u><span style="font-size: 10pt; color: black; font-family: Verdana;">Main 
Changes<o:p></o:p></span></u></b></p>
<ul style="margin-top: 0cm;" type="square"><li class="MsoNormal" style="margin-top: 4.5pt; margin-bottom: 4.5pt; color: black;"><span style="font-size: 10pt; font-family: &quot;Verdana&quot;,&quot;sans-serif&quot;;">Update&nbsp;</span><span style="font-size: 10pt; font-family: &quot;Verdana&quot;,&quot;sans-serif&quot;;">STM32F4xx
Device name used in EWARM and MDK-ARM projects</span></li></ul>
<span style="font-size: 10pt; font-family: Verdana;"><span style="font-weight: bold; font-style: italic; text-decoration: underline;">Important 
Note</span><span style="font-weight: bold;"> <br></span></span>
<ul><li class="MsoNormal"><span style="font-size: 10pt; font-family: Verdana;"><span style="font-weight: bold;"></span>The examples provided within this&nbsp;version 
assumes that the HSE </span><span style="font-size: 10pt; font-family: Verdana;">value is set to 25 MHz. If you 
have a different HSE value, you have to adapt the Library as follows:</span> 
</li></ul>
<span style="font-size: 10pt; font-family: Verdana;"><span style="font-style: italic;">stm32f4xx.h</span> file (under 
Libraries\CMSIS\Device\ST\STM32F4xx\Include):&nbsp;adapt the HSE value 
(HSE_VALUE) to your own configuration.</span><h3 style="background: rgb(51, 102, 255) none repeat scroll 0% 50%; -moz-background-clip: -moz-initial; -moz-background-origin: -moz-initial; -moz-background-inline-policy: -moz-initial; width: 197px; margin-right: 500pt;"><span style="font-size: 10pt; color: white; font-family: Arial;">V1.2.0 / 
19-September-2013</span></h3>
<p class="MsoNormal" style="margin: 4.5pt 0cm 4.5pt 18pt;"><b><u><span style="font-size: 10pt; color: black; font-family: Verdana;">Main 
Changes<o:p></o:p></span></u></b></p>
<ul style="margin-top: 0cm;" type="square"><li class="MsoNormal" style="margin-top: 4.5pt; margin-bottom: 4.5pt; color: black;"><span style="font-size: 10pt; font-family: &quot;Verdana&quot;,&quot;sans-serif&quot;;">Add
support of <span style="font-weight: bold;">STM32F429/439xx</span> and <span style="font-weight: bold;">STM32F401xCxx</span> devices</span></li><li class="MsoNormal" style="margin-top: 4.5pt; margin-bottom: 4.5pt; color: black;">

<p class="MsoNormal"><span style="font-size: 10pt; font-family: &quot;Verdana&quot;,&quot;sans-serif&quot;;">Add
STM32F429_439xx Workspace for STM32F429/439xx devices</span></p></li><li class="MsoNormal" style="margin-top: 4.5pt; margin-bottom: 4.5pt; color: black;">

<p class="MsoNormal"><span style="font-size: 10pt; font-family: &quot;Verdana&quot;,&quot;sans-serif&quot;;">Add
STM32F401xx Workspace for STM32F401Cxxx devices</span></p></li><li class="MsoNormal" style="margin-top: 4.5pt; margin-bottom: 4.5pt; color: black;"><span style="font-size: 10pt; font-family: &quot;Verdana&quot;,&quot;sans-serif&quot;;">Rename STM324xG-EVAL worksapce
by STM32F40_41xxx and update project setting (Device part number, Preprocessor
Defines, Linker, Output)</span></li><li class="MsoNormal" style="margin-top: 4.5pt; margin-bottom: 4.5pt; color: black;">

<p class="MsoNormal"><span style="font-size: 10pt; font-family: &quot;Verdana&quot;,&quot;sans-serif&quot;;">Rename
STM324x7I-EVAL project by STM32F427_437xx Project and update project setting (Device
part number, Preprocessor Defines, Linker, Output)</span></p></li><li class="MsoNormal" style="margin-top: 4.5pt; margin-bottom: 4.5pt; color: black;"><span style="font-size: 10pt; font-family: &quot;Verdana&quot;,&quot;sans-serif&quot;;">Update the template project &nbsp;: no specific configuration related to Eval board only systick configuration is
kept</span></li><li class="MsoNormal"><b><u><span style="font-size: 10pt; color: black; font-family: Verdana;">Development 
Toolchains<o:p></o:p></span></u></b><br><span style="font-size: 10pt; font-family: Verdana;"></span>
<ul><li class="MsoNormal"><span style="font-size: 10pt; font-family: Verdana;">IAR 
Embedded Workbench for ARM (EWARM)&nbsp;toolchain</span><span style="font-size: 10pt; font-family: Verdana;"><span style="text-decoration: underline;"></span></span> </li></ul>
<ul><ul><li class="MsoNormal"><span style="font-size: 10pt; font-family: Verdana;"><span style="text-decoration: underline;">Used version:</span> <span style="font-style: italic;">v6.60.2</span></span><br></li></ul></ul>
<ul><li class="MsoNormal"><span style="font-size: 10pt; font-family: Verdana;">RealView 
Microcontroller Development Kit (MDK-ARM)&nbsp;toolchain</span><span style="font-size: 10pt; font-family: Verdana;"><span style="text-decoration: underline;"></span></span> </li></ul>
<ul><ul><li class="MsoNormal"><span style="font-size: 10pt; font-family: Verdana;"><span style="text-decoration: underline;">Used version:</span> <span style="font-style: italic;">v</span></span><span style="font-size: 10pt; font-style: italic; font-family: Verdana;">4.73</span><span style="font-size: 10pt; font-family: Verdana;"><span style="font-style: italic;"></span></span><span style="font-size: 10pt; font-family: Verdana;"></span></li></ul><li class="MsoNormal"><span style="font-size: 10pt; font-family: Verdana;">STM32 
TrueSTUDIO software (</span><span style="font-size: 10pt; font-family: Verdana;">TrueSTUDIO</span><span style="font-size: 10pt; font-family: Verdana;">)&nbsp;toolchain</span><span style="font-size: 10pt; font-family: Verdana;"><span style="text-decoration: underline;"></span></span> 
<ul><li class="MsoNormal"><span style="font-size: 10pt; font-family: Verdana;"><span style="text-decoration: underline;">Used version:</span> <span style="font-style: italic;">v</span></span><span style="font-size: 10pt; font-style: italic; font-family: Verdana;">4.2.0</span><span style="font-size: 10pt; font-family: Verdana;"><span style="font-style: italic;"></span></span> </li></ul>
</li><li class="MsoNormal"><span style="font-size: 10pt; font-family: Verdana;">Raisonance RIDE7 software 
(RIDE)&nbsp;toolchain</span><span style="font-size: 10pt; font-family: Verdana;"><span style="text-decoration: underline;"></span></span> 
<ul><li class="MsoNormal"><span style="font-size: 10pt; font-family: Verdana;"><span style="text-decoration: underline;">Used version:</span> <span style="font-style: italic;">RIDE7 IDE:7.48, RKitARM for 
RIDE7:1.52</span></span><span style="font-size: 10pt; font-style: italic; font-family: Verdana;"></span> 
</li></ul></li></ul>
</li></ul>
<ul style="margin-top: 0cm;" type="square"><li class="MsoNormal"><span style="font-size: 10pt; font-family: Verdana;"><span style="font-weight: bold; font-style: italic; text-decoration: underline;">Important 
Note</span><span style="font-weight: bold;"> <br></span></span>
<ul><li class="MsoNormal"><span style="font-size: 10pt; font-family: Verdana;"><span style="font-weight: bold;"></span>The example provided within this&nbsp;version 
assumes that the HSE </span><span style="font-size: 10pt; font-family: Verdana;">value is set to 25 MHz. If you 
have a different HSE value, you have to adapt the Library as follows:</span> 
</li></ul>
<ul><ul><li class="MsoNormal" style="margin-top: 4.5pt; margin-bottom: 4.5pt; color: black;"><span style="font-size: 10pt; font-family: Verdana;"><span style="font-style: italic;">stm32f4xx.h</span> file (under 
Libraries\CMSIS\Device\ST\STM32F4xx\Include): at line103, adapt the HSE value 
(HSE_VALUE) to your own configuration.<span style="font-weight: bold;"></span></span></li></ul></ul></li></ul><h3 style="background: rgb(51, 102, 255) none repeat scroll 0% 50%; -moz-background-clip: -moz-initial; -moz-background-origin: -moz-initial; -moz-background-inline-policy: -moz-initial; width: 197px; margin-right: 500pt;"><span style="font-size: 10pt; color: white; font-family: Arial;">V1.1.0 / 
18-January-2013</span></h3>
<p class="MsoNormal" style="margin: 4.5pt 0cm 4.5pt 18pt;"><b><u><span style="font-size: 10pt; color: black; font-family: Verdana;">Main 
Changes<o:p></o:p></span></u></b></p>
<ul style="margin-top: 0cm;" type="square"><li class="MsoNormal" style="margin-top: 4.5pt; margin-bottom: 4.5pt; color: black;"><span style="font-size: 10pt; font-family: Verdana;">Official release </span><span style="font-size: 10pt; font-family: Verdana;">for <span style="font-weight: bold;">STM32F427x/437x</span> devices.</span><span style="font-size: 10pt; font-family: Verdana;"></span> 
</li><li class="MsoNormal"><b><u><span style="font-size: 10pt; color: black; font-family: Verdana;">Development 
Toolchains<o:p></o:p></span></u></b><br><span style="font-size: 10pt; font-family: Verdana;"></span>
<ul><li class="MsoNormal"><span style="font-size: 10pt; font-family: Verdana;">IAR 
Embedded Workbench for ARM (EWARM)&nbsp;toolchain</span><span style="font-size: 10pt; font-family: Verdana;"><span style="text-decoration: underline;"></span></span> </li></ul>
<ul><ul><li class="MsoNormal"><span style="font-size: 10pt; font-family: Verdana;"><span style="text-decoration: underline;">Used version:</span> <span style="font-style: italic;">v6.50 + PATCH to support STM32F427x/437x 
devices</span></span><br></li></ul></ul>
<ul><li class="MsoNormal"><span style="font-size: 10pt; font-family: Verdana;">RealView 
Microcontroller Development Kit (MDK-ARM)&nbsp;toolchain</span><span style="font-size: 10pt; font-family: Verdana;"><span style="text-decoration: underline;"></span></span> </li></ul>
<ul><ul><li class="MsoNormal"><span style="font-size: 10pt; font-family: Verdana;"><span style="text-decoration: underline;">Used version:</span> <span style="font-style: italic;">v</span></span><span style="font-size: 10pt; font-style: italic; font-family: Verdana;">4.60 
</span><span style="font-size: 10pt; font-family: Verdana;"><span style="font-style: italic;">+ PATCH to support STM32F427x/437x 
devices</span></span><span style="font-size: 10pt; font-family: Verdana;"></span></li></ul><li class="MsoNormal"><span style="font-size: 10pt; font-family: Verdana;">TASKING 
VX-toolset for ARM Cortex-M (</span><span style="font-size: 10pt; font-family: Verdana;">TASKING</span><span style="font-size: 10pt; font-family: Verdana;">)&nbsp;toolchain</span><span style="font-size: 10pt; font-family: Verdana;"><span style="text-decoration: underline;"></span></span> 
<ul><li class="MsoNormal"><span style="font-size: 10pt; font-family: Verdana;"><span style="text-decoration: underline;">Used version:</span> <span style="font-style: italic;"></span></span><span style="font-size: 10pt; font-family: Verdana;">v4.3r1 </span><span style="font-size: 10pt; font-family: Verdana;"><span style="font-style: italic;">+ 
PATCH to support STM32F427x/437x devices</span></span><span style="font-size: 10pt; font-style: italic; font-family: Verdana;"></span> 
</li></ul>
</li><li class="MsoNormal"><span style="font-size: 10pt; font-family: Verdana;">STM32 
TrueSTUDIO software (</span><span style="font-size: 10pt; font-family: Verdana;">TrueSTUDIO</span><span style="font-size: 10pt; font-family: Verdana;">)&nbsp;toolchain</span><span style="font-size: 10pt; font-family: Verdana;"><span style="text-decoration: underline;"></span></span> 
<ul><li class="MsoNormal"><span style="font-size: 10pt; font-family: Verdana;"><span style="text-decoration: underline;">Used version:</span> <span style="font-style: italic;">v</span></span><span style="font-size: 10pt; font-style: italic; font-family: Verdana;">3.3.0&nbsp;</span><span style="font-size: 10pt; font-family: Verdana;"><span style="font-style: italic;">+ 
PATCH to support STM32F427x/437x devices</span></span> </li></ul>
</li><li class="MsoNormal"><span style="font-size: 10pt; font-family: Verdana;">Raisonance RIDE7 software 
(RIDE)&nbsp;toolchain</span><span style="font-size: 10pt; font-family: Verdana;"><span style="text-decoration: underline;"></span></span> 
<ul><li class="MsoNormal"><span style="font-size: 10pt; font-family: Verdana;"><span style="text-decoration: underline;">Used version:</span> <span style="font-style: italic;">RIDE7 IDE:7.36, RKitARM for 
RIDE7:1.48</span></span><span style="font-size: 10pt; font-style: italic; font-family: Verdana;"></span> 
</li></ul></li></ul>
</li><li class="MsoNormal"><span style="font-size: 10pt; font-family: Verdana;">Add 
support for STM32F427x/437x </span><span style="font-size: 10pt; font-family: Verdana;">devices:</span> 
<ul><li class="MsoNormal"><span style="font-size: 10pt; font-family: Verdana;">Create a 
new workspace <span style="font-weight: bold; font-style: italic;">STM3242x7I_EVAL 
(STM32F427x/STM32F437x)</span></span></li></ul></li></ul>
<ul style="margin-top: 0cm;" type="square"><li class="MsoNormal"><span style="font-size: 10pt; font-family: Verdana;"><span style="font-weight: bold; font-style: italic; text-decoration: underline;">Important 
Note</span><span style="font-weight: bold;"> <br></span></span>
<ul><li class="MsoNormal"><span style="font-size: 10pt; font-family: Verdana;"><span style="font-weight: bold;"></span>The example provided within this&nbsp;version 
assumes that the HSE </span><span style="font-size: 10pt; font-family: Verdana;">value is set to 25 MHz. If you 
have a different HSE value, you have to adapt the Library as follows:</span> 
</li></ul>
<ul><ul><li class="MsoNormal" style="margin-top: 4.5pt; margin-bottom: 4.5pt; color: black;"><span style="font-size: 10pt; font-family: Verdana;"><span style="font-style: italic;">stm32f4xx.h</span> file (under 
Libraries\CMSIS\Device\ST\STM32F4xx\Include): at line103, adapt the HSE value 
(HSE_VALUE) to your own configuration.<span style="font-weight: bold;"></span></span></li></ul></ul></li></ul><h3 style="background: rgb(51, 102, 255) none repeat scroll 0% 50%; -moz-background-clip: -moz-initial; -moz-background-origin: -moz-initial; -moz-background-inline-policy: -moz-initial; margin-right: 500pt; width: 167px;"><span style="font-size: 10pt; font-family: Arial; color: white;">V1.0.1 / 13-April-2012<o:p></o:p></span></h3>
            <p class="MsoNormal" style="margin: 4.5pt 0cm 4.5pt 18pt;"><b style=""><u><span style="font-size: 10pt; font-family: Verdana; color: black;">Main
Changes<o:p></o:p></span></u></b></p>

            <ul style="margin-top: 0cm;" type="square"><li class="MsoNormal" style="color: black; margin-top: 4.5pt; margin-bottom: 4.5pt;"><span style="font-size: 10pt; font-family: Verdana;">All source files:&nbsp;license disclaimer text update and add link to the License file on ST Internet.</span></li></ul><h3 style="background: rgb(51, 102, 255) none repeat scroll 0% 50%; -moz-background-clip: -moz-initial; -moz-background-origin: -moz-initial; -moz-background-inline-policy: -moz-initial; margin-right: 500pt; width: 176px;"><span style="font-size: 10pt; font-family: Arial; color: white;">V1.0.0 /&nbsp;30-September-2011<o:p></o:p></span></h3><p class="MsoNormal" style="margin: 4.5pt 0cm 4.5pt 18pt;"><b style=""><u><span style="font-size: 10pt; font-family: Verdana; color: black;">Main
Changes<o:p></o:p></span></u></b></p>
<ul style="margin-top: 0cm;" type="square"><li class="MsoNormal" style="color: black; margin-top: 4.5pt; margin-bottom: 4.5pt;"><span style="font-size: 10pt; font-family: Verdana;">First official release for <span style="font-weight: bold; font-style: italic;">STM32F40x/41x devices</span></span></li></ul><span style="font-size: 10pt; font-family: Verdana;"></span><span style="font-size: 10pt; font-family: Verdana;"></span>

<ul style="margin-top: 0in;" type="disc">

</ul><span style="font-size: 10pt; font-family: &quot;Verdana&quot;,&quot;sans-serif&quot;; color: black;"></span>

<div class="MsoNormal" style="text-align: center;" align="center"><span style="color: black;">
<hr align="center" size="2" width="100%"></span></div>
<p class="MsoNormal" style="margin: 4.5pt 0cm 4.5pt 18pt; text-align: center;" align="center"><span style="font-size: 10pt; font-family: &quot;Verdana&quot;,&quot;sans-serif&quot;; color: black;">For
    complete documentation on </span><span style="font-size: 10pt; font-family: &quot;Verdana&quot;,&quot;sans-serif&quot;;">STM32<span style="color: black;">
    Microcontrollers visit </span><u><span style="color: blue;"><a href="http://www.st.com/internet/mcu/family/141.jsp" target="_blank">www.st.com/STM32</a></span></u></span><span style="font-size: 10pt; font-family: Verdana;"><u><span style="color: blue;"><a href="http://www.st.com/stm32" target="_blank"></a></span></u></span><span style="color: black;"><o:p></o:p></span></p>
</td>
</tr>
</tbody>
</table>
<p class="MsoNormal"><span style="font-size: 10pt;"><o:p></o:p></span></p>
</td>
</tr>
</tbody>
</table>
</div>
<p class="MsoNormal"><o:p>&nbsp;</o:p></p>
</div>
</body></html>