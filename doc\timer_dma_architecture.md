# INA226 定时器+DMA 自动采集架构

## 架构概述

新的方案使用 **定时器触发 + DMA自动传输** 的方式，完全消除了对主循环轮询和中断处理的依赖，实现真正的自动化数据采集。

## 核心组件

### 1. 定时器触发 (TIM2)
```c
// 配置 1kHz 定时器 (1ms 间隔)
TIM_TimeBaseStructure.TIM_Period = 1000 - 1;        // 1ms at 1MHz
TIM_TimeBaseStructure.TIM_Prescaler = 84 - 1;       // 84MHz/84 = 1MHz
TIM_DMACmd(TIM2, TIM_DMA_Update, ENABLE);           // 启用定时器DMA请求
```

### 2. DMA自动传输 (DMA1_Stream5)
```c
// 配置DMA从I2C读取到内存
DMA_InitStructure.DMA_PeripheralBaseAddr = (uint32_t)&I2C1->DR;
DMA_InitStructure.DMA_Memory0BaseAddr = (uint32_t)dma_read_buffer;
DMA_InitStructure.DMA_BufferSize = 2; // 读取2个寄存器
```

### 3. DMA中断处理
```c
void DMA1_Stream5_IRQHandler(void)
{
    // 数据已自动传输到 dma_read_buffer
    uint16_t shunt_data = dma_read_buffer[0];
    // 存储到ping-pong缓冲区
    samples_buf[active_idx][wr_pos] = shunt_data;
    // 重启DMA准备下次传输
}
```

## 数据流程

```
定时器TIM2 (1kHz) → DMA触发 → I2C自动读取 → DMA中断 → 数据处理
     ↓                ↓              ↓            ↓          ↓
   1ms间隔        自动启动I2C    读取INA226寄存器   数据到达   存储到缓冲区
```

## 优势对比

| 特性 | 原EXTI方案 | 新Timer+DMA方案 |
|------|-----------|----------------|
| **触发方式** | INA226 ALERT信号 | 定时器自动触发 |
| **采样频率** | 不稳定，依赖转换时间 | 固定1kHz，精确 |
| **中断负载** | 高 (50-100µs) | 低 (< 10µs) |
| **主循环依赖** | 需要轮询处理 | 完全独立 |
| **数据丢失风险** | 中等 | 极低 |
| **时序精度** | 依赖INA226 | 由MCU定时器控制 |
| **系统负载** | 中等 | 最低 |

## 实现细节

### 定时器配置
```c
static bool INA226_Timer_DMA_Init(void)
{
    // 1. 启用时钟
    RCC_APB1PeriphClockCmd(RCC_APB1Periph_TIM2, ENABLE);
    RCC_AHB1PeriphClockCmd(RCC_AHB1Periph_DMA1, ENABLE);
    
    // 2. 配置定时器 (1kHz)
    TIM_TimeBaseStructure.TIM_Period = 1000 - 1;
    TIM_TimeBaseStructure.TIM_Prescaler = 84 - 1;
    TIM_TimeBaseInit(TIM2, &TIM_TimeBaseStructure);
    
    // 3. 启用定时器DMA请求
    TIM_DMACmd(TIM2, TIM_DMA_Update, ENABLE);
    
    // 4. 配置DMA
    // ... DMA配置代码
    
    return true;
}
```

### 启动/停止控制
```c
static void INA226_Start_Timer_DMA(void)
{
    timer_dma_enabled = true;
    I2C_DMACmd(I2C1, ENABLE);    // 启用I2C DMA
    TIM_Cmd(TIM2, ENABLE);       // 启动定时器
}

static void INA226_Stop_Timer_DMA(void)
{
    timer_dma_enabled = false;
    TIM_Cmd(TIM2, DISABLE);      // 停止定时器
    I2C_DMACmd(I2C1, DISABLE);   // 禁用I2C DMA
    DMA_Cmd(DMA1_Stream5, DISABLE);
}
```

### DMA中断处理
```c
void DMA1_Stream5_IRQHandler(void)
{
    if (DMA_GetITStatus(DMA1_Stream5, DMA_IT_TCIF5))
    {
        DMA_ClearITPendingBit(DMA1_Stream5, DMA_IT_TCIF5);
        
        if (timer_dma_enabled)
        {
            // 处理接收到的数据
            uint16_t shunt_data = dma_read_buffer[0];
            samples_buf[active_idx][wr_pos] = shunt_data;
            
            // 触发缓冲区管理
            HAL_I2C_MemRxCpltCallback(NULL);
            
            // 重启DMA准备下次传输
            DMA_Cmd(DMA1_Stream5, DISABLE);
            DMA_SetCurrDataCounter(DMA1_Stream5, 2);
            DMA_Cmd(DMA1_Stream5, ENABLE);
        }
    }
}
```

## 配置参数

### 采样频率
- **当前设置**: 1kHz (1ms间隔)
- **可调范围**: 100Hz - 10kHz
- **修改方法**: 调整 `TIM_Period` 值

### DMA缓冲区
```c
static uint16_t dma_read_buffer[2]; // [shunt_voltage, mask_register]
```

### Ping-Pong缓冲区
- 保持原有的双缓冲区机制
- 每100个样本切换一次
- 自动通知上层应用

## 使用方法

### 初始化
```c
if (!INA226_InitOptimized()) {
    // 初始化失败
}
```

### 启动采集
```c
if (!INA226_StartOptimizedAcquisition()) {
    // 启动失败
}
```

### 获取数据
```c
INA226_Data_t data;
if (App_Monitor_Get_Data_Fast(&data)) {
    // 使用数据
    ui_update_ina226_data(data.voltage_V, data.current_A, 
                          data.power_W, data.shunt_voltage_mV);
}
```

## 性能特点

1. **固定采样率**: 1kHz，不受INA226转换时间影响
2. **低CPU占用**: DMA自动传输，CPU只处理结果
3. **高精度时序**: 由MCU定时器控制，误差 < 1µs
4. **无数据丢失**: 定时触发确保连续采样
5. **系统独立**: 不依赖主循环或其他任务

## 注意事项

1. **I2C速度**: 确保I2C速度足够快，能在1ms内完成传输
2. **DMA冲突**: 避免其他外设使用相同的DMA通道
3. **定时器资源**: TIM2被占用，其他功能不能使用
4. **中断优先级**: DMA中断优先级应设置合适，避免被其他中断阻塞

这个方案提供了最可靠、最高效的INA226数据采集方式，特别适合对实时性和稳定性要求高的应用。
