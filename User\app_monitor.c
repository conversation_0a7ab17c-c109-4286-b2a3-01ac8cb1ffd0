/**
 ******************************************************************************
 * @file    app_monitor.c
 * <AUTHOR>
 * @version V2.0.0
 * @date    2025-07-03
 * @brief   Module for monitoring INA226 using optimized EXTI+DMA approach.
 ******************************************************************************
 */

/* Includes ------------------------------------------------------------------*/
#include "stm32f4xx.h"
#include "app_monitor.h"
#include "mod_ina226.h"
#include "app_util.h"
#include "FreeRTOS.h"
#include "task.h"
#include "semphr.h"

/* Private define ------------------------------------------------------------*/
#define RAW_SAMPLE_BUFFER_SIZE      100

/* Private variables ---------------------------------------------------------*/
static uint16_t raw_sample_buffer[RAW_SAMPLE_BUFFER_SIZE];
static bool monitor_running = false;

// Direct access data structure (no queue)
static INA226_Data_t latest_processed_data;
static bool new_data_available = false;
static SemaphoreHandle_t data_mutex = NULL;

/* Private function prototypes -----------------------------------------------*/
static float ConvertToShuntVoltage(uint16_t raw_value);
static float ConvertToCurrent(float shunt_mv);

/* Public functions ----------------------------------------------------------*/

/**
 * @brief Initializes the monitoring application module with optimized EXTI+DMA.
 * @retval true if initialization is successful, false otherwise.
 */
bool App_Monitor_Init(void)
{
    logi("=== App Monitor Init (Optimized) ===\r\n");

    // Initialize INA226 in optimized mode
    if (!INA226_InitOptimized()) {
        logi("INA226 optimized initialization failed!\r\n");
        return false;
    }

    // Create mutex for data protection (instead of queue)
    data_mutex = xSemaphoreCreateMutex();
    if (data_mutex == NULL) {
        logi("Failed to create data mutex.\r\n");
        return false;
    }
    logi("Data mutex created (no queue).\r\n");

    // Timer-based approach doesn't need a separate monitoring task
    logi("Using Timer-based automatic acquisition (no separate task needed).\r\n");

    // Start optimized data acquisition
    if (!INA226_StartOptimizedAcquisition()) {
        logi("Failed to start optimized acquisition!\r\n");
        return false;
    }
    logi("Optimized data acquisition started.\r\n");

    monitor_running = true;
    return true;
}

/**
 * @brief Retrieves the latest processed INA226 data directly (no queue).
 * @param data Pointer to a structure to store the data.
 * @param timeout_ms Timeout in milliseconds (unused, kept for compatibility).
 * @retval true if new data was available, false otherwise.
 */
bool App_Monitor_Get_Data(INA226_Data_t *data, uint32_t timeout_ms)
{
    if (data == NULL || data_mutex == NULL) {
        return false;
    }

    // Take mutex with short timeout
    if (xSemaphoreTake(data_mutex, pdMS_TO_TICKS(5)) == pdPASS) {
        if (new_data_available) {
            *data = latest_processed_data;
            new_data_available = false;  // Mark as consumed
            xSemaphoreGive(data_mutex);
            return true;
        }
        xSemaphoreGive(data_mutex);
    }

    return false;
}

/**
 * @brief Stops the monitoring application.
 * @retval None
 */
void App_Monitor_Stop(void)
{
    monitor_running = false;
    INA226_StopOptimizedAcquisition();
    logi("App Monitor stopped.\r\n");
}

/**
 * @brief Gets the monitoring status.
 * @retval true if monitoring is running, false otherwise.
 */
bool App_Monitor_IsRunning(void)
{
    return monitor_running;
}

/**
 * @brief Print complete INA226 analysis
 * @retval None
 */
void App_Monitor_Print_Complete_Analysis(void)
{
    logi("\r\n");
    logi("################################################\r\n");
    logi("###        COMPLETE INA226 ANALYSIS         ###\r\n");
    logi("################################################\r\n");

    // Print all registers
    INA226_PrintAllRegisters();

    // Print Timer status
    INA226_GetTimerStatus();

    // Print transfer statistics
    uint32_t total_transfers, success_transfers, error_transfers;
    if (INA226_GetDMAStatus(&total_transfers, &success_transfers, &error_transfers)) {
        float success_rate = ((float)success_transfers / (float)total_transfers) * 100.0f;
        logi("Transfer Statistics:\r\n");
        logi("  Total: %lu\r\n", total_transfers);
        logi("  Success: %lu\r\n", success_transfers);
        logi("  Errors: %lu\r\n", error_transfers);
        logi("  Success Rate: %.2f%%\r\n", success_rate);
    }

    logi("################################################\r\n");
    logi("\r\n");
}

/**
 * @brief Check Timer and transfer status and print statistics
 * @retval true if system is working properly, false if errors detected
 */
bool App_Monitor_Check_DMA_Status(void)
{
    uint32_t total_transfers, success_transfers, error_transfers;

    // Check Timer status first
    logi("=== System Status Check ===\r\n");
    bool timer_running = INA226_GetTimerStatus();

    // Print all INA226 registers for detailed analysis
    bool registers_ok = INA226_PrintAllRegisters();

    // Also do basic verification
    INA226_VerifyRegisters();

    if (!INA226_GetDMAStatus(&total_transfers, &success_transfers, &error_transfers)) {
        logi("ERROR: Cannot get transfer status!\r\n");
        return false;
    }

    // Calculate success rate (fix calculation bug)
    float success_rate = (total_transfers > 0) ?
                        ((float)success_transfers / (float)total_transfers) * 100.0f : 0.0f;

    // Log status every time this function is called
    logi("Transfer Status: Total=%lu, Success=%lu, Errors=%lu, Rate=%.1f%%\r\n",
         total_transfers, success_transfers, error_transfers, success_rate);

    // Check for problems
    if (!timer_running) {
        logi("ERROR: Timer is not running!\r\n");
        return false;
    }

    if (!registers_ok) {
        logi("ERROR: INA226 registers are not configured correctly!\r\n");
        return false;
    }

    if (total_transfers == 0) {
        logi("WARNING: No transfers have occurred yet.\r\n");
        return false;
    }

    if (total_transfers > 100 && success_rate < 90.0f) {
        logi("WARNING: Transfer error rate too high!\r\n");
        return false;
    }

    // Check if transfers are stalled (no new transfers)
    static uint32_t last_total = 0;
    if (total_transfers == last_total && total_transfers > 0) {
        logi("WARNING: Transfers appear to be stalled!\r\n");
        return false;
    }
    last_total = total_transfers;

    logi("=== System Status: OK ===\r\n");
    return true;
}

/**
 * @brief Get direct access to DMA buffer for ultra-fast chart updates
 * @param buffer_ptr Pointer to receive buffer address
 * @param buffer_size Pointer to receive buffer size
 * @param write_pos Pointer to receive current write position
 * @retval true if buffer access granted, false otherwise
 */
bool App_Monitor_Get_Direct_Buffer_Access(uint16_t **buffer_ptr, uint32_t *buffer_size, uint32_t *write_pos)
{
    if (!monitor_running || buffer_ptr == NULL || buffer_size == NULL || write_pos == NULL) {
        return false;
    }

    // Get direct access to the active DMA buffer
    if (INA226_GetDirectBufferAccess(buffer_ptr, buffer_size, write_pos)) {
        return true;
    }

    return false;
}

/**
 * @brief Get latest data directly from buffer (ultra-fast, Timer+DMA automatic)
 * @param data Pointer to store the latest raw converted data
 * @retval true if new data available, false otherwise
 */
bool App_Monitor_Get_Data_Fast(INA226_Data_t *data)
{
    if (data == NULL || !monitor_running) {
        return false;
    }

    // Check for new raw data from Timer+DMA (automatic)
    if (INA226_IsNewDataAvailable())
    {
        uint32_t samples_read = INA226_GetLatestData(raw_sample_buffer, 1); // Get just 1 sample

        if (samples_read > 0)
        {
            // Convert single sample directly
            float shunt_mv = ConvertToShuntVoltage(raw_sample_buffer[0]);
            float current_a = ConvertToCurrent(shunt_mv);
            float voltage_v = 3.3f; // Assume fixed bus voltage for speed
            float power_w = current_a * voltage_v;

            // Fill data structure
            data->voltage_V = voltage_v;
            data->current_A = current_a;
            data->power_W = power_w;
            data->shunt_voltage_mV = shunt_mv;

            return true;
        }
    }

    return false;
}

/* Private functions ---------------------------------------------------------*/

// Monitor_Task removed - Timer interrupt handles data acquisition automatically

// ProcessRawSamples removed - Timer interrupt processes data directly

/**
 * @brief Convert raw value to shunt voltage
 * @param raw_value: Raw 16-bit value from INA226
 * @retval Shunt voltage in millivolts
 */
static float ConvertToShuntVoltage(uint16_t raw_value)
{
    // INA226 shunt voltage LSB is 2.5µV per bit
    int16_t signed_value = (int16_t)raw_value;
    return signed_value * 0.0025f; // Convert to mV
}

/**
 * @brief Convert shunt voltage to current
 * @param shunt_mv: Shunt voltage in millivolts
 * @retval Current in amperes
 */
static float ConvertToCurrent(float shunt_mv)
{
    // I = V / R, where R = 0.75Ω (750mΩ)
    return (shunt_mv / 1000.0f) / INA226_SHUNT_RESISTANCE_OHMS;
}
