/**
 * @file lv_draw.c
 *
 */

/*********************
 *      INCLUDES
 *********************/
#include "lv_draw.h"
#include "sw/lv_draw_sw.h"

/*********************
 *      DEFINES
 *********************/

/**********************
 *      TYPEDEFS
 **********************/

/**********************
 *  STATIC PROTOTYPES
 **********************/

/**********************
 *  STATIC VARIABLES
 **********************/

/**********************
 *  STATIC VARIABLES
 **********************/

/**********************
 *      MACROS
 **********************/

/**********************
 *   GLOBAL FUNCTIONS
 **********************/

void lv_draw_init(void)
{
    //    backend_head = NULL;
    //    lv_draw_sw_init();
    //
    //#if LV_USE_GPU_STM32_DMA2D == 0
    //    lv_gpu_stm32_dma2d_init();
    //#endif
}

/**********************
 *   STATIC FUNCTIONS
 **********************/

