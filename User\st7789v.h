#ifndef ST7789V_H
#define ST7789V_H

#include "stm32f4xx.h" 
#include <stdio.h>

/* Control Registers and constant codes */
#define ST7789_NOP 0x00
#define ST7789_SWRESET 0x01
#define ST7789_RDDID 0x04
#define ST7789_RDDST 0x09

#define ST7789_SLPIN 0x10
#define ST7789_SLPOUT 0x11
#define ST7789_PTLON 0x12
#define ST7789_NORON 0x13

#define ST7789_INVOFF 0x20
#define ST7789_INVON 0x21
#define ST7789_DISPOFF 0x28
#define ST7789_DISPON 0x29
#define ST7789_CASET 0x2A
#define ST7789_RASET 0x2B
#define ST7789_RAMWR 0x2C
#define ST7789_RAMRD 0x2E

#define ST7789_PTLAR 0x30
#define ST7789_COLMOD 0x3A
#define ST7789_MADCTL 0x36

/**
 * Memory Data Access Control Register (0x36H)
 * MAP:     D7  D6  D5  D4  D3  D2  D1  D0
 * param:   MY  MX  MV  ML  RGB MH  -   -
 *
 */

/* Page Address Order ('0': Top to Bottom, '1': the opposite) */
#define ST7789_MADCTL_MY 0x80
/* Column Address Order ('0': Left to Right, '1': the opposite) */
#define ST7789_MADCTL_MX 0x40
/* Page/Column Order ('0' = Normal Mode, '1' = Reverse Mode) */
#define ST7789_MADCTL_MV 0x20
/* Line Address Order ('0' = LCD Refresh Top to Bottom, '1' = the opposite) */
#define ST7789_MADCTL_ML 0x10
/* RGB/BGR Order ('0' = RGB, '1' = BGR) */
#define ST7789_MADCTL_RGB 0x00

// typedef enum
// {
//     LV_DISP_ROT_NONE = 0,
//     LV_DISP_ROT_90,
//     LV_DISP_ROT_180,
//     LV_DISP_ROT_270
// } lv_disp_rot_t;

/*
 *ST7789V的支持得到最大分辨率为240*320，该显示屏支持240*280，故有偏移量
 *偏移量计算举例：（320-280）/2=20
 *以USE_HORIZONTAL==1时为基准，即正屏
 */
#define WIDTH_OFFSET 35
#define HIGH_OFFSET 0

#define USE_HORIZONTAL 2 // 设置横屏或者竖屏显示 0或1为竖屏 2或3为横屏

// 画笔颜色
#define WHITE (uint16_t)0xFFFF
#define BLACK (uint16_t)0x0000
#define BLUE (uint16_t)0x001F
#define BRED (uint16_t)0XF81F
#define GRED (uint16_t)0XFFE0
#define GBLUE (uint16_t)0X07FF
#define RED (uint16_t)0xF800
#define MAGENTA (uint16_t)0xF81F
#define GREEN (uint16_t)0x07E0
#define CYAN (uint16_t)0x7FFF
#define YELLOW (uint16_t)0xFFE0
#define BROWN (uint16_t)0XBC40      // 棕色
#define BRRED (uint16_t)0XFC07      // 棕红色
#define GRAY (uint16_t)0X8430       // 灰色
#define DARKBLUE (uint16_t)0X01CF   // 深蓝色
#define LIGHTBLUE (uint16_t)0X7D7C  // 浅蓝色
#define GRAYBLUE (uint16_t)0X5458   // 灰蓝色
#define LIGHTGREEN (uint16_t)0X841F // 浅绿色
#define LGRAY (uint16_t)0XC618      // 浅灰色(PANNEL),窗体背景色
#define LGRAYBLUE (uint16_t)0XA651  // 浅灰蓝色(中间层颜色)
#define LBBLUE (uint16_t)0X2B12     // 浅棕蓝色(选择条目的反色)

// 定义 st7789v 的 SPI CS 引脚
#define SPI_ST7789V_RCU_CS RCU_GPIOA
#define SPI_ST7789V_PIN_CS GPIO_Pin_4
#define SPI_ST7789V_GPIO_CS GPIOA

#define ST7789V_CS_LOW() GPIO_ResetBits(SPI_ST7789V_GPIO_CS, SPI_ST7789V_PIN_CS) // 拉低 NSS
#define ST7789V_CS_HIGH() GPIO_SetBits(SPI_ST7789V_GPIO_CS, SPI_ST7789V_PIN_CS)  // 拉高 NSS

// 定义 st7789v 的 DC 引脚
#define ST7789V_DC_GPIO_CLK RCU_GPIOB
#define ST7789V_DC_PIN GPIO_Pin_10
#define ST7789V_DC_GPIO_PORT GPIOB

// 定义背光 BL 控制引脚
#define ST7789V_BL_GPIO_CLK RCU_GPIOB
#define ST7789V_BL_PIN GPIO_Pin_0
#define ST7789V_BL_GPIO_PORT GPIOB

// 定义 RST 控制引脚
#define SPI_ST7789V_RCU_RST RCU_GPIOB
#define ST7789V_PIN_RST GPIO_Pin_1
#define ST7789V_GPIO_PORT_RST GPIOB

/*ST7789V_AdaptiveBrightnessColorEnhancementInit初始化结构体*/
typedef struct
{
    uint8_t Adaptive_Brightness_Goal; /*off:00,User Interface Mode:01,Still Picture:10,Moving Image:11*/
    uint8_t Color_Enhancement_Cmd;    /*open:1,close:0*/
    uint8_t Color_Enhancement_Extent; /*Low enhancement:00,Medium enhancement:01,High enhancement:11*/
} WRCACE_InitTypedef;

extern WRCACE_InitTypedef WRCACE_InitStruct;

/*
 *Parameter:NewState,
 *					是否使能
 *Parameter:Start_Line,
 *					局部显示开始地址
 *Parameter:End_Line,
 *					局部显示结束地址
 *Role     :
 *         局部显示
 */
void ST7789V_PartialDisplayModeCmd(FunctionalState NewState, uint16_t Start_Line, uint16_t End_Line);

/*
 *Parameter:NewState,
 *					是否使能
 *Role     :
 *         空闲模式开和关（在图像要求不高下，减少耗电）
 */
void ST7789V_IdleModeCmd(FunctionalState NewState);

/*
 *Parameter:NewState,
 *					是否使能
 *Role     :
 *         显示开和关
 */
void ST7789V_DisplayCmd(FunctionalState NewState);

/*
 *Parameter:NewState,
 *					是否使能
 *Role     :
 *         睡眠模式开和关（关闭显示，停止显示，内容保存，减少耗电）
 */
void ST7789V_SleepModeCmd(FunctionalState NewState);

/*
 *Parameter:WRCACE_InitStruct,
 *					初始结构体
 *Role     :
 *         亮度自适应，颜色增强配置
 */
void ST7789V_AdaptiveBrightnessColorEnhancementInit(WRCACE_InitTypedef *WRCACE_InitStruct);

/*
 *Parameter:TopStill_H
 *					顶部静止高度
 *Parameter:VerticalScroll_H,
 *					滚动区域高度
 *Parameter:BottomStill_H,
 *					底部静止高度
 *Parameter:StartAddr,
 *					开始的行(注：范围为TopStill_H~VerticalScroll_H+TopStill_H)
 *Parameter:ScrollTime,
 *					刷新速度（建议最大50）
 *Return   :
 *					当前起始行
 *Role     :
 *         配置并开始垂直滚动（注：放入循环！！！）
 */
uint16_t ST7789V_VerticalScrollMode(uint16_t TopStill_H, uint16_t VerticalScroll_H, uint16_t BottomStill_H, uint16_t StartAddr, uint16_t ScrollTime);

/*
 *Parameter:Start_X
 *					起始点x
 *Parameter:Start_Y,
 *					起始点y
 *Parameter:End_X,
 *					结束点x
 *Parameter:End_Y,
 *					结束点y
 *Parameter:Dir_Mode,
 *					设置当前显示方向下的坐标系
 *Role     :
 *         设置显示范围
 */
void ST7789V_SetLcdAddress(uint16_t Start_X, uint16_t Start_Y, uint16_t End_X, uint16_t End_Y, uint8_t Dir_Mode);

void ST7789V_SetLcdXY(uint16_t Start_X, uint16_t Start_Y, uint8_t Dir_Mode);

/*
 *Parameter:Dir_Mode,
 *					显示方向
 *Role     :
 *         设置当前显示方向
 */
void ST7789V_SetDir(uint8_t Dir_Mode);

// void ST7789V_SendOneColor(uint16_t Color);

void ST7789V_SendColorLen(uint16_t color, uint32_t len);

/*
 *Role     :
 *         复位
 */
void ST7789V_Reset(void);

/*
 *Role     :
 *         lcd初始化
 */
void ST7789V_LcdInit(void);

// 使用指针传输显示数据
void ST7789V_FillLcdScreen_ColorPtr(uint16_t Start_X, uint16_t Start_Y, uint16_t End_X, uint16_t End_Y, uint16_t *Color, uint8_t Dir_Mode);

void ST7789V_SPISendData2Bytes(uint16_t Data) ;

#endif
