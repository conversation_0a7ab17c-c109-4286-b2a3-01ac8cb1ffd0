

#ifndef _APP_UART_H
#define _APP_UART_H

#include "main.h" 

#define TXBUF_SIZE_MAX    512

#define UART_RATE_9600 9600
#define UART_RATE_19200 19200
#define UART_RATE_38400 38400
#define UART_RATE_57600 57600
#define UART_RATE_115200 115200
//  5=9600 6=19200 7=38400 8=57600 9=115200 默认 9= 115200

void app_uart_send(char *buf, uint16_t size);

void logihex(char * data, uint16_t len );

void logi( const char *format, ...);

#endif /* _APP_UART_H */


