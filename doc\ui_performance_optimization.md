# UI性能优化 - 降低定时器频率

## 问题分析

原始配置导致屏幕刷新卡顿的原因：

### 1. 数据采集频率过高
```c
// 原始配置：1kHz (1ms间隔)
TIM_TimeBaseStructure.TIM_Period = 1000 - 1;    // 太快了！
```

**问题**：
- 每秒1000次中断
- 每秒1000次UI更新
- LVGL无法跟上如此高的刷新率
- MCU大部分时间在处理UI更新

### 2. UI刷新频率过高
```c
// 每次有新数据就更新图表
ui_update_chart_from_dma_buffer(...);  // 1000Hz调用
ui_update_ina226_data(...);           // 1000Hz调用
```

**问题**：
- LVGL推荐刷新率：30-60Hz
- 1000Hz远超LVGL处理能力
- 导致界面卡顿、响应慢

## 优化方案

### 1. 降低数据采集频率

```c
// 优化后：100Hz (10ms间隔)
TIM_TimeBaseStructure.TIM_Period = 10000 - 1;   // 10ms at 1MHz
TIM_TimeBaseStructure.TIM_Prescaler = 84 - 1;   // 84MHz/84 = 1MHz
```

**优势**：
- 100Hz对于电流监控完全足够
- 减少90%的中断处理开销
- 仍能捕获大部分电流变化

### 2. 进一步降低图表更新频率

```c
// 图表更新频率控制
static uint32_t chart_update_counter = 0;

chart_update_counter++;
if (chart_update_counter >= 3) { // 每3次数据更新一次图表
    ui_update_chart_from_dma_buffer(...);
    chart_update_counter = 0;
}
// 实际图表更新频率：100Hz ÷ 3 ≈ 33Hz
```

**优势**：
- 33Hz图表更新，人眼感觉流畅
- 减少67%的图表绘制开销
- 数值显示仍保持100Hz更新

### 3. 分离数值和图表更新

```c
// 数值显示：100Hz更新（快速响应）
ui_update_ina226_data(voltage, current, power, shunt_voltage);

// 图表显示：33Hz更新（流畅显示）
if (chart_update_counter >= 3) {
    ui_update_chart_from_dma_buffer(...);
}
```

## 频率对比

| 组件 | 原始频率 | 优化频率 | 性能提升 |
|------|---------|---------|---------|
| **Timer中断** | 1000Hz | 100Hz | **90%↓** |
| **数据采集** | 1000Hz | 100Hz | **90%↓** |
| **数值更新** | 1000Hz | 100Hz | **90%↓** |
| **图表更新** | 1000Hz | 33Hz | **97%↓** |

## 用户体验对比

### 优化前
- ❌ 界面卡顿严重
- ❌ 触摸响应慢
- ❌ CPU占用率高
- ❌ 功耗高

### 优化后
- ✅ 界面流畅
- ✅ 触摸响应快
- ✅ CPU占用率低
- ✅ 功耗降低
- ✅ 数据精度仍然足够

## 技术细节

### Timer配置变化
```c
// 原始：1kHz
TIM_Period = 1000 - 1;     // 1ms
// 中断频率 = 84MHz / 84 / 1000 = 1000Hz

// 优化：100Hz  
TIM_Period = 10000 - 1;    // 10ms
// 中断频率 = 84MHz / 84 / 10000 = 100Hz
```

### 数据精度分析
```
100Hz采样 = 10ms间隔
对于电流监控：
- 能检测到 >10ms 的电流变化
- 适合大多数应用场景
- 仍能捕获功耗峰值
```

### LVGL性能考虑
```
LVGL推荐刷新率：30-60Hz
我们的配置：
- 数值更新：100Hz (超出推荐，但可接受)
- 图表更新：33Hz (在推荐范围内)
```

## 进一步优化建议

### 1. 可配置频率
```c
#define DATA_ACQUISITION_HZ  100    // 可调整：50-200Hz
#define CHART_UPDATE_DIVIDER 3      // 可调整：2-5

// 实际图表频率 = DATA_ACQUISITION_HZ / CHART_UPDATE_DIVIDER
```

### 2. 自适应频率
```c
// 根据CPU负载自动调整
if (cpu_usage > 80%) {
    chart_update_divider = 5;  // 降低到20Hz
} else if (cpu_usage < 50%) {
    chart_update_divider = 2;  // 提高到50Hz
}
```

### 3. 优先级调整
```c
// 数值显示优先级高（实时性）
ui_update_ina226_data(...);  // 每次都更新

// 图表显示优先级低（流畅性）
if (should_update_chart) {
    ui_update_chart_from_dma_buffer(...);
}
```

## 结论

通过降低定时器频率从1kHz到100Hz，并进一步控制图表更新频率到33Hz：

1. **大幅提升UI性能** - 减少97%的图表绘制开销
2. **保持数据精度** - 100Hz对电流监控完全足够
3. **改善用户体验** - 界面流畅，响应快速
4. **降低功耗** - 减少90%的中断处理

这是在性能和精度之间的最佳平衡点！
