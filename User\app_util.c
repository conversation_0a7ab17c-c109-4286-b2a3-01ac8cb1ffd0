
#include "app_util.h"

void printf_float(float value) {
  int tmp, tmp1, tmp2, tmp3, tmp4, tmp5, tmp6;
  tmp = (int)value;
  tmp1 = (int)((value - tmp) * 10) % 10;
  tmp2 = (int)((value - tmp) * 100) % 10;
  tmp3 = (int)((value - tmp) * 1000) % 10;
  tmp4 = (int)((value - tmp) * 10000) % 10;
  tmp5 = (int)((value - tmp) * 100000) % 10;
  tmp6 = (int)((value - tmp) * 1000000) % 10;
  printf("%d.%d%d%d%d%d%d", tmp, tmp1, tmp2, tmp3, tmp4, tmp5, tmp6);
}

void printf_double(double value) {
  int tmp, tmp1, tmp2, tmp3, tmp4, tmp5, tmp6;
  tmp = (int)value;
  tmp1 = (int)((value - tmp) * 10) % 10;
  tmp2 = (int)((value - tmp) * 100) % 10;
  tmp3 = (int)((value - tmp) * 1000) % 10;
  tmp4 = (int)((value - tmp) * 10000) % 10;
  tmp5 = (int)((value - tmp) * 100000) % 10;
  tmp6 = (int)((value - tmp) * 1000000) % 10;
  printf("%d.%d%d%d%d%d%d", tmp, tmp1, tmp2, tmp3, tmp4, tmp5, tmp6);
}

void s_delay_us(uint32_t us) {
  // for (uint32_t i = 0; i < us; i++) {
    for (uint32_t k = 0; k < 200; k++) {
    }
  // }
}
void s_delay_ms(uint32_t ms) {
  for (uint32_t i = 0; i < ms; i++) {
    s_delay_us(1000);
  }
}