/**
 ******************************************************************************
 * @file    ui_demo.h
 * <AUTHOR>
 * @version V1.0.0
 * @date    2025-07-02
 * @brief   UI demo interface header file
 ******************************************************************************
 */

#ifndef __UI_DEMO_H
#define __UI_DEMO_H

#ifdef __cplusplus
extern "C" {
#endif

/* Includes ------------------------------------------------------------------*/
#include "lvgl.h"
#include <stdio.h>

/* Exported types ------------------------------------------------------------*/
/* Exported constants --------------------------------------------------------*/
/* Exported macro ------------------------------------------------------------*/
/* Exported functions --------------------------------------------------------*/

/**
 * @brief Create a simple UI test interface
 * @retval None
 */
void ui_test(void);

/**
 * @brief Initialize UI demo module
 * @retval None
 */
void ui_demo_init(void);

/**
 * @brief Cleanup UI demo resources
 * @retval None
 */
void ui_demo_cleanup(void);

/**
 * @brief Create INA226 data display interface
 * @retval None
 */
void ui_create_ina226_display(void);

/**
 * @brief 直接从DMA缓冲区批量更新图表 (超高效)
 * @param dma_buffer: DMA缓冲区指针
 * @param buffer_size: 缓冲区大小
 * @param start_pos: 开始位置
 * @param count: 要处理的样本数
 * @retval None
 */
void ui_update_chart_from_dma_buffer(uint16_t *dma_buffer, uint32_t buffer_size, uint32_t start_pos, uint32_t count);

/**
 * @brief Update INA226 data display
 * @param voltage: Voltage value in volts
 * @param current: Current value in amperes
 * @param power: Power value in watts
 * @param shunt_voltage: Shunt voltage value in millivolts
 * @retval None
 */
void ui_update_ina226_data(float voltage, float current, float power, float shunt_voltage);

/**
 * @brief Test INA226 data display update with dummy data
 * @retval None
 */
void ui_test_ina226_update(void);

#ifdef __cplusplus
}
#endif

#endif /* __UI_DEMO_H */
