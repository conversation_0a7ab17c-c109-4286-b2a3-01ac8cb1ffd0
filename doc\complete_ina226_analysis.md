# INA226 完整数据分析功能

## 功能概述

新增了完整的INA226寄存器读取和分析功能，可以全面诊断电流读取问题。

## 新增功能

### 1. 完整寄存器打印 (`INA226_PrintAllRegisters`)

读取并分析INA226的所有8个寄存器：

```c
bool INA226_PrintAllRegisters(void);
```

**输出示例**:
```
=== INA226 Complete Register Dump ===
Reg[0x00] Config      : 0x0127 (295)
Reg[0x01] Shunt_V     : 0x071B (1819)
Reg[0x02] Bus_V       : 0x1A60 (6752)
Reg[0x03] Power       : 0x0012 (18)
Reg[0x04] Current     : 0x0048 (72)
Reg[0x05] Calibration : 0x0444 (1092)
Reg[0x06] Mask/Enable : 0x0408 (1032)
Reg[0x07] Alert_Limit : 0x0000 (0)
=====================================

=== Register Analysis ===
Config Analysis:
  Reset: NO
  Avg: 1 samples
  VBUSCT: 140 µs
  VSHCT: 140 µs
  Mode: 7 (Continuous)
Shunt Voltage: 4.548 mV (Current: 0.006063 A)
Bus Voltage: 3.300 V
Power: 0.000045 W
Current (direct): 0.007200 A
========================
```

### 2. 完整系统分析 (`App_Monitor_Print_Complete_Analysis`)

综合分析系统状态：

```c
void App_Monitor_Print_Complete_Analysis(void);
```

**输出示例**:
```
################################################
###        COMPLETE INA226 ANALYSIS         ###
################################################

=== INA226 Complete Register Dump ===
[寄存器详细信息]

Timer2 Status: Enabled=YES, Count=1234, DMA_Enabled=YES

Transfer Statistics:
  Total: 1129
  Success: 1128
  Errors: 1
  Success Rate: 99.91%
################################################
```

## 寄存器详细分析

### 0x00 - Config Register
- **Reset位**: 检查是否意外复位
- **平均次数**: 1-1024次采样平均
- **转换时间**: Bus和Shunt电压转换时间
- **工作模式**: 连续/单次测量模式

### 0x01 - Shunt Voltage Register
- **原始值**: 16位有符号整数
- **电压值**: 原始值 × 2.5µV
- **计算电流**: 分流电压 ÷ 分流电阻

### 0x02 - Bus Voltage Register
- **原始值**: 高13位有效
- **电压值**: (原始值 >> 3) × 1.25mV

### 0x03 - Power Register
- **功率值**: 原始值 × 25 × Current_LSB

### 0x04 - Current Register
- **电流值**: 原始值 × Current_LSB (100µA)

### 0x05 - Calibration Register
- **校准值**: 影响电流和功率测量精度
- **计算公式**: 0.00512 ÷ (Current_LSB × R_shunt)

### 0x06 - Mask/Enable Register
- **中断配置**: 各种报警和转换完成中断

### 0x07 - Alert Limit Register
- **报警阈值**: 可配置的报警限制

## 调试时间安排

### 启动时分析
- **10秒后**: 执行一次完整分析
- **目的**: 检查初始化是否正确

### 定期检查
- **每5秒**: 简化状态检查
- **每30秒**: 完整寄存器分析

### 手动触发
```c
// 可以在任何时候调用
App_Monitor_Print_Complete_Analysis();
```

## 问题诊断指南

### 1. Config寄存器异常
```
Config Register: 0x4127 (Expected: 0x0127)
```
**问题**: 最高位被设置，可能是复位位或其他配置错误
**解决**: 自动恢复功能会重新写入正确值

### 2. 电流计算不一致
```
Shunt Voltage: 4.548 mV (Current: 0.006063 A)  // 从分流电压计算
Current (direct): 0.007200 A                   // 直接从电流寄存器读取
```
**分析**: 两种计算方法的结果应该接近
**问题**: 如果差异很大，可能是校准值错误

### 3. 功率计算验证
```
Bus Voltage: 3.300 V
Current (direct): 0.007200 A
Power: 0.000045 W
```
**验证**: Power应该约等于 Voltage × Current
**问题**: 如果差异很大，检查功率寄存器配置

### 4. 转换时间分析
```
Config Analysis:
  VBUSCT: 140 µs
  VSHCT: 140 µs
```
**总转换时间**: VBUSCT + VSHCT = 280µs
**采样频率**: 最大约3.6kHz (1/280µs)

## 使用建议

### 1. 初始诊断
运行程序，等待10秒后查看完整分析输出

### 2. 持续监控
观察每30秒的详细分析，检查数据一致性

### 3. 问题定位
- **电流为0**: 检查Shunt_V寄存器是否为0
- **电流不准**: 比较两种电流计算方法
- **配置错误**: 检查Config寄存器各位设置
- **校准问题**: 验证Calibration寄存器值

### 4. 数据验证
```c
// 手动验证公式
float shunt_mv = (int16_t)shunt_reg * 0.0025f;
float current_calc = shunt_mv / 1000.0f / 0.75f;  // 分流电阻0.75Ω
float current_direct = (int16_t)current_reg * 0.0001f;  // Current_LSB=100µA

// 两个值应该接近
if (abs(current_calc - current_direct) > 0.001f) {
    // 可能有问题
}
```

通过这个完整的分析功能，可以快速定位INA226的任何配置或读取问题。
