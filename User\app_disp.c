
#include <stdlib.h>
#include <stdarg.h>
#include "app_disp.h"
#include "app_spi.h"
#include "st7789v.h"
#include "font.h"

void app_disp_init(void)
{
	ST7789V_SetDir(DISP_DIR);
	logi("app_disp_init \r\n");
}

/**
 * @brief 画点
 * @param
 * @param
 * @param
 * @return
 */
void lcdDrawPoint(uint16_t x, uint16_t y, uint16_t color)
{
	ST7789V_SetLcdXY(x, y, DISP_DIR);
	ST7789V_SPISendData2Bytes(color);
}

/**
 * @brief 清屏幕
 * @param
 * @param
 * @param
 * @return
 */
void lcdClear(uint16_t color) {
  uint16_t i, j;
  ST7789V_SetLcdAddress(0, 0, LCD_W - 1, LCD_H - 1, DISP_DIR);
  for (i = 0; i < LCD_H; i+=5) {
    ST7789V_SendColorLen(color,  LCD_W*5);
    // for (j = 0; j < LCD_W; j++) {
    //   ST7789V_SPISendData2Bytes(color);
    // }
  } 
}

/**
 * @brief 画线
 * @param
 * @param
 * @param
 * @return
 */
void lcdDrawLine(uint16_t x0, uint16_t y0, uint16_t x1, uint16_t y1, uint16_t color)
{
	int dx,	 // x轴上的距离
		dy,	 // y轴上的距离
		dx2, // 计算坐标的临时变量
		dy2,
		x_inc, // inc表示点的“生长方向” x_inc>1代表从左向右
		y_inc, // inc表示点的“生长方向” x_inc>1代表从上向下（左上角是坐标原点）
		error, // 由于坐标点只有整数，是离散的不是连续的，需要变量用于四舍五入的计算
		index;
	ST7789V_SPISendData2Bytes(color);
	dx = x1 - x0; // 计算x距离
	dy = y1 - y0; // 计算y距离

	if (dx >= 0)
	{
		x_inc = 1;
	}
	else
	{
		x_inc = -1;
		dx = -dx;
	}

	if (dy >= 0)
	{
		y_inc = 1;
	}
	else
	{
		y_inc = -1;
		dy = -dy;
	}

	dx2 = dx << 1; // 相当于乘以2，如此一来，四舍五入的误差就变成了不到1舍，大于1入
	dy2 = dy << 1;

	if (dx > dy) // x距离大于y距离，那么对于每个x轴上只有一个点，每个y轴可能只有半个点
	{
		error = dy2 - dx;
		for (index = 0; index <= dx; index++) // 要画的点数不会超过x距离
		{
			lcdDrawPoint(x0, y0, color);
			if (error >= 0) // 如果error>0 说明真实的y的误差>0.5了，实际上应该+1了
			{
				error -= dx2;
				y0 += y_inc; // 增加y坐标值
			}
			error += dy2;
			x0 += x_inc; // x坐标值每次画点后都递增1
		}
	}
	else
	{
		error = dx2 - dy;
		for (index = 0; index <= dy; index++)
		{
			lcdDrawPoint(x0, y0, color);
			if (error >= 0)
			{
				error -= dy2;
				x0 += x_inc;
			}
			error += dx2;
			y0 += y_inc;
		}
	}
}

/**
 * @brief 画矩形
 * @param
 * @param
 * @param
 * @return
 */
void lcdDrawRectangle(uint16_t x1, uint16_t y1, uint16_t x2, uint16_t y2, uint16_t Color)
{
	lcdDrawLine(x1, y1, x2, y1, Color); // H
	lcdDrawLine(x1, y1, x1, y2, Color); // V

	lcdDrawLine(x1 + 1, y2 - 1, x2, y2 - 1, Color); // H 加粗 多画一条线
	lcdDrawLine(x1, y2, x2, y2, Color);				// H
	lcdDrawLine(x2 - 1, y1 + 1, x2 - 1, y2, Color); // V
	lcdDrawLine(x2, y1, x2, y2, Color);				// V
}

/**
 * @brief 显示单个字符
 * @param x y起始坐标
 * @param 字符
 * @param 字体大小 16 24 32
 * @param mode 0非叠加 1叠加
 * @return
 */
void lcdDisplayChar(uint16_t x, uint16_t y, uint8_t char_s, u8 size, uint8_t mode, uint16_t Color)
{
	uint16_t temp, t1, t;
	uint16_t y0 = y;
	uint16_t csize = ((size / 8) + ((size % 8) ? 1 : 0)) * (size / 2);

	char_s = char_s - ' ';

	for (t = 0; t < csize; t++)
	{
		if (16 == size)
		{
			temp = asc2_1608[char_s][t];
		}
		else if (24 == size)
		{
			temp = asc2_2412[char_s][t];
		}
		else if (32 == size)
		{
			temp = asc2_3216[char_s][t];
		}
		else
		{
			return;
		}
		for (t1 = 0; t1 < 8; t1++)
		{
			if (temp & 0x80)
			{
				lcdDrawPoint(x, y, Color);
				// ST7789V_SPISendData2Bytes(Color);
			}
			else if (0 == mode)
			{
				lcdDrawPoint(x, y, BLACK);
				// ST7789V_SPISendData2Bytes(BLACK);
			}
			temp <<= 1;
			y++;

			if (y >= LCD_H)
			{
				return;
			}
			if ((y - y0) == size)
			{
				y = y0;
				x++;
				if (x >= LCD_W)
				{
					return;
				}
				break;
			}
		}
	}
}

/**
 * @brief 显示字符串
 * @param x y起始坐标
 * @param 显示区域
 * @param 字符串
 * @param 字体大小 16 24 32
 * @param mode 0非叠加 1叠加
 * @return
 */
void lcdDisPlayString(uint16_t x, uint16_t y, uint16_t width, uint16_t height, uint8_t *p, uint8_t size, uint8_t mode, uint16_t color)
{
	uint16_t x0 = x;
	width += x;
	height += y;

	ST7789V_SetLcdAddress(x, y, LCD_W - 1, LCD_H - 1, DISP_DIR);

	while ((*p <= '~') && (*p >= ' '))
	{
		if (x >= width)
		{
			x = x0;
			y += size;
		}

		if (y >= height)
		{
			break;
		}
		lcdDisplayChar(x, y, *p, size, mode, color);
		x += size / 2;
		p++;
	}
}