/**
 ******************************************************************************
 * @file    mod_ina226.c
 * <AUTHOR>
 * @version V1.0.0
 * @date    2025-07-02
 * @brief   INA226 power monitor module implementation
 ******************************************************************************
 */

/* Includes ------------------------------------------------------------------*/
#include "mod_ina226.h"
#include "app_util.h"
#include "app_uart.h"  // For logi function
#include <stddef.h>    // For NULL definition

/* Private typedef -----------------------------------------------------------*/
/* Private define ------------------------------------------------------------*/
#define INA226_I2C_TIMEOUT      1000    // I2C timeout in milliseconds
#define INA226_CALIBRATION_VAL  0x0444     // Recalculated for 750mΩ shunt, 100µA/bit
                                        // CAL = 0.00512 / (Current_LSB * R_SHUNT)
                                        // CAL = 0.00512 / (0.0001 * 0.750) = 68.27 = 0x0444

/* Private macro -------------------------------------------------------------*/
/* Private variables ---------------------------------------------------------*/
static bool ina226_initialized = false;

// Optimized acquisition variables
static bool ina226_optimized_mode = false;
static uint16_t samples_buf[INA226_BUFFER_COUNT][INA226_BUFFER_SIZE];
static volatile uint32_t wr_pos = 0;
static volatile uint32_t active_idx = 0;
static volatile bool flag_half_full = false;
static volatile bool new_data_available = false;

// Timer-triggered DMA variables
static volatile bool timer_dma_enabled = false;
static volatile uint32_t dma_transfer_count = 0;
static volatile uint32_t dma_success_count = 0;
static volatile uint32_t dma_error_count = 0;
static volatile bool dma_transfer_complete = false;
static uint16_t dma_read_buffer[2]; // Buffer for DMA read: [shunt_voltage, mask_register]

/* Private function prototypes -----------------------------------------------*/
static bool INA226_I2C_Init(void);
static bool INA226_WriteRegister(uint8_t reg, uint16_t value);
static bool INA226_ReadRegister(uint8_t reg, uint16_t *value);
static uint32_t INA226_GetTimeout(void);
static bool INA226_Timer_DMA_Init(void);
static void INA226_Start_Timer_DMA(void);
static void INA226_Stop_Timer_DMA(void);

// Optimized acquisition private functions
static bool INA226_EXTI_Init(void);
static bool INA226_DMA_Init(void);
static bool INA226_ConfigureAlert(void);

/* Private functions ---------------------------------------------------------*/

/**
 * @brief Get timeout value in system ticks
 * @retval Timeout value
 */
static uint32_t INA226_GetTimeout(void)
{
    // Assuming system tick is 1ms, return timeout in ticks
    return INA226_I2C_TIMEOUT;
}

/**
 * @brief Initialize I2C peripheral for INA226
 * @retval true if initialization successful, false otherwise
 */
static bool INA226_I2C_Init(void)
{
    GPIO_InitTypeDef GPIO_InitStructure;
    I2C_InitTypeDef I2C_InitStructure;
    
    // Enable GPIO and I2C clocks
    RCC_AHB1PeriphClockCmd(INA226_I2C_SCL_GPIO_CLK | INA226_I2C_SDA_GPIO_CLK, ENABLE);
    RCC_APB1PeriphClockCmd(INA226_I2C_CLK, ENABLE);
    
    // Reset I2C peripheral
    RCC_APB1PeriphResetCmd(INA226_I2C_CLK, ENABLE);
    RCC_APB1PeriphResetCmd(INA226_I2C_CLK, DISABLE);
    
    // Configure I2C pins
    GPIO_InitStructure.GPIO_Pin = INA226_I2C_SCL_PIN;
    GPIO_InitStructure.GPIO_Mode = GPIO_Mode_AF;
    GPIO_InitStructure.GPIO_Speed = GPIO_Speed_50MHz;
    GPIO_InitStructure.GPIO_OType = GPIO_OType_OD;
    GPIO_InitStructure.GPIO_PuPd = GPIO_PuPd_UP;
    GPIO_Init(INA226_I2C_SCL_GPIO_PORT, &GPIO_InitStructure);
    
    GPIO_InitStructure.GPIO_Pin = INA226_I2C_SDA_PIN;
    GPIO_Init(INA226_I2C_SDA_GPIO_PORT, &GPIO_InitStructure);
    
    // Connect pins to I2C alternate function
    GPIO_PinAFConfig(INA226_I2C_SCL_GPIO_PORT, INA226_I2C_SCL_SOURCE, INA226_I2C_SCL_AF);
    GPIO_PinAFConfig(INA226_I2C_SDA_GPIO_PORT, INA226_I2C_SDA_SOURCE, INA226_I2C_SDA_AF);
    
    // Configure I2C peripheral
    I2C_InitStructure.I2C_Mode = I2C_Mode_I2C;
    I2C_InitStructure.I2C_DutyCycle = I2C_DutyCycle_2;
    I2C_InitStructure.I2C_OwnAddress1 = 0x00;
    I2C_InitStructure.I2C_Ack = I2C_Ack_Enable;
    I2C_InitStructure.I2C_AcknowledgedAddress = I2C_AcknowledgedAddress_7bit;
    I2C_InitStructure.I2C_ClockSpeed = 400000; // 100kHz
    
    I2C_Init(INA226_I2C, &I2C_InitStructure);
    I2C_Cmd(INA226_I2C, ENABLE);
    
    return true;
}

/**
 * @brief Write a 16-bit value to INA226 register
 * @param reg: Register address
 * @param value: 16-bit value to write
 * @retval true if write successful, false otherwise
 */
static bool INA226_WriteRegister(uint8_t reg, uint16_t value)
{
    uint32_t timeout = INA226_GetTimeout();
    
    // Wait until I2C is not busy
    while (I2C_GetFlagStatus(INA226_I2C, I2C_FLAG_BUSY))
    {
        if (--timeout == 0) return false;
    }
    
    // Generate start condition
    I2C_GenerateSTART(INA226_I2C, ENABLE);
    
    // Wait for start condition
    timeout = INA226_GetTimeout();
    while (!I2C_CheckEvent(INA226_I2C, I2C_EVENT_MASTER_MODE_SELECT))
    {
        if (--timeout == 0) return false;
    }
    
    // Send device address for write
    I2C_Send7bitAddress(INA226_I2C, INA226_I2C_ADDR, I2C_Direction_Transmitter);
    
    // Wait for address acknowledgment
    timeout = INA226_GetTimeout();
    while (!I2C_CheckEvent(INA226_I2C, I2C_EVENT_MASTER_TRANSMITTER_MODE_SELECTED))
    {
        if (--timeout == 0) return false;
    }
    
    // Send register address
    I2C_SendData(INA226_I2C, reg);
    
    // Wait for data register empty
    timeout = INA226_GetTimeout();
    while (!I2C_CheckEvent(INA226_I2C, I2C_EVENT_MASTER_BYTE_TRANSMITTED))
    {
        if (--timeout == 0) return false;
    }
    
    // Send high byte
    I2C_SendData(INA226_I2C, (uint8_t)(value >> 8));
    
    // Wait for data register empty
    timeout = INA226_GetTimeout();
    while (!I2C_CheckEvent(INA226_I2C, I2C_EVENT_MASTER_BYTE_TRANSMITTED))
    {
        if (--timeout == 0) return false;
    }
    
    // Send low byte
    I2C_SendData(INA226_I2C, (uint8_t)(value & 0xFF));
    
    // Wait for data register empty
    timeout = INA226_GetTimeout();
    while (!I2C_CheckEvent(INA226_I2C, I2C_EVENT_MASTER_BYTE_TRANSMITTED))
    {
        if (--timeout == 0) return false;
    }
    
    // Generate stop condition
    I2C_GenerateSTOP(INA226_I2C, ENABLE);
    
    return true;
}

/**
 * @brief Read a 16-bit value from INA226 register
 * @param reg: Register address
 * @param value: Pointer to store the read value
 * @retval true if read successful, false otherwise
 */
static bool INA226_ReadRegister(uint8_t reg, uint16_t *value)
{
    uint32_t timeout = INA226_GetTimeout();
    uint8_t high_byte, low_byte;
    
    if (value == NULL) return false;
    
    // Wait until I2C is not busy
    while (I2C_GetFlagStatus(INA226_I2C, I2C_FLAG_BUSY))
    {
        if (--timeout == 0) return false;
    }
    
    // Generate start condition
    I2C_GenerateSTART(INA226_I2C, ENABLE);
    
    // Wait for start condition
    timeout = INA226_GetTimeout();
    while (!I2C_CheckEvent(INA226_I2C, I2C_EVENT_MASTER_MODE_SELECT))
    {
        if (--timeout == 0) return false;
    }
    
    // Send device address for write
    I2C_Send7bitAddress(INA226_I2C, INA226_I2C_ADDR, I2C_Direction_Transmitter);
    
    // Wait for address acknowledgment
    timeout = INA226_GetTimeout();
    while (!I2C_CheckEvent(INA226_I2C, I2C_EVENT_MASTER_TRANSMITTER_MODE_SELECTED))
    {
        if (--timeout == 0) return false;
    }
    
    // Send register address
    I2C_SendData(INA226_I2C, reg);
    
    // Wait for data register empty
    timeout = INA226_GetTimeout();
    while (!I2C_CheckEvent(INA226_I2C, I2C_EVENT_MASTER_BYTE_TRANSMITTED))
    {
        if (--timeout == 0) return false;
    }
    
    // Generate restart condition
    I2C_GenerateSTART(INA226_I2C, ENABLE);
    
    // Wait for start condition
    timeout = INA226_GetTimeout();
    while (!I2C_CheckEvent(INA226_I2C, I2C_EVENT_MASTER_MODE_SELECT))
    {
        if (--timeout == 0) return false;
    }
    
    // Send device address for read
    I2C_Send7bitAddress(INA226_I2C, INA226_I2C_ADDR, I2C_Direction_Receiver);
    
    // Wait for address acknowledgment
    timeout = INA226_GetTimeout();
    while (!I2C_CheckEvent(INA226_I2C, I2C_EVENT_MASTER_RECEIVER_MODE_SELECTED))
    {
        if (--timeout == 0) return false;
    }
    
    // Enable ACK for first byte
    I2C_AcknowledgeConfig(INA226_I2C, ENABLE);
    
    // Wait for data received
    timeout = INA226_GetTimeout();
    while (!I2C_CheckEvent(INA226_I2C, I2C_EVENT_MASTER_BYTE_RECEIVED))
    {
        if (--timeout == 0) return false;
    }
    
    // Read high byte
    high_byte = I2C_ReceiveData(INA226_I2C);
    
    // Disable ACK for last byte
    I2C_AcknowledgeConfig(INA226_I2C, DISABLE);
    
    // Generate stop condition
    I2C_GenerateSTOP(INA226_I2C, ENABLE);
    
    // Wait for data received
    timeout = INA226_GetTimeout();
    while (!I2C_CheckEvent(INA226_I2C, I2C_EVENT_MASTER_BYTE_RECEIVED))
    {
        if (--timeout == 0) return false;
    }
    
    // Read low byte
    low_byte = I2C_ReceiveData(INA226_I2C);
    
    // Combine bytes
    *value = ((uint16_t)high_byte << 8) | low_byte;
    
    return true;
}

/* Public functions ----------------------------------------------------------*/

/**
 * @brief Initialize INA226 module
 * @retval true if initialization successful, false otherwise
 */
bool INA226_Init(void)
{
    uint16_t manuf_id, die_id;
    
    // Initialize I2C
    if (!INA226_I2C_Init())
    {
        return false;
    }
    
    // Small delay after I2C initialization
    s_delay_ms(10);
    
    // Check if device is present by reading manufacturer ID
    if (!INA226_ReadRegister(INA226_REG_MANUF_ID, &manuf_id))
    {
        return false;
    }
    
    // Check manufacturer ID (should be 0x5449 for TI)
    if (manuf_id != 0x5449)
    {
        return false;
    }
    
    // Read die ID (should be 0x2260 for INA226)
    if (!INA226_ReadRegister(INA226_REG_DIE_ID, &die_id))
    {
        return false;
    }
    
    if (die_id != 0x2260)
    {
        return false;
    }
    
    // Reset device to default settings
    if (!INA226_Reset())
    {
        return false;
    }
    
    // Configure device with default settings
    if (!INA226_WriteRegister(INA226_REG_CONFIG, INA226_CONFIG_DEFAULT))
    {
        return false;
    }
    
    // Set calibration value
    if (!INA226_WriteRegister(INA226_REG_CALIBRATION, INA226_CALIBRATION_VAL))
    {
        return false;
    }
    
    ina226_initialized = true;
    return true;
}

/**
 * @brief Read all measurement data from INA226
 * @param data: Pointer to data structure to store results
 * @retval true if read successful, false otherwise
 */
bool INA226_ReadData(INA226_Data_t *data)
{
    uint16_t raw_value;
    int16_t signed_value;

    if (!ina226_initialized || data == NULL)
    {
        return false;
    }

    // Read shunt voltage
    if (!INA226_ReadRegister(INA226_REG_SHUNT_V, &raw_value))
    {
        return false;
    }
    signed_value = (int16_t)raw_value;
    data->shunt_voltage_mV = signed_value * 0.0025f; // 2.5µV per bit

    // Read bus voltage
    if (!INA226_ReadRegister(INA226_REG_BUS_V, &raw_value))
    {
        return false;
    }
    data->voltage_V = raw_value * 0.00125f; // 1.25mV per bit

    // Read current
    if (!INA226_ReadRegister(INA226_REG_CURRENT, &raw_value))
    {
        return false;
    }
    signed_value = (int16_t)raw_value;
    data->current_A = signed_value * INA226_CURRENT_LSB_A;

    // Read power
    if (!INA226_ReadRegister(INA226_REG_POWER, &raw_value))
    {
        return false;
    }
    data->power_W = raw_value * INA226_POWER_LSB_W;

    return true;
}

/**
 * @brief Read bus voltage from INA226
 * @retval Bus voltage in volts, or -1.0f on error
 */
float INA226_ReadBusVoltage(void)
{
    uint16_t raw_value;

    if (!ina226_initialized)
    {
        return -1.0f;
    }

    if (!INA226_ReadRegister(INA226_REG_BUS_V, &raw_value))
    {
        return -1.0f;
    }

    return raw_value * 0.00125f; // 1.25mV per bit
}

/**
 * @brief Read shunt voltage from INA226
 * @retval Shunt voltage in millivolts, or -1.0f on error
 */
float INA226_ReadShuntVoltage(void)
{
    uint16_t raw_value;
    int16_t signed_value;

    if (!ina226_initialized)
    {
        return -1.0f;
    }

    if (!INA226_ReadRegister(INA226_REG_SHUNT_V, &raw_value))
    {
        return -1.0f;
    }

    signed_value = (int16_t)raw_value;
    return signed_value * 0.0025f; // 2.5µV per bit
}

/**
 * @brief Read current from INA226
 * @retval Current in amperes, or -1.0f on error
 */
float INA226_ReadCurrent(void)
{
    uint16_t raw_value;
    int16_t signed_value;

    if (!ina226_initialized)
    {
        return -1.0f;
    }

    if (!INA226_ReadRegister(INA226_REG_CURRENT, &raw_value))
    {
        return -1.0f;
    }

    signed_value = (int16_t)raw_value;
    return signed_value * INA226_CURRENT_LSB_A;
}

/**
 * @brief Read power from INA226
 * @retval Power in watts, or -1.0f on error
 */
float INA226_ReadPower(void)
{
    uint16_t raw_value;

    if (!ina226_initialized)
    {
        return -1.0f;
    }

    if (!INA226_ReadRegister(INA226_REG_POWER, &raw_value))
    {
        return -1.0f;
    }

    return raw_value * INA226_POWER_LSB_W;
}

/**
 * @brief Configure INA226 settings
 * @param config: Pointer to configuration structure
 * @retval true if configuration successful, false otherwise
 */
bool INA226_Configure(const INA226_Config_t *config)
{
    uint16_t config_reg;

    if (!ina226_initialized || config == NULL)
    {
        return false;
    }

    config_reg = config->avg_mode | config->bus_conv_time |
                 config->shunt_conv_time | config->mode;

    return INA226_WriteRegister(INA226_REG_CONFIG, config_reg);
}

/**
 * @brief Reset INA226 to default settings
 * @retval true if reset successful, false otherwise
 */
bool INA226_Reset(void)
{
    // Write reset bit to configuration register
    if (!INA226_WriteRegister(INA226_REG_CONFIG, INA226_CONFIG_RESET))
    {
        return false;
    }

    // Wait for reset to complete
    s_delay_ms(10);

    return true;
}

/**
 * @brief Check if INA226 is present and responding
 * @retval true if device is present, false otherwise
 */
bool INA226_IsPresent(void)
{
    uint16_t manuf_id;

    // Try to read manufacturer ID
    if (!INA226_ReadRegister(INA226_REG_MANUF_ID, &manuf_id))
    {
        return false;
    }

    // Check if manufacturer ID matches TI (0x5449)
    return (manuf_id == 0x5449);
}

/**
 * @brief 显示INA226配置信息
 * @retval None
 */
void INA226_PrintConfig(void)
{
    logi("=== INA226 Configuration ===\r\n");
    logi("Shunt Resistance: %.3f Ohms (R750)\r\n", INA226_SHUNT_RESISTANCE_OHMS);
    logi("Current LSB: %.1f µA/bit\r\n", INA226_CURRENT_LSB_A * 1000000.0f);
    logi("Power LSB: %.3f mW/bit\r\n", INA226_POWER_LSB_W * 1000.0f);
    logi("Calibration Value: 0x%04X\r\n", INA226_CALIBRATION_VAL);
    logi("Max Current Range: ±%.3f A\r\n", (32767 * INA226_CURRENT_LSB_A));
    logi("Current Resolution: %.1f µA\r\n", INA226_CURRENT_LSB_A * 1000000.0f);
    logi("============================\r\n");
}

/* Optimized acquisition functions -------------------------------------------*/

/**
 * @brief Initialize EXTI for INA226 ALERT pin
 * @retval true if initialization successful, false otherwise
 */
static bool INA226_EXTI_Init(void)
{
    GPIO_InitTypeDef GPIO_InitStructure;
    EXTI_InitTypeDef EXTI_InitStructure;
    NVIC_InitTypeDef NVIC_InitStructure;

    logi("Initializing INA226 EXTI (PB8)...\r\n");

    // Enable GPIO clock
    RCC_AHB1PeriphClockCmd(INA226_ALERT_GPIO_CLK, ENABLE);
    RCC_APB2PeriphClockCmd(RCC_APB2Periph_SYSCFG, ENABLE);
    logi("GPIO and SYSCFG clocks enabled.\r\n");

    // Configure ALERT pin as input with pull-up
    GPIO_InitStructure.GPIO_Pin = INA226_ALERT_PIN;
    GPIO_InitStructure.GPIO_Mode = GPIO_Mode_IN;
    GPIO_InitStructure.GPIO_PuPd = GPIO_PuPd_UP;
    // GPIO_InitStructure.GPIO_Mode  = GPIO_Mode_AF;
    // GPIO_InitStructure.GPIO_OType = GPIO_OType_OD;   // 开漏
    // GPIO_InitStructure.GPIO_PuPd  = GPIO_PuPd_UP;    // 内部上拉
    // GPIO_InitStructure.GPIO_Speed = GPIO_Speed_50MHz;/* 或 100MHz */
    GPIO_Init(INA226_ALERT_GPIO_PORT, &GPIO_InitStructure);
    logi("PB8 configured as input with pull-up.\r\n");

    // Read initial pin state
    uint8_t pin_state = GPIO_ReadInputDataBit(INA226_ALERT_GPIO_PORT, INA226_ALERT_PIN);
    logi("Initial PB8 pin state: %d\r\n", pin_state);

    // Connect EXTI line to GPIO pin
    SYSCFG_EXTILineConfig(INA226_ALERT_EXTI_PORT, INA226_ALERT_EXTI_PIN);
    logi("EXTI Line8 connected to GPIOB Pin8.\r\n");

    // Configure EXTI line
    EXTI_InitStructure.EXTI_Line = INA226_ALERT_EXTI_LINE;
    EXTI_InitStructure.EXTI_Mode = EXTI_Mode_Interrupt;
    EXTI_InitStructure.EXTI_Trigger = EXTI_Trigger_Falling; // INA226 ALERT is active low 
    EXTI_InitStructure.EXTI_LineCmd = ENABLE;
    EXTI_Init(&EXTI_InitStructure);
    logi("EXTI Line8 configured for falling edge trigger.\r\n");

    // Clear any pending EXTI interrupt
    EXTI_ClearITPendingBit(INA226_ALERT_EXTI_LINE);

    // Configure NVIC
    NVIC_InitStructure.NVIC_IRQChannel = INA226_ALERT_IRQn;
    NVIC_InitStructure.NVIC_IRQChannelPreemptionPriority = 5;
    NVIC_InitStructure.NVIC_IRQChannelSubPriority = 0;
    NVIC_InitStructure.NVIC_IRQChannelCmd = ENABLE;
    NVIC_Init(&NVIC_InitStructure);
    logi("NVIC configured for EXTI9_5_IRQn with priority 5.\r\n");

    logi("INA226 EXTI initialization completed.\r\n");
    return true;
}

/**
 * @brief Initialize DMA for I2C1 reception
 * @retval true if initialization successful, false otherwise
 */
static bool INA226_DMA_Init(void)
{
    DMA_InitTypeDef DMA_InitStructure;
    NVIC_InitTypeDef NVIC_InitStructure;

    // Enable DMA clock
    RCC_AHB1PeriphClockCmd(INA226_DMA_CLK, ENABLE);

    // Reset DMA stream
    DMA_DeInit(INA226_DMA_STREAM);

    // Wait for DMA stream to be disabled
    while (DMA_GetCmdStatus(INA226_DMA_STREAM) != DISABLE);

    // Configure DMA stream
    DMA_InitStructure.DMA_Channel = INA226_DMA_CHANNEL;
    DMA_InitStructure.DMA_PeripheralBaseAddr = (uint32_t)&INA226_I2C->DR;
    DMA_InitStructure.DMA_Memory0BaseAddr = (uint32_t)&samples_buf[0][0];
    DMA_InitStructure.DMA_DIR = DMA_DIR_PeripheralToMemory;
    DMA_InitStructure.DMA_BufferSize = 2; // 2 bytes for shunt voltage register
    DMA_InitStructure.DMA_PeripheralInc = DMA_PeripheralInc_Disable;
    DMA_InitStructure.DMA_MemoryInc = DMA_MemoryInc_Enable;
    DMA_InitStructure.DMA_PeripheralDataSize = DMA_PeripheralDataSize_Byte;
    DMA_InitStructure.DMA_MemoryDataSize = DMA_MemoryDataSize_Byte;
    DMA_InitStructure.DMA_Mode = DMA_Mode_Normal;
    DMA_InitStructure.DMA_Priority = DMA_Priority_High;
    DMA_InitStructure.DMA_FIFOMode = DMA_FIFOMode_Disable;
    DMA_InitStructure.DMA_FIFOThreshold = DMA_FIFOThreshold_1QuarterFull;
    DMA_InitStructure.DMA_MemoryBurst = DMA_MemoryBurst_Single;
    DMA_InitStructure.DMA_PeripheralBurst = DMA_PeripheralBurst_Single;

    DMA_Init(INA226_DMA_STREAM, &DMA_InitStructure);

    // Enable DMA transfer complete interrupt
    DMA_ITConfig(INA226_DMA_STREAM, DMA_IT_TC, ENABLE);

    // Configure NVIC for DMA
    NVIC_InitStructure.NVIC_IRQChannel = INA226_DMA_IRQn;
    NVIC_InitStructure.NVIC_IRQChannelPreemptionPriority = 6;
    NVIC_InitStructure.NVIC_IRQChannelSubPriority = 0;
    NVIC_InitStructure.NVIC_IRQChannelCmd = ENABLE;
    NVIC_Init(&NVIC_InitStructure);

    // Enable I2C DMA requests
    I2C_DMACmd(INA226_I2C, ENABLE);

    return true;
}

/**
 * @brief Configure INA226 ALERT functionality
 * @retval true if configuration successful, false otherwise
 */
/**
 * @brief Configure INA226 ALERT functionality
 * @retval true if configuration successful, false otherwise
 */
static bool INA226_ConfigureAlert(void)
{
    uint16_t read_value;

    logi("Configuring INA226 ALERT functionality...\r\n");

    /*----------------------------------------------------------------------
     * 1) 设定快速连续转换：
     *    AVG = 1  | VBUSCT = 140 µs | VSHCT = 140 µs | MODE = Shunt+Bus continuous
     *    ==> 0x0127
     *--------------------------------------------------------------------*/
    /* === 把 0x4007 改成纯快速配置 1-sample, 140µs === */
    uint16_t config_value = 0x0127;  // AVG=1, VBUSCT=140µs, VSHCT=140µs, MODE=111

    if (!INA226_WriteRegister(INA226_REG_CONFIG, config_value)) {
        logi("ERROR: Failed to write Config register\r\n");
        return false;
    }
    logi("Config register set to 0x%04X (1-sample, 140 µs)\r\n", config_value);

    /*----------------------------------------------------------------------
     * 2) 只打开 CNVR（Conversion Ready）中断，非锁存、低电平有效
     *    Mask/Enable bit10 = 1，其他保持 0  ==> 0x0400
     *--------------------------------------------------------------------*/
    const uint16_t mask_enable = 0x0400;

    if (!INA226_WriteRegister(INA226_REG_MASK_ENABLE, mask_enable)) {
        logi("ERROR: Failed to write Mask/Enable register\r\n");
        return false;
    }
    logi("Mask/Enable register set to 0x%04X (CNVR enabled)\r\n", mask_enable);

    /* Alert Limit 与 CNVR 无关，这里写个最大值占位 */
    if (!INA226_WriteRegister(INA226_REG_ALERT_LIMIT, 0x7FFF)) {
        logi("ERROR: Failed to write Alert Limit register\r\n");
        return false;
    }
    
    /*----------------------------------------------------------------------
     * 3) ★关键★：第一次转换完成时，ALERT 会被拉低并把 CVRF=1。
     *           立刻读一次任意寄存器可清除 CVRF，让 ALERT 释放为高电平，
     *           之后每次转换都会输出一个窄脉冲。
     *--------------------------------------------------------------------*/
    if (!INA226_ReadRegister(INA226_REG_MASK_ENABLE, &read_value)) {
        logi("ERROR: Failed to read back Mask/Enable register\r\n");
        return false;
    }
    logi("CVRF cleared, Mask/Enable now = 0x%04X\r\n", read_value);

    /*----------------------------------------------------------------------
     * 4) 完成
     *--------------------------------------------------------------------*/
    logi("INA226 ALERT configuration completed successfully.\r\n");
    return true;
}

/**
 * @brief Initialize INA226 with EXTI+DMA optimization
 * @retval true if initialization successful, false otherwise
 */
bool INA226_InitOptimized(void)
{
    logi("=== INA226 Timer-based Init ===\r\n");

    ina226_optimized_mode = true;

    // First initialize basic INA226
    logi("Step 1: Basic INA226 initialization...\r\n");
    if (!INA226_Init())
    {
        logi("ERROR: Basic INA226 initialization failed!\r\n");
        return false;
    }
    logi("Step 1: Basic INA226 initialization completed.\r\n");

    // Initialize Timer for automatic reading
    logi("Step 2: Timer initialization...\r\n");
    if (!INA226_Timer_DMA_Init())
    {
        logi("ERROR: Timer initialization failed!\r\n");
        return false;
    }
    logi("Step 2: Timer initialization completed.\r\n");

    // Configure ALERT functionality (optional, for status monitoring)
    logi("Step 3: ALERT configuration...\r\n");
    if (!INA226_ConfigureAlert())
    {
        logi("ERROR: ALERT configuration failed!\r\n");
        return false;
    }
    logi("Step 3: ALERT configuration completed.\r\n");

    // Initialize ping-pong buffer variables
    logi("Step 4: Buffer initialization...\r\n");
    wr_pos = 0;
    active_idx = 0;
    flag_half_full = false;
    new_data_available = false;

    // Reset counters
    dma_transfer_count = 0;
    dma_success_count = 0;
    dma_error_count = 0;
    dma_transfer_complete = false;
    logi("Step 4: Buffer initialization completed.\r\n");

    logi("=== INA226 Timer-based mode initialized successfully ===\r\n");

    return true;
}

/**
 * @brief Start optimized data acquisition using Timer+DMA
 * @retval true if start successful, false otherwise
 */
bool INA226_StartOptimizedAcquisition(void)
{
    if (!ina226_optimized_mode)
    {
        logi("INA226 not in optimized mode!\r\n");
        return false;
    }

    // Reset buffer state
    wr_pos = 0;
    active_idx = 0;
    flag_half_full = false;
    new_data_available = false;
    dma_transfer_count = 0;

    // Start Timer+DMA automatic reading
    INA226_Start_Timer_DMA();

    logi("INA226 Timer+DMA acquisition started.\r\n");
    return true;
}

/**
 * @brief Stop optimized data acquisition
 * @retval None
 */
void INA226_StopOptimizedAcquisition(void)
{
    if (!ina226_optimized_mode)
    {
        return;
    }

    // Stop Timer+DMA
    INA226_Stop_Timer_DMA();

    logi("INA226 Timer+DMA acquisition stopped.\r\n");
}

/**
 * @brief Get latest shunt voltage data from ping-pong buffer
 * @param buffer: Pointer to buffer to store data
 * @param count: Number of samples to read (max INA226_BUFFER_SIZE)
 * @retval Number of samples actually read
 */
uint32_t INA226_GetLatestData(uint16_t *buffer, uint32_t count)
{
    if (!ina226_optimized_mode || buffer == NULL || count == 0)
    {
        return 0;
    }

    if (!flag_half_full)
    {
        return 0; // No data available yet
    }

    // Determine which buffer to read from (the inactive one)
    uint32_t read_idx = active_idx ^ 1;
    uint32_t samples_to_read = (count > INA226_BUFFER_SIZE) ? INA226_BUFFER_SIZE : count;

    // Copy data from the completed buffer
    for (uint32_t i = 0; i < samples_to_read; i++)
    {
        buffer[i] = samples_buf[read_idx][i];
    }

    // Clear the flag after reading
    flag_half_full = false;
    new_data_available = false;

    return samples_to_read;
}

/**
 * @brief Initialize true DMA hardware automation for INA226 reading
 * @retval true if initialization successful, false otherwise
 */
static bool INA226_True_DMA_Init(void)
{
    DMA_InitTypeDef DMA_InitStructure;
    NVIC_InitTypeDef NVIC_InitStructure;

    logi("Initializing TRUE DMA hardware automation...\r\n");

    // Enable DMA1 clock
    RCC_AHB1PeriphClockCmd(RCC_AHB1Periph_DMA1, ENABLE);

    // Configure DMA1 Stream0 for I2C1 RX (hardware automation)
    DMA_DeInit(DMA1_Stream0);
    DMA_InitStructure.DMA_Channel = DMA_Channel_1;
    DMA_InitStructure.DMA_PeripheralBaseAddr = (uint32_t)&I2C1->DR;
    DMA_InitStructure.DMA_Memory0BaseAddr = (uint32_t)dma_read_buffer;
    DMA_InitStructure.DMA_DIR = DMA_DIR_PeripheralToMemory;
    DMA_InitStructure.DMA_BufferSize = 2; // Read 2 bytes (shunt voltage)
    DMA_InitStructure.DMA_PeripheralInc = DMA_PeripheralInc_Disable;
    DMA_InitStructure.DMA_MemoryInc = DMA_MemoryInc_Enable;
    DMA_InitStructure.DMA_PeripheralDataSize = DMA_PeripheralDataSize_Byte;
    DMA_InitStructure.DMA_MemoryDataSize = DMA_MemoryDataSize_Byte;
    DMA_InitStructure.DMA_Mode = DMA_Mode_Circular; // Continuous operation
    DMA_InitStructure.DMA_Priority = DMA_Priority_High;
    DMA_InitStructure.DMA_FIFOMode = DMA_FIFOMode_Disable;
    DMA_Init(DMA1_Stream0, &DMA_InitStructure);

    // Enable DMA Transfer Complete interrupt
    DMA_ITConfig(DMA1_Stream0, DMA_IT_TC, ENABLE);

    // Configure DMA interrupt
    NVIC_InitStructure.NVIC_IRQChannel = DMA1_Stream0_IRQn;
    NVIC_InitStructure.NVIC_IRQChannelPreemptionPriority = 1; // High priority
    NVIC_InitStructure.NVIC_IRQChannelSubPriority = 0;
    NVIC_InitStructure.NVIC_IRQChannelCmd = ENABLE;
    NVIC_Init(&NVIC_InitStructure);

    logi("TRUE DMA hardware automation initialized.\r\n");
    return true;
}

/**
 * @brief Initialize Timer for automatic INA226 reading (current approach)
 * @retval true if initialization successful, false otherwise
 */
static bool INA226_Timer_DMA_Init(void)
{
    TIM_TimeBaseInitTypeDef TIM_TimeBaseStructure;
    NVIC_InitTypeDef NVIC_InitStructure;

    logi("Initializing Timer for automatic INA226 reading...\r\n");

    // Enable Timer2 clock
    RCC_APB1PeriphClockCmd(RCC_APB1Periph_TIM2, ENABLE);

    // Configure Timer2 for 100Hz trigger (10ms interval) - better for UI performance
    TIM_TimeBaseStructure.TIM_Period = 10000 - 1;       // 10ms at 1MHz
    TIM_TimeBaseStructure.TIM_Prescaler = 84 - 1;       // 84MHz/84 = 1MHz
    TIM_TimeBaseStructure.TIM_ClockDivision = TIM_CKD_DIV1;
    TIM_TimeBaseStructure.TIM_CounterMode = TIM_CounterMode_Up;
    TIM_TimeBaseInit(TIM2, &TIM_TimeBaseStructure);

    // Configure Timer2 to trigger DMA directly (true hardware automation)
    TIM_DMACmd(TIM2, TIM_DMA_Update, ENABLE);

    // Note: Timer interrupt is NOT enabled - DMA handles everything automatically
    // No CPU intervention needed for data transfer!

    logi("Timer2 initialized for 100Hz automatic triggering (UI-friendly).\r\n");
    return true;
}

/**
 * @brief Start Timer-triggered automatic reading
 * @retval None
 */
static void INA226_Start_Timer_DMA(void)
{
    logi("Starting Timer2 for automatic INA226 reading...\r\n");

    timer_dma_enabled = true;

    // Reset counters
    dma_transfer_count = 0;
    dma_success_count = 0;
    dma_error_count = 0;

    // Start Timer2
    TIM_Cmd(TIM2, ENABLE);

    logi("Timer2 started, automatic reading enabled.\r\n");
}

/**
 * @brief Stop Timer-triggered automatic reading
 * @retval None
 */
static void INA226_Stop_Timer_DMA(void)
{
    logi("Stopping Timer2 automatic reading...\r\n");

    timer_dma_enabled = false;

    // Stop Timer2
    TIM_Cmd(TIM2, DISABLE);

    logi("Timer2 stopped, automatic reading disabled.\r\n");
}

/**
 * @brief Get direct access to DMA buffer for chart updates
 * @param buffer_ptr Pointer to receive buffer address
 * @param buffer_size Pointer to receive buffer size
 * @param write_pos Pointer to receive current write position
 * @retval true if access granted, false otherwise
 */
bool INA226_GetDirectBufferAccess(uint16_t **buffer_ptr, uint32_t *buffer_size, uint32_t *write_pos)
{
    if (!ina226_optimized_mode || buffer_ptr == NULL || buffer_size == NULL || write_pos == NULL) {
        return false;
    }

    // Return pointer to the current active buffer
    *buffer_ptr = samples_buf[active_idx];
    *buffer_size = INA226_BUFFER_SIZE;
    *write_pos = wr_pos;

    // Debug: Log buffer access occasionally
    static uint32_t access_debug_counter = 0;
    if ((++access_debug_counter % 200) == 0) {
        logi("Buffer Access: active_idx=%lu, wr_pos=%lu, buffer_ptr=0x%08X\r\n",
             active_idx, wr_pos, (uint32_t)*buffer_ptr);

        // Show some recent samples from the buffer
        uint32_t start_pos = (wr_pos >= 5) ? wr_pos - 5 : 0;
        logi("Recent samples: ");
        for (uint32_t i = start_pos; i < wr_pos && i < start_pos + 5; i++) {
            logi("0x%04X ", samples_buf[active_idx][i]);
        }
        logi("\r\n");
    }

    return true;
}

/**
 * @brief Get DMA transfer statistics
 * @param total_transfers Pointer to store total transfer count
 * @param success_transfers Pointer to store successful transfer count
 * @param error_transfers Pointer to store error transfer count
 * @retval true if DMA is active, false otherwise
 */
bool INA226_GetDMAStatus(uint32_t *total_transfers, uint32_t *success_transfers, uint32_t *error_transfers)
{
    if (total_transfers) *total_transfers = dma_transfer_count;
    if (success_transfers) *success_transfers = dma_success_count;
    if (error_transfers) *error_transfers = dma_error_count;

    return timer_dma_enabled;
}

/**
 * @brief Check if DMA transfer was completed recently
 * @retval true if transfer completed since last check, false otherwise
 */
bool INA226_IsDMATransferComplete(void)
{
    if (dma_transfer_complete) {
        dma_transfer_complete = false; // Clear flag after reading
        return true;
    }
    return false;
}

/**
 * @brief Read and print all INA226 registers for analysis
 * @retval true if all reads successful, false otherwise
 */
bool INA226_PrintAllRegisters(void)
{
    uint16_t reg_values[8];
    const char* reg_names[] = {
        "Config", "Shunt_V", "Bus_V", "Power",
        "Current", "Calibration", "Mask/Enable", "Alert_Limit"
    };
    uint8_t reg_addresses[] = {
        0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07
    };

    logi("=== INA226 Complete Register Dump ===\r\n");

    bool all_success = true;
    for (int i = 0; i < 8; i++) {
        if (INA226_ReadRegister(reg_addresses[i], &reg_values[i])) {
            logi("Reg[0x%02X] %-12s: 0x%04X (%d)\r\n",
                 reg_addresses[i], reg_names[i], reg_values[i], reg_values[i]);
        } else {
            logi("Reg[0x%02X] %-12s: READ FAILED\r\n", reg_addresses[i], reg_names[i]);
            all_success = false;
        }
    }

    logi("=====================================\r\n");

    // Detailed analysis
    logi("=== Register Analysis ===\r\n");

    // Config register analysis (0x00)
    if (reg_values[0] != 0xFFFF) {
        uint16_t config = reg_values[0];
        logi("Config Analysis:\r\n");
        logi("  Reset: %s\r\n", (config & 0x8000) ? "YES" : "NO");
        logi("  Avg: %d samples\r\n", 1 << ((config >> 9) & 0x07));
        logi("  VBUSCT: %d µs\r\n", 140 + 204 * ((config >> 6) & 0x07));
        logi("  VSHCT: %d µs\r\n", 140 + 204 * ((config >> 3) & 0x07));
        logi("  Mode: %d (%s)\r\n", config & 0x07,
             (config & 0x07) == 7 ? "Continuous" : "Other");
    }

    // Measurement data analysis
    if (reg_values[1] != 0xFFFF) {
        int16_t shunt_raw = (int16_t)reg_values[1];
        float shunt_mv = shunt_raw * 0.0025f;
        float current_calc = (shunt_mv / 1000.0f) / 0.75f;
        logi("Shunt Voltage: %.3f mV (Current: %.6f A)\r\n", shunt_mv, current_calc);
    }

    if (reg_values[2] != 0xFFFF) {
        float bus_v = (reg_values[2] >> 3) * 0.00125f;
        logi("Bus Voltage: %.3f V\r\n", bus_v);
    }

    if (reg_values[3] != 0xFFFF) {
        float power_w = reg_values[3] * 0.025f * 0.0001f; // 25mW * Current_LSB
        logi("Power: %.6f W\r\n", power_w);
    }

    if (reg_values[4] != 0xFFFF) {
        int16_t current_raw = (int16_t)reg_values[4];
        float current_direct = current_raw * 0.0001f; // Current_LSB = 100µA
        logi("Current (direct): %.6f A\r\n", current_direct);
    }

    logi("========================\r\n");

    return all_success;
}

/**
 * @brief Verify INA226 register settings
 * @retval true if registers are correct, false otherwise
 */
bool INA226_VerifyRegisters(void)
{
    uint16_t config, calibration, mask;

    logi("=== INA226 Register Verification ===\r\n");

    // Read and verify config register
    if (INA226_ReadRegister(INA226_REG_CONFIG, &config)) {
        logi("Config Register: 0x%04X (Expected: 0x0127)\r\n", config);
    } else {
        logi("ERROR: Failed to read Config register\r\n");
        return false;
    }

    // Read and verify calibration register
    if (INA226_ReadRegister(INA226_REG_CALIBRATION, &calibration)) {
        logi("Calibration Register: 0x%04X (Expected: 0x%04X)\r\n", calibration, INA226_CALIBRATION_VAL);
    } else {
        logi("ERROR: Failed to read Calibration register\r\n");
        return false;
    }

    // Read mask register
    if (INA226_ReadRegister(INA226_REG_MASK_ENABLE, &mask)) {
        logi("Mask Register: 0x%04X\r\n", mask);
    } else {
        logi("ERROR: Failed to read Mask register\r\n");
        return false;
    }

    logi("===================================\r\n");

    return (config == 0x0127 && calibration == INA226_CALIBRATION_VAL);
}

/**
 * @brief Get Timer status for debugging
 * @retval true if Timer is running, false otherwise
 */
bool INA226_GetTimerStatus(void)
{
    bool timer_enabled = (TIM2->CR1 & TIM_CR1_CEN) != 0;
    uint32_t timer_count = TIM2->CNT;

    logi("Timer2 Status: Enabled=%s, Count=%lu, DMA_Enabled=%s\r\n",
         timer_enabled ? "YES" : "NO",
         timer_count,
         timer_dma_enabled ? "YES" : "NO");

    return timer_enabled;
}

/**
 * @brief Check buffer data integrity
 * @retval true if buffer data looks valid, false if issues detected
 */
bool INA226_CheckBufferIntegrity(void)
{
    uint32_t zero_count = 0;
    uint32_t valid_count = 0;

    // Check current active buffer
    for (uint32_t i = 0; i < wr_pos && i < INA226_BUFFER_SIZE; i++) {
        uint16_t sample = samples_buf[active_idx][i];
        if (sample == 0x0000 || sample == 0xFFFF) {
            zero_count++;
        } else {
            valid_count++;
        }
    }

    if (zero_count > 0) {
        logi("Buffer integrity issue: %lu zeros, %lu valid in buffer %lu (pos=%lu)\r\n",
             zero_count, valid_count, active_idx, wr_pos);
        return false;
    }

    return true;
}

/**
 * @brief Check if new data is available in ping-pong buffer
 * @retval true if new data available, false otherwise
 */
bool INA226_IsNewDataAvailable(void)
{
    return new_data_available;
}

/* Interrupt callback functions ----------------------------------------------*/

/**
 * @brief DMA transfer complete callback (called from stm32f4xx_it.c)
 * @retval None
 */
void INA226_DMA_TransferComplete_Callback(void)
{
    if (timer_dma_enabled)
    {
        dma_transfer_count++;
        dma_success_count++;
        dma_transfer_complete = true;

        // Debug: Log raw data occasionally
        static uint32_t dma_debug_counter = 0;
        if ((++dma_debug_counter % 100) == 0) {
            logi("DMA Complete: Transfer %lu, Success %lu\r\n",
                 dma_transfer_count, dma_success_count);
        }
    }
}

/**
 * @brief DMA transfer error callback (called from stm32f4xx_it.c)
 * @retval None
 */
void INA226_DMA_TransferError_Callback(void)
{
    dma_error_count++;
    logi("DMA Transfer Error! Total errors: %lu\r\n", dma_error_count);
}

/**
 * @brief Timer2 interrupt handler for automatic INA226 reading (current method)
 * @retval None
 */
void TIM2_IRQHandler(void)
{
    if (TIM_GetITStatus(TIM2, TIM_IT_Update) != RESET)
    {
        // Clear interrupt flag
        TIM_ClearITPendingBit(TIM2, TIM_IT_Update);

        if (timer_dma_enabled)
        {
            dma_transfer_count++;

            // Read INA226 shunt voltage register
            uint16_t shunt_data;
            if (INA226_ReadRegister(INA226_REG_SHUNT_V, &shunt_data))
            {
                dma_success_count++;
                dma_transfer_complete = true;

                // Debug: Log raw data occasionally
                static uint32_t debug_counter = 0;
                if ((++debug_counter % 100) == 0) {
                    int16_t signed_value = (int16_t)shunt_data;
                    float shunt_mv = signed_value * 0.0025f;
                    float current_a = (shunt_mv / 1000.0f) / 0.75f;
                    logi("Raw: 0x%04X, Shunt: %.3fmV, Current: %.6fA\r\n",
                         shunt_data, shunt_mv, current_a);
                }

                // Store data in ping-pong buffer
                samples_buf[active_idx][wr_pos] = shunt_data;

                // Trigger buffer management
                HAL_I2C_MemRxCpltCallback(NULL);
            }
            else
            {
                dma_error_count++;
            }
        }
    }
}

// DMA1_Stream5_IRQHandler removed - using unified DMA1_Stream0_IRQHandler in stm32f4xx_it.c

/**
 * @brief GPIO EXTI callback - now simplified (optional, for manual trigger)
 * @param GPIO_Pin: GPIO pin that triggered the interrupt
 * @retval None
 */
void HAL_GPIO_EXTI_Callback(uint16_t GPIO_Pin)
{
    // This callback is now optional since we use timer-triggered DMA
    // Can be used for manual trigger or debugging
    if (GPIO_Pin == INA226_ALERT_PIN && ina226_optimized_mode)
    {
        // Optional: manual trigger for immediate read
    }
}

/**
 * @brief I2C memory reception complete callback
 * @param hi2c: I2C handle pointer
 * @retval None
 */
void HAL_I2C_MemRxCpltCallback(I2C_HandleTypeDef *hi2c)
{
    static uint32_t callback_count = 0;

    if (ina226_optimized_mode)
    {
        callback_count++;
        wr_pos++;

        // For testing, make data available every 10 samples for stability
        if (wr_pos >= 10) // Increased back to 10 for stability
        {
            // Debug: Log buffer switch
            static uint32_t switch_count = 0;
            if ((++switch_count % 50) == 0) {
                logi("Buffer switch %lu: from idx=%lu pos=%lu to idx=%lu\r\n",
                     switch_count, active_idx, wr_pos, active_idx ^ 1);
            }

            // Buffer ready
            flag_half_full = true;
            new_data_available = true;

            // Switch to next buffer BEFORE resetting position
            // This prevents main loop from reading uninitialized data
            uint32_t old_idx = active_idx;
            active_idx ^= 1; // Switch ping-pong buffer first
            wr_pos = 0;      // Then reset position

            // Initialize new buffer with last valid sample to avoid 0 readings
            if (old_idx < 2 && active_idx < 2) {
                samples_buf[active_idx][0] = samples_buf[old_idx][9]; // Copy last sample
                wr_pos = 1; // Start from position 1
            }
        }
    }
}
