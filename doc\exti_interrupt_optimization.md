# INA226 EXTI 中断优化方案

## 问题分析

### 原始问题
INA226 转换完成后立即发出 ALERT 信号，但 MCU 的 EXTI 中断处理时间过长，导致：

1. **中断处理时间过长** (50-100µs)：
   - 阻塞式 I2C 读取 (`INA226_ReadRegister`)
   - 日志输出 (`logi`)
   - DMA 重新配置
   - 两次 I2C 操作（读数据 + 清标志）

2. **错过后续中断**：
   - 下一次转换完成时 (0.5-1ms 后)
   - ALERT 再次拉低，但上一轮中断还未处理完
   - EXTI 无法响应新的中断

3. **数据丢失**：
   - 波形断帧
   - 采样率不稳定

## 优化方案

### 核心思想：延迟处理 (Deferred Processing)

将耗时的 I2C 操作从中断上下文移到主循环中执行。

### 优化前 (原始代码)
```c
void HAL_GPIO_EXTI_Callback(uint16_t GPIO_Pin)
{
    // ❌ 耗时操作都在中断中
    interrupt_count++;
    logi("EXTI interrupt...");                    // 耗时
    
    // DMA 配置
    DMA_Cmd(INA226_DMA_STREAM, DISABLE);         // 耗时
    while (DMA_GetCmdStatus(...) != DISABLE);    // 等待
    
    // I2C 读取
    INA226_ReadRegister(INA226_REG_SHUNT_V, &data);  // 阻塞 I2C
    INA226_ReadRegister(INA226_REG_MASK_ENABLE, &dummy); // 清标志
    
    // 总耗时：50-100µs
}
```

### 优化后 (新代码)
```c
// 快速中断变量
static volatile bool dma_read_pending = false;

void HAL_GPIO_EXTI_Callback(uint16_t GPIO_Pin)
{
    if (GPIO_Pin == INA226_ALERT_PIN && ina226_optimized_mode)
    {
        fast_interrupt_count++;
        dma_read_pending = true;  // ✅ 只设置标志
        // 立即退出，总耗时 < 5µs
    }
}

// 在主循环中处理
bool INA226_ProcessPendingRead(void)
{
    if (!dma_read_pending) return false;
    
    dma_read_pending = false;
    
    // ✅ 在非中断上下文中执行耗时操作
    INA226_ReadRegister(INA226_REG_SHUNT_V, &shunt_data);
    INA226_ReadRegister(INA226_REG_MASK_ENABLE, &dummy);
    
    // 处理数据...
    return true;
}
```

## 性能对比

| 方案 | 中断处理时间 | 能否响应快速中断 | 数据丢失风险 |
|------|-------------|-----------------|-------------|
| 原始方案 | 50-100µs | ❌ 否 | ❌ 高 |
| 优化方案 | < 5µs | ✅ 是 | ✅ 低 |

## 实现细节

### 1. 快速中断响应
```c
// 中断中只做最少的工作
void HAL_GPIO_EXTI_Callback(uint16_t GPIO_Pin)
{
    fast_interrupt_count++;      // 计数器
    dma_read_pending = true;     // 设置标志
    // 立即返回
}
```

### 2. 延迟处理
```c
// 主循环调用
bool App_Monitor_Get_Data_Fast(INA226_Data_t *data)
{
    INA226_ProcessPendingRead();  // 处理待处理的读取
    
    if (INA226_IsNewDataAvailable()) {
        // 获取数据...
        return true;
    }
    return false;
}
```

### 3. 数据流优化
```
原始流程：
ALERT → EXTI中断 → I2C读取 → 清标志 → 存储数据
        (50-100µs，阻塞后续中断)

优化流程：
ALERT → EXTI中断 → 设置标志 → 立即返回
        (< 5µs)
主循环 → 检查标志 → I2C读取 → 清标志 → 存储数据
        (非阻塞，不影响中断响应)
```

## 配置建议

### INA226 配置
```c
// 适中的转换频率，避免过快
uint16_t cfg = 0x04C7;  // 332µs + 588µs ≈ 1.1kHz
INA226_WriteRegister(0x00, cfg);

// 启用转换完成中断 + 锁存使能
INA226_WriteRegister(0x06, INA226_MASK_CNVR | INA226_MASK_LEN);
```

### 主循环调用
```c
// 在主循环中优先使用快速方法
if (App_Monitor_Get_Data_Fast(&ina226_data)) {
    ui_update_ina226_data(...);
}
```

## 优势

1. **中断响应快速**: < 5µs，不会错过后续中断
2. **数据完整性**: 避免因中断冲突导致的数据丢失
3. **系统稳定性**: 减少中断上下文中的复杂操作
4. **兼容性好**: 保持原有 API 接口不变

## 注意事项

1. **及时处理**: 主循环需要频繁调用 `INA226_ProcessPendingRead()`
2. **标志保护**: 使用 `volatile` 关键字保护共享变量
3. **错误处理**: 检查 I2C 操作的返回值
4. **调试信息**: 避免在中断中使用 `logi()` 等耗时函数

这个优化方案解决了 INA226 高频转换时的中断冲突问题，确保数据采集的连续性和稳定性。
