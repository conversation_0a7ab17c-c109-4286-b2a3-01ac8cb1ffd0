SPDXVersion: SPDX-2.2
DataLicense: CC0-1.0
SPDXID: SPDXRef-DOCUMENT
DocumentName: FreeRTOS-Kernel
DocumentNamespace: https://github.com/FreeRTOS/FreeRTOS-Kernel/blob/v11.1.0/sbom.spdx
Creator: Organization:Amazon Web Services
Created: 2024-04-22T07:38:21Z
CreatorComment: NOASSERTION
DocumentComment: NOASSERTION

PackageName: FreeRTOS-Kernel
SPDXID: SPDXRef-Package-FreeRTOS-Kernel
PackageVersion: v11.1.0
ExternalRef: SECURITY cpe23Type cpe:2.3:o:amazon:freertos:11.1.0:*:*:*:*:*:*:*
PackageDownloadLocation: https://github.com/FreeRTOS/FreeRTOS-Kernel/tree/v11.1.0
PackageLicenseDeclared: MIT
PackageLicenseConcluded: MIT
PackageLicenseInfoFromFiles: NOASSERTION
FilesAnalyzed: true
PackageVerificationCode: d207dfc39049c30f36250801ccd6b5931877f0c4
PackageCopyrightText: NOASSERTION
PackageSummary: NOASSERTION
PackageDescription: FreeRTOS Kernel.

FileName: ./timers.c
SPDXID: SPDXRef-File-timers.c
FileChecksum: SHA1: d5b7529301689737f32d77a91962a3087784230b
LicenseConcluded: MIT
FileCopyrightText: NOASSERTION
FileComment: NOASSERTION

FileName: ./croutine.c
SPDXID: SPDXRef-File-croutine.c
FileChecksum: SHA1: 420bd8faaa2cbdb2e89ca5d3a2c68f91c4b89bc5
LicenseConcluded: MIT
FileCopyrightText: NOASSERTION
FileComment: NOASSERTION

FileName: ./queue.c
SPDXID: SPDXRef-File-queue.c
FileChecksum: SHA1: 69ebc4bd69bfa1401c6e8725f36a581c85d718b1
LicenseConcluded: MIT
FileCopyrightText: NOASSERTION
FileComment: NOASSERTION

FileName: ./stream_buffer.c
SPDXID: SPDXRef-File-streambuffer.c
FileChecksum: SHA1: 7f9e5da8efc17ed8da38553a517c510d823a82a4
LicenseConcluded: MIT
FileCopyrightText: NOASSERTION
FileComment: NOASSERTION

FileName: ./tasks.c
SPDXID: SPDXRef-File-tasks.c
FileChecksum: SHA1: 8b450f2d352e70551d8bcfa4b4ef4eb1990fec26
LicenseConcluded: MIT
FileCopyrightText: NOASSERTION
FileComment: NOASSERTION

FileName: ./list.c
SPDXID: SPDXRef-File-list.c
FileChecksum: SHA1: 95aa5a49a56b9cec24a71261800ecd98f4c32a02
LicenseConcluded: MIT
FileCopyrightText: NOASSERTION
FileComment: NOASSERTION

FileName: ./event_groups.c
SPDXID: SPDXRef-File-eventgroups.c
FileChecksum: SHA1: 1a490cd2efa56dc38b1070685a4532fd3a50ef00
LicenseConcluded: MIT
FileCopyrightText: NOASSERTION
FileComment: NOASSERTION

FileName: ./portable/WizC/PIC18/port.c
SPDXID: SPDXRef-File-portable-WizC-PIC18-port.c
FileChecksum: SHA1: c8612625afbd7d4a74c77b073cc8446c8e53932c
LicenseConcluded: MIT
FileCopyrightText: NOASSERTION
FileComment: NOASSERTION

FileName: ./portable/WizC/PIC18/Drivers/Tick/isrTick.c
SPDXID: SPDXRef-File-portable-WizC-PIC18-Drivers-Tick-isrTick.c
FileChecksum: SHA1: 9ff364d374c5e49b996e50ac3cdc5120c96e5e71
LicenseConcluded: MIT
FileCopyrightText: NOASSERTION
FileComment: NOASSERTION

FileName: ./portable/WizC/PIC18/Drivers/Tick/Tick.c
SPDXID: SPDXRef-File-portable-WizC-PIC18-Drivers-Tick-Tick.c
FileChecksum: SHA1: de216847e48f56c85c42236fddee0f1ee1962d01
LicenseConcluded: MIT
FileCopyrightText: NOASSERTION
FileComment: NOASSERTION

FileName: ./portable/Rowley/MSP430F449/port.c
SPDXID: SPDXRef-File-portable-Rowley-MSP430F449-port.c
FileChecksum: SHA1: ac8786cc8b5648fa8cba956020766f525343231d
LicenseConcluded: MIT
FileCopyrightText: NOASSERTION
FileComment: NOASSERTION

FileName: ./portable/CCS/ARM_CM3/port.c
SPDXID: SPDXRef-File-portable-CCS-ARMCM3-port.c
FileChecksum: SHA1: 588f3c4c845d5fd519f89e1f367994b02825bde7
LicenseConcluded: MIT
FileCopyrightText: NOASSERTION
FileComment: NOASSERTION

FileName: ./portable/CCS/ARM_CM4F/port.c
SPDXID: SPDXRef-File-portable-CCS-ARMCM4F-port.c
FileChecksum: SHA1: f30c7fabadd20b5c27edce4daae67b119a5eb4da
LicenseConcluded: MIT
FileCopyrightText: NOASSERTION
FileComment: NOASSERTION

FileName: ./portable/CCS/ARM_Cortex-R4/port.c
SPDXID: SPDXRef-File-portable-CCS-ARMCortex-R4-port.c
FileChecksum: SHA1: 0b362809b7772a016ffae4c7bcf438b5d0a98dcd
LicenseConcluded: MIT
FileCopyrightText: NOASSERTION
FileComment: NOASSERTION

FileName: ./portable/CCS/MSP430X/port.c
SPDXID: SPDXRef-File-portable-CCS-MSP430X-port.c
FileChecksum: SHA1: 4da53f91ec3eb2174e9057bd779825988380c0fd
LicenseConcluded: MIT
FileCopyrightText: NOASSERTION
FileComment: NOASSERTION

FileName: ./portable/ThirdParty/XCC/Xtensa/portclib.c
SPDXID: SPDXRef-File-portable-ThirdParty-XCC-Xtensa-portclib.c
FileChecksum: SHA1: 1d17ee61635e25b8c1d99a2dbe5923ea6a393923
LicenseConcluded: MIT
FileCopyrightText: NOASSERTION
FileComment: NOASSERTION

FileName: ./portable/ThirdParty/XCC/Xtensa/port.c
SPDXID: SPDXRef-File-portable-ThirdParty-XCC-Xtensa-port.c
FileChecksum: SHA1: e8a372bd0b9150d958f6c6c7abe497eed111d1ac
LicenseConcluded: MIT
FileCopyrightText: NOASSERTION
FileComment: NOASSERTION

FileName: ./portable/ThirdParty/XCC/Xtensa/xtensa_init.c
SPDXID: SPDXRef-File-portable-ThirdParty-XCC-Xtensa-xtensainit.c
FileChecksum: SHA1: 01984f142efb060333aad973f76e11e1c82da0c7
LicenseConcluded: MIT
FileCopyrightText: NOASSERTION
FileComment: NOASSERTION

FileName: ./portable/ThirdParty/XCC/Xtensa/xtensa_intr.c
SPDXID: SPDXRef-File-portable-ThirdParty-XCC-Xtensa-xtensaintr.c
FileChecksum: SHA1: 3b46379977eb084bdf84902faa9f07c7fd6ba54d
LicenseConcluded: MIT
FileCopyrightText: NOASSERTION
FileComment: NOASSERTION

FileName: ./portable/ThirdParty/XCC/Xtensa/xtensa_overlay_os_hook.c
SPDXID: SPDXRef-File-portable-ThirdParty-XCC-Xtensa-xtensaoverlayoshook.c
FileChecksum: SHA1: 08aa4782d91e9af9c1f593c6f55b65d6e87dd422
LicenseConcluded: MIT
FileCopyrightText: NOASSERTION
FileComment: NOASSERTION

FileName: ./portable/ThirdParty/CDK/T-HEAD_CK802/port.c
SPDXID: SPDXRef-File-portable-ThirdParty-CDK-T-HEADCK802-port.c
FileChecksum: SHA1: c6b49a067c84f32d2005d53ef925d9b014567c94
LicenseConcluded: MIT
FileCopyrightText: NOASSERTION
FileComment: NOASSERTION

FileName: ./portable/ThirdParty/xClang/XCOREAI/port.c
SPDXID: SPDXRef-File-portable-ThirdParty-xClang-XCOREAI-port.c
FileChecksum: SHA1: a2a0e9c484a5c7d2176fb10278d2adaf356a2e9c
LicenseConcluded: MIT
FileCopyrightText: NOASSERTION
FileComment: NOASSERTION

FileName: ./portable/ThirdParty/GCC/ARC_EM_HS/port.c
SPDXID: SPDXRef-File-portable-ThirdParty-GCC-ARCEMHS-port.c
FileChecksum: SHA1: 1eab05e19f10eda5a1fbdb9bfab0ff003880b3e4
LicenseConcluded: MIT
FileCopyrightText: NOASSERTION
FileComment: NOASSERTION

FileName: ./portable/ThirdParty/GCC/ARC_EM_HS/arc_freertos_exceptions.c
SPDXID: SPDXRef-File-portable-ThirdParty-GCC-ARCEMHS-arcfreertosexceptions.c
FileChecksum: SHA1: d1f6d21c811d6a330a325b06eef04655d99d5a44
LicenseConcluded: MIT
FileCopyrightText: NOASSERTION
FileComment: NOASSERTION

FileName: ./portable/ThirdParty/GCC/ARC_EM_HS/freertos_tls.c
SPDXID: SPDXRef-File-portable-ThirdParty-GCC-ARCEMHS-freertostls.c
FileChecksum: SHA1: 0ebb2a2ea5bea07469fd5437367f3d8ba007fc32
LicenseConcluded: MIT
FileCopyrightText: NOASSERTION
FileComment: NOASSERTION

FileName: ./portable/ThirdParty/GCC/ARM_TFM/os_wrapper_freertos.c
SPDXID: SPDXRef-File-portable-ThirdParty-GCC-ARMTFM-oswrapperfreertos.c
FileChecksum: SHA1: 01e591e3f4a80a85afce4ebf03a57f2d0f3a04ad
LicenseConcluded: MIT
FileCopyrightText: NOASSERTION
FileComment: NOASSERTION

FileName: ./portable/ThirdParty/GCC/RP2040/port.c
SPDXID: SPDXRef-File-portable-ThirdParty-GCC-RP2040-port.c
FileChecksum: SHA1: 48caa2f4495eae0150ba9a2a67383d54746ffc65
LicenseConcluded: MIT
FileCopyrightText: NOASSERTION
FileComment: NOASSERTION

FileName: ./portable/ThirdParty/GCC/ARC_v1/port.c
SPDXID: SPDXRef-File-portable-ThirdParty-GCC-ARCv1-port.c
FileChecksum: SHA1: 6b28d6493bb88013c4d088e9d51e3790fa54ac14
LicenseConcluded: MIT
FileCopyrightText: NOASSERTION
FileComment: NOASSERTION

FileName: ./portable/ThirdParty/GCC/ARC_v1/arc_freertos_exceptions.c
SPDXID: SPDXRef-File-portable-ThirdParty-GCC-ARCv1-arcfreertosexceptions.c
FileChecksum: SHA1: d1f6d21c811d6a330a325b06eef04655d99d5a44
LicenseConcluded: MIT
FileCopyrightText: NOASSERTION
FileComment: NOASSERTION

FileName: ./portable/ThirdParty/GCC/Xtensa_ESP32/FreeRTOS-openocd.c
SPDXID: SPDXRef-File-portable-ThirdParty-GCC-XtensaESP32-FreeRTOS-openocd.c
FileChecksum: SHA1: 51fed0de72edfa2119b6ef614550f568bbc5ca73
LicenseConcluded: MIT
FileCopyrightText: NOASSERTION
FileComment: NOASSERTION

FileName: ./portable/ThirdParty/GCC/Xtensa_ESP32/port_systick.c
SPDXID: SPDXRef-File-portable-ThirdParty-GCC-XtensaESP32-portsystick.c
FileChecksum: SHA1: 53e73f1b68dbebe770940c465270e40078540823
LicenseConcluded: MIT
FileCopyrightText: NOASSERTION
FileComment: NOASSERTION

FileName: ./portable/ThirdParty/GCC/Xtensa_ESP32/port.c
SPDXID: SPDXRef-File-portable-ThirdParty-GCC-XtensaESP32-port.c
FileChecksum: SHA1: 07502e7202c00764df207f1c9a434e3c130ed95f
LicenseConcluded: MIT
FileCopyrightText: NOASSERTION
FileComment: NOASSERTION

FileName: ./portable/ThirdParty/GCC/Xtensa_ESP32/xtensa_init.c
SPDXID: SPDXRef-File-portable-ThirdParty-GCC-XtensaESP32-xtensainit.c
FileChecksum: SHA1: d9df771315aa1623052ad29a6a590cb50134bc04
LicenseConcluded: MIT
FileCopyrightText: NOASSERTION
FileComment: NOASSERTION

FileName: ./portable/ThirdParty/GCC/Xtensa_ESP32/port_common.c
SPDXID: SPDXRef-File-portable-ThirdParty-GCC-XtensaESP32-portcommon.c
FileChecksum: SHA1: 82381398311a146ad226612b884d2d2c6e221fc3
LicenseConcluded: MIT
FileCopyrightText: NOASSERTION
FileComment: NOASSERTION

FileName: ./portable/ThirdParty/GCC/Xtensa_ESP32/xtensa_overlay_os_hook.c
SPDXID: SPDXRef-File-portable-ThirdParty-GCC-XtensaESP32-xtensaoverlayoshook.c
FileChecksum: SHA1: 8d61fb9862ede5c199117506022a70c3e2e7552c
LicenseConcluded: MIT
FileCopyrightText: NOASSERTION
FileComment: NOASSERTION

FileName: ./portable/ThirdParty/GCC/Posix/port.c
SPDXID: SPDXRef-File-portable-ThirdParty-GCC-Posix-port.c
FileChecksum: SHA1: d0feff7632dc7696d1351df320ad5b2fe72bf0ac
LicenseConcluded: MIT
FileCopyrightText: NOASSERTION
FileComment: NOASSERTION

FileName: ./portable/ThirdParty/GCC/Posix/utils/wait_for_event.c
SPDXID: SPDXRef-File-portable-ThirdParty-GCC-Posix-utils-waitforevent.c
FileChecksum: SHA1: 226df026c36f25be40a71b1e3a58169061970e3b
LicenseConcluded: MIT
FileCopyrightText: NOASSERTION
FileComment: NOASSERTION

FileName: ./portable/ThirdParty/GCC/ATmega/port.c
SPDXID: SPDXRef-File-portable-ThirdParty-GCC-ATmega-port.c
FileChecksum: SHA1: d4c6d1cd22fcc12a1883e9af8f17aa1841b61760
LicenseConcluded: MIT
FileCopyrightText: NOASSERTION
FileComment: NOASSERTION

FileName: ./portable/IAR/STR75x/port.c
SPDXID: SPDXRef-File-portable-IAR-STR75x-port.c
FileChecksum: SHA1: 67e9368c2e73f38202845f3c1b62f4a5c2b08f45
LicenseConcluded: MIT
FileCopyrightText: NOASSERTION
FileComment: NOASSERTION

FileName: ./portable/IAR/ARM_CM85/secure/secure_context.c
SPDXID: SPDXRef-File-portable-IAR-ARMCM85-secure-securecontext.c
FileChecksum: SHA1: b9283bfc2b63772aa24fd8bf3f923e57e2f4c2eb
LicenseConcluded: MIT
FileCopyrightText: NOASSERTION
FileComment: NOASSERTION

FileName: ./portable/IAR/ARM_CM85/secure/secure_heap.c
SPDXID: SPDXRef-File-portable-IAR-ARMCM85-secure-secureheap.c
FileChecksum: SHA1: 450b9d9674e2f561f9276fcc1b92b8076e6f4de7
LicenseConcluded: MIT
FileCopyrightText: NOASSERTION
FileComment: NOASSERTION

FileName: ./portable/IAR/ARM_CM85/secure/secure_init.c
SPDXID: SPDXRef-File-portable-IAR-ARMCM85-secure-secureinit.c
FileChecksum: SHA1: a2f502de907154a213fd8ebf4a87165ef3693fc7
LicenseConcluded: MIT
FileCopyrightText: NOASSERTION
FileComment: NOASSERTION

FileName: ./portable/IAR/ARM_CM85/non_secure/port.c
SPDXID: SPDXRef-File-portable-IAR-ARMCM85-nonsecure-port.c
FileChecksum: SHA1: f6bd76c2e19638dc20e0e058315dc950a3b0820e
LicenseConcluded: MIT
FileCopyrightText: NOASSERTION
FileComment: NOASSERTION

FileName: ./portable/IAR/ARM_CM3/port.c
SPDXID: SPDXRef-File-portable-IAR-ARMCM3-port.c
FileChecksum: SHA1: a4c86a0cae10b21183d80952b593b23e11e90abd
LicenseConcluded: MIT
FileCopyrightText: NOASSERTION
FileComment: NOASSERTION

FileName: ./portable/IAR/ARM_CM7/r0p1/port.c
SPDXID: SPDXRef-File-portable-IAR-ARMCM7-r0p1-port.c
FileChecksum: SHA1: a57c2c3eb082d582941a76fccbf985099d6d2b29
LicenseConcluded: MIT
FileCopyrightText: NOASSERTION
FileComment: NOASSERTION

FileName: ./portable/IAR/ARM_CA9/port.c
SPDXID: SPDXRef-File-portable-IAR-ARMCA9-port.c
FileChecksum: SHA1: b05d2ebc204ad72bb942538c64aecf9cd437e134
LicenseConcluded: MIT
FileCopyrightText: NOASSERTION
FileComment: NOASSERTION

FileName: ./portable/IAR/RX700v3_DPFPU/port.c
SPDXID: SPDXRef-File-portable-IAR-RX700v3DPFPU-port.c
FileChecksum: SHA1: f6a03b8b5bd27e2d3f25666e28651c1a2b8b5fd7
LicenseConcluded: MIT
FileCopyrightText: NOASSERTION
FileComment: NOASSERTION

FileName: ./portable/IAR/AVR_Mega0/port.c
SPDXID: SPDXRef-File-portable-IAR-AVRMega0-port.c
FileChecksum: SHA1: 176ec555f139a09815c3c72c73366d43ba0366c6
LicenseConcluded: MIT
FileCopyrightText: NOASSERTION
FileComment: NOASSERTION

FileName: ./portable/IAR/V850ES/port.c
SPDXID: SPDXRef-File-portable-IAR-V850ES-port.c
FileChecksum: SHA1: 190f72de8c02c241ec01b74b2311fa61c7dc9bf6
LicenseConcluded: MIT
FileCopyrightText: NOASSERTION
FileComment: NOASSERTION

FileName: ./portable/IAR/ARM_CRx_No_GIC/port.c
SPDXID: SPDXRef-File-portable-IAR-ARMCRxNoGIC-port.c
FileChecksum: SHA1: 5b9ba554a20c713b1e5dba9027b4080346723dcf
LicenseConcluded: MIT
FileCopyrightText: NOASSERTION
FileComment: NOASSERTION

FileName: ./portable/IAR/ATMega323/port.c
SPDXID: SPDXRef-File-portable-IAR-ATMega323-port.c
FileChecksum: SHA1: ef496e64aa61bdbd0d6eda86273c8a359b6ad140
LicenseConcluded: MIT
FileCopyrightText: NOASSERTION
FileComment: NOASSERTION

FileName: ./portable/IAR/ARM_CM55_NTZ/non_secure/port.c
SPDXID: SPDXRef-File-portable-IAR-ARMCM55NTZ-nonsecure-port.c
FileChecksum: SHA1: f6bd76c2e19638dc20e0e058315dc950a3b0820e
LicenseConcluded: MIT
FileCopyrightText: NOASSERTION
FileComment: NOASSERTION

FileName: ./portable/IAR/MSP430/port.c
SPDXID: SPDXRef-File-portable-IAR-MSP430-port.c
FileChecksum: SHA1: d35ec10dde14f5e5ba0672e00d3aa3145b8ed9b9
LicenseConcluded: MIT
FileCopyrightText: NOASSERTION
FileComment: NOASSERTION

FileName: ./portable/IAR/ARM_CM23_NTZ/non_secure/port.c
SPDXID: SPDXRef-File-portable-IAR-ARMCM23NTZ-nonsecure-port.c
FileChecksum: SHA1: f6bd76c2e19638dc20e0e058315dc950a3b0820e
LicenseConcluded: MIT
FileCopyrightText: NOASSERTION
FileComment: NOASSERTION

FileName: ./portable/IAR/ARM_CM23/secure/secure_context.c
SPDXID: SPDXRef-File-portable-IAR-ARMCM23-secure-securecontext.c
FileChecksum: SHA1: b9283bfc2b63772aa24fd8bf3f923e57e2f4c2eb
LicenseConcluded: MIT
FileCopyrightText: NOASSERTION
FileComment: NOASSERTION

FileName: ./portable/IAR/ARM_CM23/secure/secure_heap.c
SPDXID: SPDXRef-File-portable-IAR-ARMCM23-secure-secureheap.c
FileChecksum: SHA1: 450b9d9674e2f561f9276fcc1b92b8076e6f4de7
LicenseConcluded: MIT
FileCopyrightText: NOASSERTION
FileComment: NOASSERTION

FileName: ./portable/IAR/ARM_CM23/secure/secure_init.c
SPDXID: SPDXRef-File-portable-IAR-ARMCM23-secure-secureinit.c
FileChecksum: SHA1: a2f502de907154a213fd8ebf4a87165ef3693fc7
LicenseConcluded: MIT
FileCopyrightText: NOASSERTION
FileComment: NOASSERTION

FileName: ./portable/IAR/ARM_CM23/non_secure/port.c
SPDXID: SPDXRef-File-portable-IAR-ARMCM23-nonsecure-port.c
FileChecksum: SHA1: f6bd76c2e19638dc20e0e058315dc950a3b0820e
LicenseConcluded: MIT
FileCopyrightText: NOASSERTION
FileComment: NOASSERTION

FileName: ./portable/IAR/AVR_AVRDx/port.c
SPDXID: SPDXRef-File-portable-IAR-AVRAVRDx-port.c
FileChecksum: SHA1: d2574a5e660ceb0aab2851423ac4591cefa2b089
LicenseConcluded: MIT
FileCopyrightText: NOASSERTION
FileComment: NOASSERTION

FileName: ./portable/IAR/78K0R/port.c
SPDXID: SPDXRef-File-portable-IAR-78K0R-port.c
FileChecksum: SHA1: 7ac7d45df2b089a8459d87dffe5ccc5706574e8b
LicenseConcluded: MIT
FileCopyrightText: NOASSERTION
FileComment: NOASSERTION

FileName: ./portable/IAR/ARM_CM4F/port.c
SPDXID: SPDXRef-File-portable-IAR-ARMCM4F-port.c
FileChecksum: SHA1: e15a74dcf6b69001c35f30489bebde5efbbe0cf1
LicenseConcluded: MIT
FileCopyrightText: NOASSERTION
FileComment: NOASSERTION

FileName: ./portable/IAR/STR71x/port.c
SPDXID: SPDXRef-File-portable-IAR-STR71x-port.c
FileChecksum: SHA1: bbd8e6f8af240371f6dc87024fe8c3ca0351e0f3
LicenseConcluded: MIT
FileCopyrightText: NOASSERTION
FileComment: NOASSERTION

FileName: ./portable/IAR/RX600/port.c
SPDXID: SPDXRef-File-portable-IAR-RX600-port.c
FileChecksum: SHA1: 86f53a0cbbba7fd1d842818e3075b11db7207319
LicenseConcluded: MIT
FileCopyrightText: NOASSERTION
FileComment: NOASSERTION

FileName: ./portable/IAR/ARM_CM35P_NTZ/non_secure/port.c
SPDXID: SPDXRef-File-portable-IAR-ARMCM35PNTZ-nonsecure-port.c
FileChecksum: SHA1: f6bd76c2e19638dc20e0e058315dc950a3b0820e
LicenseConcluded: MIT
FileCopyrightText: NOASSERTION
FileComment: NOASSERTION

FileName: ./portable/IAR/ARM_CM55/secure/secure_context.c
SPDXID: SPDXRef-File-portable-IAR-ARMCM55-secure-securecontext.c
FileChecksum: SHA1: b9283bfc2b63772aa24fd8bf3f923e57e2f4c2eb
LicenseConcluded: MIT
FileCopyrightText: NOASSERTION
FileComment: NOASSERTION

FileName: ./portable/IAR/ARM_CM55/secure/secure_heap.c
SPDXID: SPDXRef-File-portable-IAR-ARMCM55-secure-secureheap.c
FileChecksum: SHA1: 450b9d9674e2f561f9276fcc1b92b8076e6f4de7
LicenseConcluded: MIT
FileCopyrightText: NOASSERTION
FileComment: NOASSERTION

FileName: ./portable/IAR/ARM_CM55/secure/secure_init.c
SPDXID: SPDXRef-File-portable-IAR-ARMCM55-secure-secureinit.c
FileChecksum: SHA1: a2f502de907154a213fd8ebf4a87165ef3693fc7
LicenseConcluded: MIT
FileCopyrightText: NOASSERTION
FileComment: NOASSERTION

FileName: ./portable/IAR/ARM_CM55/non_secure/port.c
SPDXID: SPDXRef-File-portable-IAR-ARMCM55-nonsecure-port.c
FileChecksum: SHA1: f6bd76c2e19638dc20e0e058315dc950a3b0820e
LicenseConcluded: MIT
FileCopyrightText: NOASSERTION
FileComment: NOASSERTION

FileName: ./portable/IAR/AVR32_UC3/read.c
SPDXID: SPDXRef-File-portable-IAR-AVR32UC3-read.c
FileChecksum: SHA1: 14379e8ad9ef1d748012d61a5e9d965ca229901f
LicenseConcluded: MIT
FileCopyrightText: NOASSERTION
FileComment: NOASSERTION

FileName: ./portable/IAR/AVR32_UC3/write.c
SPDXID: SPDXRef-File-portable-IAR-AVR32UC3-write.c
FileChecksum: SHA1: 786afa98231b9ab28e3e19424c52336bbcce77f1
LicenseConcluded: MIT
FileCopyrightText: NOASSERTION
FileComment: NOASSERTION

FileName: ./portable/IAR/AVR32_UC3/port.c
SPDXID: SPDXRef-File-portable-IAR-AVR32UC3-port.c
FileChecksum: SHA1: 5a4055f6998624bd4e163d40eba15dc5f7871e39
LicenseConcluded: MIT
FileCopyrightText: NOASSERTION
FileComment: NOASSERTION

FileName: ./portable/IAR/ARM_CM35P/secure/secure_context.c
SPDXID: SPDXRef-File-portable-IAR-ARMCM35P-secure-securecontext.c
FileChecksum: SHA1: b9283bfc2b63772aa24fd8bf3f923e57e2f4c2eb
LicenseConcluded: MIT
FileCopyrightText: NOASSERTION
FileComment: NOASSERTION

FileName: ./portable/IAR/ARM_CM35P/secure/secure_heap.c
SPDXID: SPDXRef-File-portable-IAR-ARMCM35P-secure-secureheap.c
FileChecksum: SHA1: 450b9d9674e2f561f9276fcc1b92b8076e6f4de7
LicenseConcluded: MIT
FileCopyrightText: NOASSERTION
FileComment: NOASSERTION

FileName: ./portable/IAR/ARM_CM35P/secure/secure_init.c
SPDXID: SPDXRef-File-portable-IAR-ARMCM35P-secure-secureinit.c
FileChecksum: SHA1: a2f502de907154a213fd8ebf4a87165ef3693fc7
LicenseConcluded: MIT
FileCopyrightText: NOASSERTION
FileComment: NOASSERTION

FileName: ./portable/IAR/ARM_CM35P/non_secure/port.c
SPDXID: SPDXRef-File-portable-IAR-ARMCM35P-nonsecure-port.c
FileChecksum: SHA1: f6bd76c2e19638dc20e0e058315dc950a3b0820e
LicenseConcluded: MIT
FileCopyrightText: NOASSERTION
FileComment: NOASSERTION

FileName: ./portable/IAR/ARM_CM33_NTZ/non_secure/port.c
SPDXID: SPDXRef-File-portable-IAR-ARMCM33NTZ-nonsecure-port.c
FileChecksum: SHA1: f6bd76c2e19638dc20e0e058315dc950a3b0820e
LicenseConcluded: MIT
FileCopyrightText: NOASSERTION
FileComment: NOASSERTION

FileName: ./portable/IAR/ARM_CA5_No_GIC/port.c
SPDXID: SPDXRef-File-portable-IAR-ARMCA5NoGIC-port.c
FileChecksum: SHA1: 3d6639b32eb31917f9ca15e4490e4ba787912a5e
LicenseConcluded: MIT
FileCopyrightText: NOASSERTION
FileComment: NOASSERTION

FileName: ./portable/IAR/ARM_CM0/port.c
SPDXID: SPDXRef-File-portable-IAR-ARMCM0-port.c
FileChecksum: SHA1: 1f597b5d13d4ce41069e9ce147866295b4c0d1ee
LicenseConcluded: MIT
FileCopyrightText: NOASSERTION
FileComment: NOASSERTION

FileName: ./portable/IAR/RX100/port.c
SPDXID: SPDXRef-File-portable-IAR-RX100-port.c
FileChecksum: SHA1: 097c0f4d4490e6649a3b2978217d540e14c8c13f
LicenseConcluded: MIT
FileCopyrightText: NOASSERTION
FileComment: NOASSERTION

FileName: ./portable/IAR/ARM_CM4F_MPU/port.c
SPDXID: SPDXRef-File-portable-IAR-ARMCM4FMPU-port.c
FileChecksum: SHA1: 0b827147158ff0c2430d83270e76a967c62c3448
LicenseConcluded: MIT
FileCopyrightText: NOASSERTION
FileComment: NOASSERTION

FileName: ./portable/IAR/MSP430X/port.c
SPDXID: SPDXRef-File-portable-IAR-MSP430X-port.c
FileChecksum: SHA1: 3bc9d0f7fd471a723a677b5b5be2eb42ce197e3e
LicenseConcluded: MIT
FileCopyrightText: NOASSERTION
FileComment: NOASSERTION

FileName: ./portable/IAR/ARM_CM33/secure/secure_context.c
SPDXID: SPDXRef-File-portable-IAR-ARMCM33-secure-securecontext.c
FileChecksum: SHA1: b9283bfc2b63772aa24fd8bf3f923e57e2f4c2eb
LicenseConcluded: MIT
FileCopyrightText: NOASSERTION
FileComment: NOASSERTION

FileName: ./portable/IAR/ARM_CM33/secure/secure_heap.c
SPDXID: SPDXRef-File-portable-IAR-ARMCM33-secure-secureheap.c
FileChecksum: SHA1: 450b9d9674e2f561f9276fcc1b92b8076e6f4de7
LicenseConcluded: MIT
FileCopyrightText: NOASSERTION
FileComment: NOASSERTION

FileName: ./portable/IAR/ARM_CM33/secure/secure_init.c
SPDXID: SPDXRef-File-portable-IAR-ARMCM33-secure-secureinit.c
FileChecksum: SHA1: a2f502de907154a213fd8ebf4a87165ef3693fc7
LicenseConcluded: MIT
FileCopyrightText: NOASSERTION
FileComment: NOASSERTION

FileName: ./portable/IAR/ARM_CM33/non_secure/port.c
SPDXID: SPDXRef-File-portable-IAR-ARMCM33-nonsecure-port.c
FileChecksum: SHA1: f6bd76c2e19638dc20e0e058315dc950a3b0820e
LicenseConcluded: MIT
FileCopyrightText: NOASSERTION
FileComment: NOASSERTION

FileName: ./portable/IAR/AtmelSAM9XE/port.c
SPDXID: SPDXRef-File-portable-IAR-AtmelSAM9XE-port.c
FileChecksum: SHA1: d0da045bee4cd9372296fdc26f51d50c90d26624
LicenseConcluded: MIT
FileCopyrightText: NOASSERTION
FileComment: NOASSERTION

FileName: ./portable/IAR/RXv2/port.c
SPDXID: SPDXRef-File-portable-IAR-RXv2-port.c
FileChecksum: SHA1: 1f062ccb339590ed13933d6332276fdad5e4b5d7
LicenseConcluded: MIT
FileCopyrightText: NOASSERTION
FileComment: NOASSERTION

FileName: ./portable/IAR/RL78/port.c
SPDXID: SPDXRef-File-portable-IAR-RL78-port.c
FileChecksum: SHA1: 644e590162f05348d030baf8d9f7eccb5fbd4b6b
LicenseConcluded: MIT
FileCopyrightText: NOASSERTION
FileComment: NOASSERTION

FileName: ./portable/IAR/AtmelSAM7S64/port.c
SPDXID: SPDXRef-File-portable-IAR-AtmelSAM7S64-port.c
FileChecksum: SHA1: be62503de644dca07a4d2461107b1f29f274b232
LicenseConcluded: MIT
FileCopyrightText: NOASSERTION
FileComment: NOASSERTION

FileName: ./portable/IAR/RISC-V/port.c
SPDXID: SPDXRef-File-portable-IAR-RISC-V-port.c
FileChecksum: SHA1: d5e34f88e5815a50070a80157ccf0c12d1f19386
LicenseConcluded: MIT
FileCopyrightText: NOASSERTION
FileComment: NOASSERTION

FileName: ./portable/IAR/STR91x/port.c
SPDXID: SPDXRef-File-portable-IAR-STR91x-port.c
FileChecksum: SHA1: 23c3245b9400a6f661bbde400dc0b309f40288bf
LicenseConcluded: MIT
FileCopyrightText: NOASSERTION
FileComment: NOASSERTION

FileName: ./portable/IAR/ARM_CM85_NTZ/non_secure/port.c
SPDXID: SPDXRef-File-portable-IAR-ARMCM85NTZ-nonsecure-port.c
FileChecksum: SHA1: f6bd76c2e19638dc20e0e058315dc950a3b0820e
LicenseConcluded: MIT
FileCopyrightText: NOASSERTION
FileComment: NOASSERTION

FileName: ./portable/IAR/LPC2000/port.c
SPDXID: SPDXRef-File-portable-IAR-LPC2000-port.c
FileChecksum: SHA1: 2bc46b4c592256dd9accba017e06f63899a342ff
LicenseConcluded: MIT
FileCopyrightText: NOASSERTION
FileComment: NOASSERTION

FileName: ./portable/MikroC/ARM_CM4F/port.c
SPDXID: SPDXRef-File-portable-MikroC-ARMCM4F-port.c
FileChecksum: SHA1: f1475e55b128514c48b011818fc3c78958b3344f
LicenseConcluded: MIT
FileCopyrightText: NOASSERTION
FileComment: NOASSERTION

FileName: ./portable/Softune/MB91460/port.c
SPDXID: SPDXRef-File-portable-Softune-MB91460-port.c
FileChecksum: SHA1: 33368d9508ef97d578e146003d206a4104253378
LicenseConcluded: MIT
FileCopyrightText: NOASSERTION
FileComment: NOASSERTION

FileName: ./portable/Softune/MB91460/__STD_LIB_sbrk.c
SPDXID: SPDXRef-File-portable-Softune-MB91460-STDLIBsbrk.c
FileChecksum: SHA1: daca714fdf608efebf2de99aaea2ab627087c546
LicenseConcluded: MIT
FileCopyrightText: NOASSERTION
FileComment: NOASSERTION

FileName: ./portable/Softune/MB96340/port.c
SPDXID: SPDXRef-File-portable-Softune-MB96340-port.c
FileChecksum: SHA1: 991abc655b48718b2835fb1996e651c7c2acee44
LicenseConcluded: MIT
FileCopyrightText: NOASSERTION
FileComment: NOASSERTION

FileName: ./portable/Softune/MB96340/__STD_LIB_sbrk.c
SPDXID: SPDXRef-File-portable-Softune-MB96340-STDLIBsbrk.c
FileChecksum: SHA1: daca714fdf608efebf2de99aaea2ab627087c546
LicenseConcluded: MIT
FileCopyrightText: NOASSERTION
FileComment: NOASSERTION

FileName: ./portable/Renesas/RX700v3_DPFPU/port.c
SPDXID: SPDXRef-File-portable-Renesas-RX700v3DPFPU-port.c
FileChecksum: SHA1: 1b0983884a2bb556f126fdb0ddc849b959204a3b
LicenseConcluded: MIT
FileCopyrightText: NOASSERTION
FileComment: NOASSERTION

FileName: ./portable/Renesas/SH2A_FPU/port.c
SPDXID: SPDXRef-File-portable-Renesas-SH2AFPU-port.c
FileChecksum: SHA1: c96b957a1c7dade91412131f8da133f8d4f63139
LicenseConcluded: MIT
FileCopyrightText: NOASSERTION
FileComment: NOASSERTION

FileName: ./portable/Renesas/RX600/port.c
SPDXID: SPDXRef-File-portable-Renesas-RX600-port.c
FileChecksum: SHA1: 06d674f0f32141e29468d99a1823aeca231f7681
LicenseConcluded: MIT
FileCopyrightText: NOASSERTION
FileComment: NOASSERTION

FileName: ./portable/Renesas/RX100/port.c
SPDXID: SPDXRef-File-portable-Renesas-RX100-port.c
FileChecksum: SHA1: bfff36589f1fb88dc1722716895b9dabc4910794
LicenseConcluded: MIT
FileCopyrightText: NOASSERTION
FileComment: NOASSERTION

FileName: ./portable/Renesas/RX600v2/port.c
SPDXID: SPDXRef-File-portable-Renesas-RX600v2-port.c
FileChecksum: SHA1: e92a40845ebbf0e2f39ffdf08b68337d22a4a7d4
LicenseConcluded: MIT
FileCopyrightText: NOASSERTION
FileComment: NOASSERTION

FileName: ./portable/Renesas/RX200/port.c
SPDXID: SPDXRef-File-portable-Renesas-RX200-port.c
FileChecksum: SHA1: 312a2224720fdcadb21f35cf6a20f645a441626b
LicenseConcluded: MIT
FileCopyrightText: NOASSERTION
FileComment: NOASSERTION

FileName: ./portable/SDCC/Cygnal/port.c
SPDXID: SPDXRef-File-portable-SDCC-Cygnal-port.c
FileChecksum: SHA1: 930062ff3756fae46a3c00b903db7451e5607693
LicenseConcluded: MIT
FileCopyrightText: NOASSERTION
FileComment: NOASSERTION

FileName: ./portable/oWatcom/16BitDOS/Flsh186/port.c
SPDXID: SPDXRef-File-portable-oWatcom-16BitDOS-Flsh186-port.c
FileChecksum: SHA1: f8e127e9772fb3f556c2dddae38bb99da3529518
LicenseConcluded: MIT
FileCopyrightText: NOASSERTION
FileComment: NOASSERTION

FileName: ./portable/oWatcom/16BitDOS/PC/port.c
SPDXID: SPDXRef-File-portable-oWatcom-16BitDOS-PC-port.c
FileChecksum: SHA1: 634351185c8a257620754290d2b63c0392f45a81
LicenseConcluded: MIT
FileCopyrightText: NOASSERTION
FileComment: NOASSERTION

FileName: ./portable/oWatcom/16BitDOS/common/portcomn.c
SPDXID: SPDXRef-File-portable-oWatcom-16BitDOS-common-portcomn.c
FileChecksum: SHA1: 85c29772974e5702359b7882bf307aa80cdb951a
LicenseConcluded: MIT
FileCopyrightText: NOASSERTION
FileComment: NOASSERTION

FileName: ./portable/RVDS/ARM_CM3/port.c
SPDXID: SPDXRef-File-portable-RVDS-ARMCM3-port.c
FileChecksum: SHA1: 1534d01a36d99151db7ad5aa2aa11fc9398cc79a
LicenseConcluded: MIT
FileCopyrightText: NOASSERTION
FileComment: NOASSERTION

FileName: ./portable/RVDS/ARM_CM7/r0p1/port.c
SPDXID: SPDXRef-File-portable-RVDS-ARMCM7-r0p1-port.c
FileChecksum: SHA1: 2dccd5b93f769f438011e28e3fe1d72cfac7f6f2
LicenseConcluded: MIT
FileCopyrightText: NOASSERTION
FileComment: NOASSERTION

FileName: ./portable/RVDS/ARM_CA9/port.c
SPDXID: SPDXRef-File-portable-RVDS-ARMCA9-port.c
FileChecksum: SHA1: 697449a3d1c306c0fe68d96f84edbfc10ece9035
LicenseConcluded: MIT
FileCopyrightText: NOASSERTION
FileComment: NOASSERTION

FileName: ./portable/RVDS/ARM_CM4F/port.c
SPDXID: SPDXRef-File-portable-RVDS-ARMCM4F-port.c
FileChecksum: SHA1: 5f2e3973cc446ed789a75d9bc5b6ba737c91fd9c
LicenseConcluded: MIT
FileCopyrightText: NOASSERTION
FileComment: NOASSERTION

FileName: ./portable/RVDS/ARM_CM0/port.c
SPDXID: SPDXRef-File-portable-RVDS-ARMCM0-port.c
FileChecksum: SHA1: c720f1844aca05b28679522d79b80724cfe6d00e
LicenseConcluded: MIT
FileCopyrightText: NOASSERTION
FileComment: NOASSERTION

FileName: ./portable/RVDS/ARM7_LPC21xx/port.c
SPDXID: SPDXRef-File-portable-RVDS-ARM7LPC21xx-port.c
FileChecksum: SHA1: 4e8f1ab75f8263857d93014ac31f88c64a9d1511
LicenseConcluded: MIT
FileCopyrightText: NOASSERTION
FileComment: NOASSERTION

FileName: ./portable/RVDS/ARM_CM4_MPU/port.c
SPDXID: SPDXRef-File-portable-RVDS-ARMCM4MPU-port.c
FileChecksum: SHA1: d0200a59599079d5be251f8eb0b262978ed0f4e8
LicenseConcluded: MIT
FileCopyrightText: NOASSERTION
FileComment: NOASSERTION

FileName: ./portable/RVDS/ARM_CM4_MPU/mpu_wrappers_v2_asm.c
SPDXID: SPDXRef-File-portable-RVDS-ARMCM4MPU-mpuwrappersv2asm.c
FileChecksum: SHA1: b862396fb902f4f193b1dcade22b74c44c1dd9a7
LicenseConcluded: MIT
FileCopyrightText: NOASSERTION
FileComment: NOASSERTION

FileName: ./portable/ARMv8M/secure/init/secure_init.c
SPDXID: SPDXRef-File-portable-ARMv8M-secure-init-secureinit.c
FileChecksum: SHA1: a2f502de907154a213fd8ebf4a87165ef3693fc7
LicenseConcluded: MIT
FileCopyrightText: NOASSERTION
FileComment: NOASSERTION

FileName: ./portable/ARMv8M/secure/heap/secure_heap.c
SPDXID: SPDXRef-File-portable-ARMv8M-secure-heap-secureheap.c
FileChecksum: SHA1: 450b9d9674e2f561f9276fcc1b92b8076e6f4de7
LicenseConcluded: MIT
FileCopyrightText: NOASSERTION
FileComment: NOASSERTION

FileName: ./portable/ARMv8M/secure/context/secure_context.c
SPDXID: SPDXRef-File-portable-ARMv8M-secure-context-securecontext.c
FileChecksum: SHA1: b9283bfc2b63772aa24fd8bf3f923e57e2f4c2eb
LicenseConcluded: MIT
FileCopyrightText: NOASSERTION
FileComment: NOASSERTION

FileName: ./portable/ARMv8M/secure/context/portable/GCC/ARM_CM23/secure_context_port.c
SPDXID: SPDXRef-File-portable-ARMv8M-secure-context-portable-GCC-ARMCM23-securecontextport.c
FileChecksum: SHA1: a2da582746c3e0997ffe71e6e225a1ab32b91b6a
LicenseConcluded: MIT
FileCopyrightText: NOASSERTION
FileComment: NOASSERTION

FileName: ./portable/ARMv8M/secure/context/portable/GCC/ARM_CM33/secure_context_port.c
SPDXID: SPDXRef-File-portable-ARMv8M-secure-context-portable-GCC-ARMCM33-securecontextport.c
FileChecksum: SHA1: cd31a095e79169f5bb371c04027df39669c671ae
LicenseConcluded: MIT
FileCopyrightText: NOASSERTION
FileComment: NOASSERTION

FileName: ./portable/ARMv8M/non_secure/port.c
SPDXID: SPDXRef-File-portable-ARMv8M-nonsecure-port.c
FileChecksum: SHA1: f6bd76c2e19638dc20e0e058315dc950a3b0820e
LicenseConcluded: MIT
FileCopyrightText: NOASSERTION
FileComment: NOASSERTION

FileName: ./portable/ARMv8M/non_secure/portable/GCC/ARM_CM23_NTZ/mpu_wrappers_v2_asm.c
SPDXID: SPDXRef-File-portable-ARMv8M-nonsecure-portable-GCC-ARMCM23NTZ-mpuwrappersv2asm.c
FileChecksum: SHA1: d25e20c9abee0edb07afaf8be795de131d27e04b
LicenseConcluded: MIT
FileCopyrightText: NOASSERTION
FileComment: NOASSERTION

FileName: ./portable/ARMv8M/non_secure/portable/GCC/ARM_CM23_NTZ/portasm.c
SPDXID: SPDXRef-File-portable-ARMv8M-nonsecure-portable-GCC-ARMCM23NTZ-portasm.c
FileChecksum: SHA1: 89e35a1318b5940d750d95ea75e132147a92c333
LicenseConcluded: MIT
FileCopyrightText: NOASSERTION
FileComment: NOASSERTION

FileName: ./portable/ARMv8M/non_secure/portable/GCC/ARM_CM23/mpu_wrappers_v2_asm.c
SPDXID: SPDXRef-File-portable-ARMv8M-nonsecure-portable-GCC-ARMCM23-mpuwrappersv2asm.c
FileChecksum: SHA1: d25e20c9abee0edb07afaf8be795de131d27e04b
LicenseConcluded: MIT
FileCopyrightText: NOASSERTION
FileComment: NOASSERTION

FileName: ./portable/ARMv8M/non_secure/portable/GCC/ARM_CM23/portasm.c
SPDXID: SPDXRef-File-portable-ARMv8M-nonsecure-portable-GCC-ARMCM23-portasm.c
FileChecksum: SHA1: a9264073894d15c80b053dfb58f352715f4be886
LicenseConcluded: MIT
FileCopyrightText: NOASSERTION
FileComment: NOASSERTION

FileName: ./portable/ARMv8M/non_secure/portable/GCC/ARM_CM33_NTZ/mpu_wrappers_v2_asm.c
SPDXID: SPDXRef-File-portable-ARMv8M-nonsecure-portable-GCC-ARMCM33NTZ-mpuwrappersv2asm.c
FileChecksum: SHA1: ca2795fc9320114f2e6d3f3e650dfd7e81ff4b83
LicenseConcluded: MIT
FileCopyrightText: NOASSERTION
FileComment: NOASSERTION

FileName: ./portable/ARMv8M/non_secure/portable/GCC/ARM_CM33_NTZ/portasm.c
SPDXID: SPDXRef-File-portable-ARMv8M-nonsecure-portable-GCC-ARMCM33NTZ-portasm.c
FileChecksum: SHA1: 3dbda857c79672c6eebaf96f335643435157f47c
LicenseConcluded: MIT
FileCopyrightText: NOASSERTION
FileComment: NOASSERTION

FileName: ./portable/ARMv8M/non_secure/portable/GCC/ARM_CM33/mpu_wrappers_v2_asm.c
SPDXID: SPDXRef-File-portable-ARMv8M-nonsecure-portable-GCC-ARMCM33-mpuwrappersv2asm.c
FileChecksum: SHA1: 17239cad5bfb222454503c215324cac12798f7c9
LicenseConcluded: MIT
FileCopyrightText: NOASSERTION
FileComment: NOASSERTION

FileName: ./portable/ARMv8M/non_secure/portable/GCC/ARM_CM33/portasm.c
SPDXID: SPDXRef-File-portable-ARMv8M-nonsecure-portable-GCC-ARMCM33-portasm.c
FileChecksum: SHA1: 11936a5230bcf96cf0598f1429b9c041608bd823
LicenseConcluded: MIT
FileCopyrightText: NOASSERTION
FileComment: NOASSERTION

FileName: ./portable/Common/mpu_wrappers_v2.c
SPDXID: SPDXRef-File-portable-Common-mpuwrappersv2.c
FileChecksum: SHA1: aa44f02d24f0ab3eb45d1acb29a7790a445f09f3
LicenseConcluded: MIT
FileCopyrightText: NOASSERTION
FileComment: NOASSERTION

FileName: ./portable/Common/mpu_wrappers.c
SPDXID: SPDXRef-File-portable-Common-mpuwrappers.c
FileChecksum: SHA1: b21df42e64784b4d0eced7a645de0eaddc5e69db
LicenseConcluded: MIT
FileCopyrightText: NOASSERTION
FileComment: NOASSERTION

FileName: ./portable/BCC/16BitDOS/Flsh186/port.c
SPDXID: SPDXRef-File-portable-BCC-16BitDOS-Flsh186-port.c
FileChecksum: SHA1: 9fb6e09454abef3dbf3ce0fed0d8ae05b2365912
LicenseConcluded: MIT
FileCopyrightText: NOASSERTION
FileComment: NOASSERTION

FileName: ./portable/BCC/16BitDOS/PC/port.c
SPDXID: SPDXRef-File-portable-BCC-16BitDOS-PC-port.c
FileChecksum: SHA1: 2ff5b2325b3084b84ecc71145cabaef713f4f886
LicenseConcluded: MIT
FileCopyrightText: NOASSERTION
FileComment: NOASSERTION

FileName: ./portable/BCC/16BitDOS/common/portcomn.c
SPDXID: SPDXRef-File-portable-BCC-16BitDOS-common-portcomn.c
FileChecksum: SHA1: b35b0e1a9f6355bd558241fe5a0467a9295e4225
LicenseConcluded: MIT
FileCopyrightText: NOASSERTION
FileComment: NOASSERTION

FileName: ./portable/template/port.c
SPDXID: SPDXRef-File-portable-template-port.c
FileChecksum: SHA1: 2a3e995d1f6537fadb5f9d0f01a8fc1b39df6b9e
LicenseConcluded: MIT
FileCopyrightText: NOASSERTION
FileComment: NOASSERTION

FileName: ./portable/MemMang/heap_5.c
SPDXID: SPDXRef-File-portable-MemMang-heap5.c
FileChecksum: SHA1: 9e0c44164c6c6de0d0adc5f0fb1e273dbde43978
LicenseConcluded: MIT
FileCopyrightText: NOASSERTION
FileComment: NOASSERTION

FileName: ./portable/MemMang/heap_4.c
SPDXID: SPDXRef-File-portable-MemMang-heap4.c
FileChecksum: SHA1: c27d581289c142dd71d6b6e5be797afe5e049fac
LicenseConcluded: MIT
FileCopyrightText: NOASSERTION
FileComment: NOASSERTION

FileName: ./portable/MemMang/heap_3.c
SPDXID: SPDXRef-File-portable-MemMang-heap3.c
FileChecksum: SHA1: f860d3929e75a32045efef38367ef57e6cf0eab2
LicenseConcluded: MIT
FileCopyrightText: NOASSERTION
FileComment: NOASSERTION

FileName: ./portable/MemMang/heap_1.c
SPDXID: SPDXRef-File-portable-MemMang-heap1.c
FileChecksum: SHA1: c4f44d5b258b2e15752440d5544ca4c25668502f
LicenseConcluded: MIT
FileCopyrightText: NOASSERTION
FileComment: NOASSERTION

FileName: ./portable/MemMang/heap_2.c
SPDXID: SPDXRef-File-portable-MemMang-heap2.c
FileChecksum: SHA1: e2fd7d6f4e83252c5ac9df1a5c28cb6496fb1b86
LicenseConcluded: MIT
FileCopyrightText: NOASSERTION
FileComment: NOASSERTION

FileName: ./portable/Tasking/ARM_CM4F/port.c
SPDXID: SPDXRef-File-portable-Tasking-ARMCM4F-port.c
FileChecksum: SHA1: 8cecf9064ad12c76b74e12bef919bc5b471cf1cd
LicenseConcluded: MIT
FileCopyrightText: NOASSERTION
FileComment: NOASSERTION

FileName: ./portable/Paradigm/Tern_EE/small/port.c
SPDXID: SPDXRef-File-portable-Paradigm-TernEE-small-port.c
FileChecksum: SHA1: bdc0c5a52b2e4cc4a5fcbd717d3330a452332192
LicenseConcluded: MIT
FileCopyrightText: NOASSERTION
FileComment: NOASSERTION

FileName: ./portable/Paradigm/Tern_EE/large_untested/port.c
SPDXID: SPDXRef-File-portable-Paradigm-TernEE-largeuntested-port.c
FileChecksum: SHA1: 5cfdfc31674f25fabe7059fcc4f47e5503b768ce
LicenseConcluded: MIT
FileCopyrightText: NOASSERTION
FileComment: NOASSERTION

FileName: ./portable/MSVC-MingW/port.c
SPDXID: SPDXRef-File-portable-MSVC-MingW-port.c
FileChecksum: SHA1: 961688429de3e4e8338b1c433339be97b10174b7
LicenseConcluded: MIT
FileCopyrightText: NOASSERTION
FileComment: NOASSERTION

FileName: ./portable/CodeWarrior/HCS12/port.c
SPDXID: SPDXRef-File-portable-CodeWarrior-HCS12-port.c
FileChecksum: SHA1: 6da18428e34fb460eee3f95b2d05b0270630ce2d
LicenseConcluded: MIT
FileCopyrightText: NOASSERTION
FileComment: NOASSERTION

FileName: ./portable/CodeWarrior/ColdFire_V2/port.c
SPDXID: SPDXRef-File-portable-CodeWarrior-ColdFireV2-port.c
FileChecksum: SHA1: 01499fdb98f9ab899f76f19d8b518267a018da1b
LicenseConcluded: MIT
FileCopyrightText: NOASSERTION
FileComment: NOASSERTION

FileName: ./portable/CodeWarrior/ColdFire_V1/port.c
SPDXID: SPDXRef-File-portable-CodeWarrior-ColdFireV1-port.c
FileChecksum: SHA1: 7f9c925ff3c6a5a322c04b24e0700e1880b49167
LicenseConcluded: MIT
FileCopyrightText: NOASSERTION
FileComment: NOASSERTION

FileName: ./portable/MPLAB/PIC32MEC14xx/port.c
SPDXID: SPDXRef-File-portable-MPLAB-PIC32MEC14xx-port.c
FileChecksum: SHA1: 235d577ee1438f0f2c73807a11c423be5e77d758
LicenseConcluded: MIT
FileCopyrightText: NOASSERTION
FileComment: NOASSERTION

FileName: ./portable/MPLAB/PIC24_dsPIC/port.c
SPDXID: SPDXRef-File-portable-MPLAB-PIC24dsPIC-port.c
FileChecksum: SHA1: 41245e0c48f28767bda00eee9629f0731daeefcc
LicenseConcluded: MIT
FileCopyrightText: NOASSERTION
FileComment: NOASSERTION

FileName: ./portable/MPLAB/PIC18F/port.c
SPDXID: SPDXRef-File-portable-MPLAB-PIC18F-port.c
FileChecksum: SHA1: f2bcc500cb156637f110b416f80af9c3db2fcdba
LicenseConcluded: MIT
FileCopyrightText: NOASSERTION
FileComment: NOASSERTION

FileName: ./portable/MPLAB/PIC32MZ/port.c
SPDXID: SPDXRef-File-portable-MPLAB-PIC32MZ-port.c
FileChecksum: SHA1: 658936d19abdde01aa9e47ae7289b61e39115678
LicenseConcluded: MIT
FileCopyrightText: NOASSERTION
FileComment: NOASSERTION

FileName: ./portable/MPLAB/PIC32MX/port.c
SPDXID: SPDXRef-File-portable-MPLAB-PIC32MX-port.c
FileChecksum: SHA1: 3d8afd8f636f738bd1fbda7efb9df92d6d8a5857
LicenseConcluded: MIT
FileCopyrightText: NOASSERTION
FileComment: NOASSERTION

FileName: ./portable/GCC/STR75x/port.c
SPDXID: SPDXRef-File-portable-GCC-STR75x-port.c
FileChecksum: SHA1: be6a1e438d20db918284f89f8e1ad45c13ac4431
LicenseConcluded: MIT
FileCopyrightText: NOASSERTION
FileComment: NOASSERTION

FileName: ./portable/GCC/STR75x/portISR.c
SPDXID: SPDXRef-File-portable-GCC-STR75x-portISR.c
FileChecksum: SHA1: 5d4cafe37add64f6a1d4e117ae6a4957144a21b4
LicenseConcluded: MIT
FileCopyrightText: NOASSERTION
FileComment: NOASSERTION

FileName: ./portable/GCC/ARM_CR5/port.c
SPDXID: SPDXRef-File-portable-GCC-ARMCR5-port.c
FileChecksum: SHA1: a1a5e1d2b1699d459fc9017b948481e2a9968e7f
LicenseConcluded: MIT
FileCopyrightText: NOASSERTION
FileComment: NOASSERTION

FileName: ./portable/GCC/ARM_CM85/secure/secure_context.c
SPDXID: SPDXRef-File-portable-GCC-ARMCM85-secure-securecontext.c
FileChecksum: SHA1: b9283bfc2b63772aa24fd8bf3f923e57e2f4c2eb
LicenseConcluded: MIT
FileCopyrightText: NOASSERTION
FileComment: NOASSERTION

FileName: ./portable/GCC/ARM_CM85/secure/secure_context_port.c
SPDXID: SPDXRef-File-portable-GCC-ARMCM85-secure-securecontextport.c
FileChecksum: SHA1: cd31a095e79169f5bb371c04027df39669c671ae
LicenseConcluded: MIT
FileCopyrightText: NOASSERTION
FileComment: NOASSERTION

FileName: ./portable/GCC/ARM_CM85/secure/secure_heap.c
SPDXID: SPDXRef-File-portable-GCC-ARMCM85-secure-secureheap.c
FileChecksum: SHA1: 450b9d9674e2f561f9276fcc1b92b8076e6f4de7
LicenseConcluded: MIT
FileCopyrightText: NOASSERTION
FileComment: NOASSERTION

FileName: ./portable/GCC/ARM_CM85/secure/secure_init.c
SPDXID: SPDXRef-File-portable-GCC-ARMCM85-secure-secureinit.c
FileChecksum: SHA1: a2f502de907154a213fd8ebf4a87165ef3693fc7
LicenseConcluded: MIT
FileCopyrightText: NOASSERTION
FileComment: NOASSERTION

FileName: ./portable/GCC/ARM_CM85/non_secure/port.c
SPDXID: SPDXRef-File-portable-GCC-ARMCM85-nonsecure-port.c
FileChecksum: SHA1: f6bd76c2e19638dc20e0e058315dc950a3b0820e
LicenseConcluded: MIT
FileCopyrightText: NOASSERTION
FileComment: NOASSERTION

FileName: ./portable/GCC/ARM_CM85/non_secure/mpu_wrappers_v2_asm.c
SPDXID: SPDXRef-File-portable-GCC-ARMCM85-nonsecure-mpuwrappersv2asm.c
FileChecksum: SHA1: 17239cad5bfb222454503c215324cac12798f7c9
LicenseConcluded: MIT
FileCopyrightText: NOASSERTION
FileComment: NOASSERTION

FileName: ./portable/GCC/ARM_CM85/non_secure/portasm.c
SPDXID: SPDXRef-File-portable-GCC-ARMCM85-nonsecure-portasm.c
FileChecksum: SHA1: 11936a5230bcf96cf0598f1429b9c041608bd823
LicenseConcluded: MIT
FileCopyrightText: NOASSERTION
FileComment: NOASSERTION

FileName: ./portable/GCC/PPC440_Xilinx/port.c
SPDXID: SPDXRef-File-portable-GCC-PPC440Xilinx-port.c
FileChecksum: SHA1: 08c8913d71536782ad68acf75fc21543eee20045
LicenseConcluded: MIT
FileCopyrightText: NOASSERTION
FileComment: NOASSERTION

FileName: ./portable/GCC/H8S2329/port.c
SPDXID: SPDXRef-File-portable-GCC-H8S2329-port.c
FileChecksum: SHA1: b5e20a1979625ecbe777501ad47dfc3f66d2a12d
LicenseConcluded: MIT
FileCopyrightText: NOASSERTION
FileComment: NOASSERTION

FileName: ./portable/GCC/MicroBlazeV8/port_exceptions.c
SPDXID: SPDXRef-File-portable-GCC-MicroBlazeV8-portexceptions.c
FileChecksum: SHA1: 1cd4581d1716ee513d38a9c6fea73e8500f4902b
LicenseConcluded: MIT
FileCopyrightText: NOASSERTION
FileComment: NOASSERTION

FileName: ./portable/GCC/MicroBlazeV8/port.c
SPDXID: SPDXRef-File-portable-GCC-MicroBlazeV8-port.c
FileChecksum: SHA1: 70e4601ad0ac17947a121e1fdead8c6ccb57c054
LicenseConcluded: MIT
FileCopyrightText: NOASSERTION
FileComment: NOASSERTION

FileName: ./portable/GCC/ARM_CM3/port.c
SPDXID: SPDXRef-File-portable-GCC-ARMCM3-port.c
FileChecksum: SHA1: 0df4b7d3a95dcb22ada4f5171dd5054558aa6cad
LicenseConcluded: MIT
FileCopyrightText: NOASSERTION
FileComment: NOASSERTION

FileName: ./portable/GCC/ARM_CM7/r0p1/port.c
SPDXID: SPDXRef-File-portable-GCC-ARMCM7-r0p1-port.c
FileChecksum: SHA1: d2ab5cba00f52f6e96eee7f349b95ba720792364
LicenseConcluded: MIT
FileCopyrightText: NOASSERTION
FileComment: NOASSERTION

FileName: ./portable/GCC/ARM_CA9/port.c
SPDXID: SPDXRef-File-portable-GCC-ARMCA9-port.c
FileChecksum: SHA1: 86fb6fa7ccbad9ac8506912885a44a060e7945ab
LicenseConcluded: MIT
FileCopyrightText: NOASSERTION
FileComment: NOASSERTION

FileName: ./portable/GCC/RX700v3_DPFPU/port.c
SPDXID: SPDXRef-File-portable-GCC-RX700v3DPFPU-port.c
FileChecksum: SHA1: 466f814895f2e99522fa572343d44c80dc170d21
LicenseConcluded: MIT
FileCopyrightText: NOASSERTION
FileComment: NOASSERTION

FileName: ./portable/GCC/ARM_CRx_No_GIC/port.c
SPDXID: SPDXRef-File-portable-GCC-ARMCRxNoGIC-port.c
FileChecksum: SHA1: f035f1afeea066ecf9fb5212d9a8eb193223e31c
LicenseConcluded: MIT
FileCopyrightText: NOASSERTION
FileComment: NOASSERTION

FileName: ./portable/GCC/HCS12/port.c
SPDXID: SPDXRef-File-portable-GCC-HCS12-port.c
FileChecksum: SHA1: da57381349197c3733b86833c883fbbec6ab0cda
LicenseConcluded: MIT
FileCopyrightText: NOASSERTION
FileComment: NOASSERTION

FileName: ./portable/GCC/CORTUS_APS3/port.c
SPDXID: SPDXRef-File-portable-GCC-CORTUSAPS3-port.c
FileChecksum: SHA1: 80b60a39e1026a272d2fbd56b54dbe7a39b0b227
LicenseConcluded: MIT
FileCopyrightText: NOASSERTION
FileComment: NOASSERTION

FileName: ./portable/GCC/ATMega323/port.c
SPDXID: SPDXRef-File-portable-GCC-ATMega323-port.c
FileChecksum: SHA1: 733f6a9db8513101a7125c08923a6dc999c47692
LicenseConcluded: MIT
FileCopyrightText: NOASSERTION
FileComment: NOASSERTION

FileName: ./portable/GCC/ARM_CM55_NTZ/non_secure/port.c
SPDXID: SPDXRef-File-portable-GCC-ARMCM55NTZ-nonsecure-port.c
FileChecksum: SHA1: f6bd76c2e19638dc20e0e058315dc950a3b0820e
LicenseConcluded: MIT
FileCopyrightText: NOASSERTION
FileComment: NOASSERTION

FileName: ./portable/GCC/ARM_CM55_NTZ/non_secure/mpu_wrappers_v2_asm.c
SPDXID: SPDXRef-File-portable-GCC-ARMCM55NTZ-nonsecure-mpuwrappersv2asm.c
FileChecksum: SHA1: ca2795fc9320114f2e6d3f3e650dfd7e81ff4b83
LicenseConcluded: MIT
FileCopyrightText: NOASSERTION
FileComment: NOASSERTION

FileName: ./portable/GCC/ARM_CM55_NTZ/non_secure/portasm.c
SPDXID: SPDXRef-File-portable-GCC-ARMCM55NTZ-nonsecure-portasm.c
FileChecksum: SHA1: 3dbda857c79672c6eebaf96f335643435157f47c
LicenseConcluded: MIT
FileCopyrightText: NOASSERTION
FileComment: NOASSERTION

FileName: ./portable/GCC/ARM7_AT91FR40008/port.c
SPDXID: SPDXRef-File-portable-GCC-ARM7AT91FR40008-port.c
FileChecksum: SHA1: 2707016b301d2605ae5ed9e4500a1694839070ec
LicenseConcluded: MIT
FileCopyrightText: NOASSERTION
FileComment: NOASSERTION

FileName: ./portable/GCC/ARM7_AT91FR40008/portISR.c
SPDXID: SPDXRef-File-portable-GCC-ARM7AT91FR40008-portISR.c
FileChecksum: SHA1: 0e1c72f30dbf4f12b9249babafccda2c52ff93e6
LicenseConcluded: MIT
FileCopyrightText: NOASSERTION
FileComment: NOASSERTION

FileName: ./portable/GCC/ARM_CM23_NTZ/non_secure/port.c
SPDXID: SPDXRef-File-portable-GCC-ARMCM23NTZ-nonsecure-port.c
FileChecksum: SHA1: f6bd76c2e19638dc20e0e058315dc950a3b0820e
LicenseConcluded: MIT
FileCopyrightText: NOASSERTION
FileComment: NOASSERTION

FileName: ./portable/GCC/ARM_CM23_NTZ/non_secure/mpu_wrappers_v2_asm.c
SPDXID: SPDXRef-File-portable-GCC-ARMCM23NTZ-nonsecure-mpuwrappersv2asm.c
FileChecksum: SHA1: d25e20c9abee0edb07afaf8be795de131d27e04b
LicenseConcluded: MIT
FileCopyrightText: NOASSERTION
FileComment: NOASSERTION

FileName: ./portable/GCC/ARM_CM23_NTZ/non_secure/portasm.c
SPDXID: SPDXRef-File-portable-GCC-ARMCM23NTZ-nonsecure-portasm.c
FileChecksum: SHA1: 89e35a1318b5940d750d95ea75e132147a92c333
LicenseConcluded: MIT
FileCopyrightText: NOASSERTION
FileComment: NOASSERTION

FileName: ./portable/GCC/ARM_CM23/secure/secure_context.c
SPDXID: SPDXRef-File-portable-GCC-ARMCM23-secure-securecontext.c
FileChecksum: SHA1: b9283bfc2b63772aa24fd8bf3f923e57e2f4c2eb
LicenseConcluded: MIT
FileCopyrightText: NOASSERTION
FileComment: NOASSERTION

FileName: ./portable/GCC/ARM_CM23/secure/secure_context_port.c
SPDXID: SPDXRef-File-portable-GCC-ARMCM23-secure-securecontextport.c
FileChecksum: SHA1: a2da582746c3e0997ffe71e6e225a1ab32b91b6a
LicenseConcluded: MIT
FileCopyrightText: NOASSERTION
FileComment: NOASSERTION

FileName: ./portable/GCC/ARM_CM23/secure/secure_heap.c
SPDXID: SPDXRef-File-portable-GCC-ARMCM23-secure-secureheap.c
FileChecksum: SHA1: 450b9d9674e2f561f9276fcc1b92b8076e6f4de7
LicenseConcluded: MIT
FileCopyrightText: NOASSERTION
FileComment: NOASSERTION

FileName: ./portable/GCC/ARM_CM23/secure/secure_init.c
SPDXID: SPDXRef-File-portable-GCC-ARMCM23-secure-secureinit.c
FileChecksum: SHA1: a2f502de907154a213fd8ebf4a87165ef3693fc7
LicenseConcluded: MIT
FileCopyrightText: NOASSERTION
FileComment: NOASSERTION

FileName: ./portable/GCC/ARM_CM23/non_secure/port.c
SPDXID: SPDXRef-File-portable-GCC-ARMCM23-nonsecure-port.c
FileChecksum: SHA1: f6bd76c2e19638dc20e0e058315dc950a3b0820e
LicenseConcluded: MIT
FileCopyrightText: NOASSERTION
FileComment: NOASSERTION

FileName: ./portable/GCC/ARM_CM23/non_secure/mpu_wrappers_v2_asm.c
SPDXID: SPDXRef-File-portable-GCC-ARMCM23-nonsecure-mpuwrappersv2asm.c
FileChecksum: SHA1: d25e20c9abee0edb07afaf8be795de131d27e04b
LicenseConcluded: MIT
FileCopyrightText: NOASSERTION
FileComment: NOASSERTION

FileName: ./portable/GCC/ARM_CM23/non_secure/portasm.c
SPDXID: SPDXRef-File-portable-GCC-ARMCM23-nonsecure-portasm.c
FileChecksum: SHA1: a9264073894d15c80b053dfb58f352715f4be886
LicenseConcluded: MIT
FileCopyrightText: NOASSERTION
FileComment: NOASSERTION

FileName: ./portable/GCC/TriCore_1782/porttrap.c
SPDXID: SPDXRef-File-portable-GCC-TriCore1782-porttrap.c
FileChecksum: SHA1: 3032f0dbf05c7edc1ade5c20d308899a28d5cb34
LicenseConcluded: MIT
FileCopyrightText: NOASSERTION
FileComment: NOASSERTION

FileName: ./portable/GCC/TriCore_1782/port.c
SPDXID: SPDXRef-File-portable-GCC-TriCore1782-port.c
FileChecksum: SHA1: 2b10441e538b9ac02dea968ea94ea521b19ccbf3
LicenseConcluded: MIT
FileCopyrightText: NOASSERTION
FileComment: NOASSERTION

FileName: ./portable/GCC/MSP430F449/port.c
SPDXID: SPDXRef-File-portable-GCC-MSP430F449-port.c
FileChecksum: SHA1: 078384b1732d93bffc577bdf3be376cd83d94beb
LicenseConcluded: MIT
FileCopyrightText: NOASSERTION
FileComment: NOASSERTION

FileName: ./portable/GCC/ColdFire_V2/port.c
SPDXID: SPDXRef-File-portable-GCC-ColdFireV2-port.c
FileChecksum: SHA1: 50ed8f05aab8edb80dabd366b714a6312d6bc728
LicenseConcluded: MIT
FileCopyrightText: NOASSERTION
FileComment: NOASSERTION

FileName: ./portable/GCC/ARM_CM4F/port.c
SPDXID: SPDXRef-File-portable-GCC-ARMCM4F-port.c
FileChecksum: SHA1: cea22d5d2ecd6e0a5a9e9a81947dded4cc6366df
LicenseConcluded: MIT
FileCopyrightText: NOASSERTION
FileComment: NOASSERTION

FileName: ./portable/GCC/RX600/port.c
SPDXID: SPDXRef-File-portable-GCC-RX600-port.c
FileChecksum: SHA1: 7ec8535b2366db1120d95207b05a1023e4d3ae23
LicenseConcluded: MIT
FileCopyrightText: NOASSERTION
FileComment: NOASSERTION

FileName: ./portable/GCC/ARM_CM35P_NTZ/non_secure/port.c
SPDXID: SPDXRef-File-portable-GCC-ARMCM35PNTZ-nonsecure-port.c
FileChecksum: SHA1: f6bd76c2e19638dc20e0e058315dc950a3b0820e
LicenseConcluded: MIT
FileCopyrightText: NOASSERTION
FileComment: NOASSERTION

FileName: ./portable/GCC/ARM_CM35P_NTZ/non_secure/mpu_wrappers_v2_asm.c
SPDXID: SPDXRef-File-portable-GCC-ARMCM35PNTZ-nonsecure-mpuwrappersv2asm.c
FileChecksum: SHA1: ca2795fc9320114f2e6d3f3e650dfd7e81ff4b83
LicenseConcluded: MIT
FileCopyrightText: NOASSERTION
FileComment: NOASSERTION

FileName: ./portable/GCC/ARM_CM35P_NTZ/non_secure/portasm.c
SPDXID: SPDXRef-File-portable-GCC-ARMCM35PNTZ-nonsecure-portasm.c
FileChecksum: SHA1: 3dbda857c79672c6eebaf96f335643435157f47c
LicenseConcluded: MIT
FileCopyrightText: NOASSERTION
FileComment: NOASSERTION

FileName: ./portable/GCC/ARM_CM55/secure/secure_context.c
SPDXID: SPDXRef-File-portable-GCC-ARMCM55-secure-securecontext.c
FileChecksum: SHA1: b9283bfc2b63772aa24fd8bf3f923e57e2f4c2eb
LicenseConcluded: MIT
FileCopyrightText: NOASSERTION
FileComment: NOASSERTION

FileName: ./portable/GCC/ARM_CM55/secure/secure_context_port.c
SPDXID: SPDXRef-File-portable-GCC-ARMCM55-secure-securecontextport.c
FileChecksum: SHA1: cd31a095e79169f5bb371c04027df39669c671ae
LicenseConcluded: MIT
FileCopyrightText: NOASSERTION
FileComment: NOASSERTION

FileName: ./portable/GCC/ARM_CM55/secure/secure_heap.c
SPDXID: SPDXRef-File-portable-GCC-ARMCM55-secure-secureheap.c
FileChecksum: SHA1: 450b9d9674e2f561f9276fcc1b92b8076e6f4de7
LicenseConcluded: MIT
FileCopyrightText: NOASSERTION
FileComment: NOASSERTION

FileName: ./portable/GCC/ARM_CM55/secure/secure_init.c
SPDXID: SPDXRef-File-portable-GCC-ARMCM55-secure-secureinit.c
FileChecksum: SHA1: a2f502de907154a213fd8ebf4a87165ef3693fc7
LicenseConcluded: MIT
FileCopyrightText: NOASSERTION
FileComment: NOASSERTION

FileName: ./portable/GCC/ARM_CM55/non_secure/port.c
SPDXID: SPDXRef-File-portable-GCC-ARMCM55-nonsecure-port.c
FileChecksum: SHA1: f6bd76c2e19638dc20e0e058315dc950a3b0820e
LicenseConcluded: MIT
FileCopyrightText: NOASSERTION
FileComment: NOASSERTION

FileName: ./portable/GCC/ARM_CM55/non_secure/mpu_wrappers_v2_asm.c
SPDXID: SPDXRef-File-portable-GCC-ARMCM55-nonsecure-mpuwrappersv2asm.c
FileChecksum: SHA1: 17239cad5bfb222454503c215324cac12798f7c9
LicenseConcluded: MIT
FileCopyrightText: NOASSERTION
FileComment: NOASSERTION

FileName: ./portable/GCC/ARM_CM55/non_secure/portasm.c
SPDXID: SPDXRef-File-portable-GCC-ARMCM55-nonsecure-portasm.c
FileChecksum: SHA1: 11936a5230bcf96cf0598f1429b9c041608bd823
LicenseConcluded: MIT
FileCopyrightText: NOASSERTION
FileComment: NOASSERTION

FileName: ./portable/GCC/ARM_AARCH64/port.c
SPDXID: SPDXRef-File-portable-GCC-ARMAARCH64-port.c
FileChecksum: SHA1: 7cdf7baba08244e9c2c24b36f295b9754ea3431f
LicenseConcluded: MIT
FileCopyrightText: NOASSERTION
FileComment: NOASSERTION

FileName: ./portable/GCC/AVR32_UC3/port.c
SPDXID: SPDXRef-File-portable-GCC-AVR32UC3-port.c
FileChecksum: SHA1: 3702cf47f6d31d056feda04ef3f1738c2af83a32
LicenseConcluded: MIT
FileCopyrightText: NOASSERTION
FileComment: NOASSERTION

FileName: ./portable/GCC/ARM_CM35P/secure/secure_context.c
SPDXID: SPDXRef-File-portable-GCC-ARMCM35P-secure-securecontext.c
FileChecksum: SHA1: b9283bfc2b63772aa24fd8bf3f923e57e2f4c2eb
LicenseConcluded: MIT
FileCopyrightText: NOASSERTION
FileComment: NOASSERTION

FileName: ./portable/GCC/ARM_CM35P/secure/secure_context_port.c
SPDXID: SPDXRef-File-portable-GCC-ARMCM35P-secure-securecontextport.c
FileChecksum: SHA1: cd31a095e79169f5bb371c04027df39669c671ae
LicenseConcluded: MIT
FileCopyrightText: NOASSERTION
FileComment: NOASSERTION

FileName: ./portable/GCC/ARM_CM35P/secure/secure_heap.c
SPDXID: SPDXRef-File-portable-GCC-ARMCM35P-secure-secureheap.c
FileChecksum: SHA1: 450b9d9674e2f561f9276fcc1b92b8076e6f4de7
LicenseConcluded: MIT
FileCopyrightText: NOASSERTION
FileComment: NOASSERTION

FileName: ./portable/GCC/ARM_CM35P/secure/secure_init.c
SPDXID: SPDXRef-File-portable-GCC-ARMCM35P-secure-secureinit.c
FileChecksum: SHA1: a2f502de907154a213fd8ebf4a87165ef3693fc7
LicenseConcluded: MIT
FileCopyrightText: NOASSERTION
FileComment: NOASSERTION

FileName: ./portable/GCC/ARM_CM35P/non_secure/port.c
SPDXID: SPDXRef-File-portable-GCC-ARMCM35P-nonsecure-port.c
FileChecksum: SHA1: f6bd76c2e19638dc20e0e058315dc950a3b0820e
LicenseConcluded: MIT
FileCopyrightText: NOASSERTION
FileComment: NOASSERTION

FileName: ./portable/GCC/ARM_CM35P/non_secure/mpu_wrappers_v2_asm.c
SPDXID: SPDXRef-File-portable-GCC-ARMCM35P-nonsecure-mpuwrappersv2asm.c
FileChecksum: SHA1: 17239cad5bfb222454503c215324cac12798f7c9
LicenseConcluded: MIT
FileCopyrightText: NOASSERTION
FileComment: NOASSERTION

FileName: ./portable/GCC/ARM_CM35P/non_secure/portasm.c
SPDXID: SPDXRef-File-portable-GCC-ARMCM35P-nonsecure-portasm.c
FileChecksum: SHA1: 11936a5230bcf96cf0598f1429b9c041608bd823
LicenseConcluded: MIT
FileCopyrightText: NOASSERTION
FileComment: NOASSERTION

FileName: ./portable/GCC/PPC405_Xilinx/port.c
SPDXID: SPDXRef-File-portable-GCC-PPC405Xilinx-port.c
FileChecksum: SHA1: 0b7b04e33f75c98a73e1ceeacfbfaf6bfa6b888a
LicenseConcluded: MIT
FileCopyrightText: NOASSERTION
FileComment: NOASSERTION

FileName: ./portable/GCC/ARM_CM33_NTZ/non_secure/port.c
SPDXID: SPDXRef-File-portable-GCC-ARMCM33NTZ-nonsecure-port.c
FileChecksum: SHA1: f6bd76c2e19638dc20e0e058315dc950a3b0820e
LicenseConcluded: MIT
FileCopyrightText: NOASSERTION
FileComment: NOASSERTION

FileName: ./portable/GCC/ARM_CM33_NTZ/non_secure/mpu_wrappers_v2_asm.c
SPDXID: SPDXRef-File-portable-GCC-ARMCM33NTZ-nonsecure-mpuwrappersv2asm.c
FileChecksum: SHA1: ca2795fc9320114f2e6d3f3e650dfd7e81ff4b83
LicenseConcluded: MIT
FileCopyrightText: NOASSERTION
FileComment: NOASSERTION

FileName: ./portable/GCC/ARM_CM33_NTZ/non_secure/portasm.c
SPDXID: SPDXRef-File-portable-GCC-ARMCM33NTZ-nonsecure-portasm.c
FileChecksum: SHA1: 3dbda857c79672c6eebaf96f335643435157f47c
LicenseConcluded: MIT
FileCopyrightText: NOASSERTION
FileComment: NOASSERTION

FileName: ./portable/GCC/IA32_flat/port.c
SPDXID: SPDXRef-File-portable-GCC-IA32flat-port.c
FileChecksum: SHA1: 495d6a4620d070db565c3b7a97f9b9eadc173805
LicenseConcluded: MIT
FileCopyrightText: NOASSERTION
FileComment: NOASSERTION

FileName: ./portable/GCC/ARM_CM0/port.c
SPDXID: SPDXRef-File-portable-GCC-ARMCM0-port.c
FileChecksum: SHA1: 28300f7c7f0760dffef4b084252c041221d5aab7
LicenseConcluded: MIT
FileCopyrightText: NOASSERTION
FileComment: NOASSERTION

FileName: ./portable/GCC/ARM_CM0/mpu_wrappers_v2_asm.c
SPDXID: SPDXRef-File-portable-GCC-ARMCM0-mpuwrappersv2asm.c
FileChecksum: SHA1: 08b5bb890d85bd2d08e923cca55f00d205379678
LicenseConcluded: MIT
FileCopyrightText: NOASSERTION
FileComment: NOASSERTION

FileName: ./portable/GCC/ARM_CM0/portasm.c
SPDXID: SPDXRef-File-portable-GCC-ARMCM0-portasm.c
FileChecksum: SHA1: 058223038a2e13dc3c9783175efd52031dec9e2e
LicenseConcluded: MIT
FileCopyrightText: NOASSERTION
FileComment: NOASSERTION

FileName: ./portable/GCC/ARM_AARCH64_SRE/port.c
SPDXID: SPDXRef-File-portable-GCC-ARMAARCH64SRE-port.c
FileChecksum: SHA1: 9b568372d16d653385c4d5c32ed5eff1ccfd1f72
LicenseConcluded: MIT
FileCopyrightText: NOASSERTION
FileComment: NOASSERTION

FileName: ./portable/GCC/RX100/port.c
SPDXID: SPDXRef-File-portable-GCC-RX100-port.c
FileChecksum: SHA1: 1d5af3396c2a2d2893fcfec74a8b4ee9000e09dc
LicenseConcluded: MIT
FileCopyrightText: NOASSERTION
FileComment: NOASSERTION

FileName: ./portable/GCC/ARM7_AT91SAM7S/port.c
SPDXID: SPDXRef-File-portable-GCC-ARM7AT91SAM7S-port.c
FileChecksum: SHA1: 5d4336d1a2009d0b2c8bb33339374e1ccd0645a7
LicenseConcluded: MIT
FileCopyrightText: NOASSERTION
FileComment: NOASSERTION

FileName: ./portable/GCC/ARM7_AT91SAM7S/portISR.c
SPDXID: SPDXRef-File-portable-GCC-ARM7AT91SAM7S-portISR.c
FileChecksum: SHA1: 6030124b87191dcbe6ada1abb420510f8e3f8edb
LicenseConcluded: MIT
FileCopyrightText: NOASSERTION
FileComment: NOASSERTION

FileName: ./portable/GCC/ARM7_AT91SAM7S/lib_AT91SAM7X256.c
SPDXID: SPDXRef-File-portable-GCC-ARM7AT91SAM7S-libAT91SAM7X256.c
FileChecksum: SHA1: bfb6819c371f68f732dd07a9eacb43e9bfe6ef76
LicenseConcluded: MIT
FileCopyrightText: NOASSERTION
FileComment: NOASSERTION

FileName: ./portable/GCC/RX600v2/port.c
SPDXID: SPDXRef-File-portable-GCC-RX600v2-port.c
FileChecksum: SHA1: 5d4d651602ff7036bee35ff250fe02cab7953a2f
LicenseConcluded: MIT
FileCopyrightText: NOASSERTION
FileComment: NOASSERTION

FileName: ./portable/GCC/ARM_CM33/secure/secure_context.c
SPDXID: SPDXRef-File-portable-GCC-ARMCM33-secure-securecontext.c
FileChecksum: SHA1: b9283bfc2b63772aa24fd8bf3f923e57e2f4c2eb
LicenseConcluded: MIT
FileCopyrightText: NOASSERTION
FileComment: NOASSERTION

FileName: ./portable/GCC/ARM_CM33/secure/secure_context_port.c
SPDXID: SPDXRef-File-portable-GCC-ARMCM33-secure-securecontextport.c
FileChecksum: SHA1: cd31a095e79169f5bb371c04027df39669c671ae
LicenseConcluded: MIT
FileCopyrightText: NOASSERTION
FileComment: NOASSERTION

FileName: ./portable/GCC/ARM_CM33/secure/secure_heap.c
SPDXID: SPDXRef-File-portable-GCC-ARMCM33-secure-secureheap.c
FileChecksum: SHA1: 450b9d9674e2f561f9276fcc1b92b8076e6f4de7
LicenseConcluded: MIT
FileCopyrightText: NOASSERTION
FileComment: NOASSERTION

FileName: ./portable/GCC/ARM_CM33/secure/secure_init.c
SPDXID: SPDXRef-File-portable-GCC-ARMCM33-secure-secureinit.c
FileChecksum: SHA1: a2f502de907154a213fd8ebf4a87165ef3693fc7
LicenseConcluded: MIT
FileCopyrightText: NOASSERTION
FileComment: NOASSERTION

FileName: ./portable/GCC/ARM_CM33/non_secure/port.c
SPDXID: SPDXRef-File-portable-GCC-ARMCM33-nonsecure-port.c
FileChecksum: SHA1: f6bd76c2e19638dc20e0e058315dc950a3b0820e
LicenseConcluded: MIT
FileCopyrightText: NOASSERTION
FileComment: NOASSERTION

FileName: ./portable/GCC/ARM_CM33/non_secure/mpu_wrappers_v2_asm.c
SPDXID: SPDXRef-File-portable-GCC-ARMCM33-nonsecure-mpuwrappersv2asm.c
FileChecksum: SHA1: 17239cad5bfb222454503c215324cac12798f7c9
LicenseConcluded: MIT
FileCopyrightText: NOASSERTION
FileComment: NOASSERTION

FileName: ./portable/GCC/ARM_CM33/non_secure/portasm.c
SPDXID: SPDXRef-File-portable-GCC-ARMCM33-nonsecure-portasm.c
FileChecksum: SHA1: 11936a5230bcf96cf0598f1429b9c041608bd823
LicenseConcluded: MIT
FileCopyrightText: NOASSERTION
FileComment: NOASSERTION

FileName: ./portable/GCC/MicroBlazeV9/port_exceptions.c
SPDXID: SPDXRef-File-portable-GCC-MicroBlazeV9-portexceptions.c
FileChecksum: SHA1: 87e7cc95a87da99f9471757bd172369ee3c732da
LicenseConcluded: MIT
FileCopyrightText: NOASSERTION
FileComment: NOASSERTION

FileName: ./portable/GCC/MicroBlazeV9/port.c
SPDXID: SPDXRef-File-portable-GCC-MicroBlazeV9-port.c
FileChecksum: SHA1: c15613c2408e17ee5a0317527116bcaa3f533f04
LicenseConcluded: MIT
FileCopyrightText: NOASSERTION
FileComment: NOASSERTION

FileName: ./portable/GCC/RX200/port.c
SPDXID: SPDXRef-File-portable-GCC-RX200-port.c
FileChecksum: SHA1: f5eb0226c1b83d4af3da2161c5b2795e1c45e578
LicenseConcluded: MIT
FileCopyrightText: NOASSERTION
FileComment: NOASSERTION

FileName: ./portable/GCC/MicroBlaze/port.c
SPDXID: SPDXRef-File-portable-GCC-MicroBlaze-port.c
FileChecksum: SHA1: b8b17731e059b6afbd4858dcbf47fe112f20ef2b
LicenseConcluded: MIT
FileCopyrightText: NOASSERTION
FileComment: NOASSERTION

FileName: ./portable/GCC/RL78/port.c
SPDXID: SPDXRef-File-portable-GCC-RL78-port.c
FileChecksum: SHA1: b1ba760b46e778c07efcb5ad633ec653a7837197
LicenseConcluded: MIT
FileCopyrightText: NOASSERTION
FileComment: NOASSERTION

FileName: ./portable/GCC/RISC-V/port.c
SPDXID: SPDXRef-File-portable-GCC-RISC-V-port.c
FileChecksum: SHA1: d7d504456ecc1b1545767a19dbbbdc6a3f8516c6
LicenseConcluded: MIT
FileCopyrightText: NOASSERTION
FileComment: NOASSERTION

FileName: ./portable/GCC/ARM_CRx_MPU/port.c
SPDXID: SPDXRef-File-portable-GCC-ARMCRxMPU-port.c
FileChecksum: SHA1: 8a3d1095b41fa94e76062f870ea56643b292f90f
LicenseConcluded: MIT
FileCopyrightText: NOASSERTION
FileComment: NOASSERTION

FileName: ./portable/GCC/ARM7_LPC23xx/port.c
SPDXID: SPDXRef-File-portable-GCC-ARM7LPC23xx-port.c
FileChecksum: SHA1: a8764dc9c3d09f92d93c5c43a0aca19b457a9420
LicenseConcluded: MIT
FileCopyrightText: NOASSERTION
FileComment: NOASSERTION

FileName: ./portable/GCC/ARM7_LPC23xx/portISR.c
SPDXID: SPDXRef-File-portable-GCC-ARM7LPC23xx-portISR.c
FileChecksum: SHA1: d2f72cd864fbccaf84a3a3d0fe3b2ba8eb1a0a4e
LicenseConcluded: MIT
FileCopyrightText: NOASSERTION
FileComment: NOASSERTION

FileName: ./portable/GCC/ARM_CM3_MPU/port.c
SPDXID: SPDXRef-File-portable-GCC-ARMCM3MPU-port.c
FileChecksum: SHA1: 3e908fc394978f8b2ee9b090eb0922e272e2b289
LicenseConcluded: MIT
FileCopyrightText: NOASSERTION
FileComment: NOASSERTION

FileName: ./portable/GCC/ARM_CM3_MPU/mpu_wrappers_v2_asm.c
SPDXID: SPDXRef-File-portable-GCC-ARMCM3MPU-mpuwrappersv2asm.c
FileChecksum: SHA1: b21306e7c82540426b6502146298dc7969e5767a
LicenseConcluded: MIT
FileCopyrightText: NOASSERTION
FileComment: NOASSERTION

FileName: ./portable/GCC/ARM_CM4_MPU/port.c
SPDXID: SPDXRef-File-portable-GCC-ARMCM4MPU-port.c
FileChecksum: SHA1: 3453705df55aca4c2aa943837343c21209f9707b
LicenseConcluded: MIT
FileCopyrightText: NOASSERTION
FileComment: NOASSERTION

FileName: ./portable/GCC/ARM_CM4_MPU/mpu_wrappers_v2_asm.c
SPDXID: SPDXRef-File-portable-GCC-ARMCM4MPU-mpuwrappersv2asm.c
FileChecksum: SHA1: b21306e7c82540426b6502146298dc7969e5767a
LicenseConcluded: MIT
FileCopyrightText: NOASSERTION
FileComment: NOASSERTION

FileName: ./portable/GCC/NiosII/port.c
SPDXID: SPDXRef-File-portable-GCC-NiosII-port.c
FileChecksum: SHA1: 89a01b491be889253eeb6253e397a8fc9d874331
LicenseConcluded: MIT
FileCopyrightText: NOASSERTION
FileComment: NOASSERTION

FileName: ./portable/GCC/ARM_CM85_NTZ/non_secure/port.c
SPDXID: SPDXRef-File-portable-GCC-ARMCM85NTZ-nonsecure-port.c
FileChecksum: SHA1: f6bd76c2e19638dc20e0e058315dc950a3b0820e
LicenseConcluded: MIT
FileCopyrightText: NOASSERTION
FileComment: NOASSERTION

FileName: ./portable/GCC/ARM_CM85_NTZ/non_secure/mpu_wrappers_v2_asm.c
SPDXID: SPDXRef-File-portable-GCC-ARMCM85NTZ-nonsecure-mpuwrappersv2asm.c
FileChecksum: SHA1: ca2795fc9320114f2e6d3f3e650dfd7e81ff4b83
LicenseConcluded: MIT
FileCopyrightText: NOASSERTION
FileComment: NOASSERTION

FileName: ./portable/GCC/ARM_CM85_NTZ/non_secure/portasm.c
SPDXID: SPDXRef-File-portable-GCC-ARMCM85NTZ-nonsecure-portasm.c
FileChecksum: SHA1: 3dbda857c79672c6eebaf96f335643435157f47c
LicenseConcluded: MIT
FileCopyrightText: NOASSERTION
FileComment: NOASSERTION

FileName: ./portable/GCC/ARM7_LPC2000/port.c
SPDXID: SPDXRef-File-portable-GCC-ARM7LPC2000-port.c
FileChecksum: SHA1: 0e11ffcce71641c0a6ee036e5697fc81bd4e6a57
LicenseConcluded: MIT
FileCopyrightText: NOASSERTION
FileComment: NOASSERTION

FileName: ./portable/GCC/ARM7_LPC2000/portISR.c
SPDXID: SPDXRef-File-portable-GCC-ARM7LPC2000-portISR.c
FileChecksum: SHA1: 89a0d3ecbf4f1e95b04e0963788d724db13b0d89
LicenseConcluded: MIT
FileCopyrightText: NOASSERTION
FileComment: NOASSERTION

FileName: ./examples/cmake_example/main.c
SPDXID: SPDXRef-File-examples-cmakeexample-main.c
FileChecksum: SHA1: 5582c3f14ca1f062389533a5ad1a76a84daedb9e
LicenseConcluded: MIT
FileCopyrightText: NOASSERTION
FileComment: NOASSERTION

