/**
 ******************************************************************************
 * @file    app_monitor.h
 * <AUTHOR>
 * @version V2.0.0
 * @date    2025-07-03
 * @brief   Header for app_monitor.c module (Optimized EXTI+DMA version)
 ******************************************************************************
 */

#ifndef __APP_MONITOR_H
#define __APP_MONITOR_H

#ifdef __cplusplus
extern "C" {
#endif

/* Includes ------------------------------------------------------------------*/
#include "mod_ina226.h"
#include <stdbool.h>

/* Exported types ------------------------------------------------------------*/
/* Exported constants --------------------------------------------------------*/
/* Exported macro ------------------------------------------------------------*/
/* Exported functions ------------------------------------------------------- */

/**
 * @brief Initializes the monitoring application module with optimized EXTI+DMA.
 * @retval true if initialization is successful, false otherwise.
 */
bool App_Monitor_Init(void);

/**
 * @brief Retrieves the latest processed INA226 data (no queue, direct access).
 * @param data Pointer to a structure to store the data.
 * @param timeout_ms Timeout in milliseconds (unused, kept for compatibility).
 * @retval true if new data was available, false otherwise.
 */
bool App_Monitor_Get_Data(INA226_Data_t *data, uint32_t timeout_ms);

/**
 * @brief Get latest data directly from buffer (ultra-fast, no processing)
 * @param data Pointer to store the latest raw converted data
 * @retval true if new data available, false otherwise
 */
bool App_Monitor_Get_Data_Fast(INA226_Data_t *data);

/**
 * @brief Stops the monitoring application.
 * @retval None
 */
void App_Monitor_Stop(void);

/**
 * @brief Gets the monitoring status.
 * @retval true if monitoring is running, false otherwise.
 */
bool App_Monitor_IsRunning(void);

#ifdef __cplusplus
}
#endif

#endif /* __APP_MONITOR_H */
