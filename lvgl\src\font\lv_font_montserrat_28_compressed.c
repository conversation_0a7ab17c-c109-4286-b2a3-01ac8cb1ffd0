/*******************************************************************************
 * Size: 28 px
 * Bpp: 4
 * Opts: --bpp 4 --size 28 --font <PERSON>-Medium.ttf -r 0x20-0x7F,0xB0,0x2022 --font <PERSON><PERSON>ome5-Solid+Brands+Regular.woff -r 61441,61448,61451,61452,61452,61453,61457,61459,61461,61465,61468,61473,61478,61479,61480,61502,61507,61512,61515,61516,61517,61521,61522,61523,61524,61543,61544,61550,61552,61553,61556,61559,61560,61561,61563,61587,61589,61636,61637,61639,61641,61664,61671,61674,61683,61724,61732,61787,61931,62016,62017,62018,62019,62020,62087,62099,62212,62189,62810,63426,63650 --format lvgl -o lv_font_montserrat_28_compressed.c --force-fast-kern-format
 ******************************************************************************/

#ifdef LV_LVGL_H_INCLUDE_SIMPLE
    #include "lvgl.h"
#else
    #include "../../lvgl.h"
#endif

#ifndef LV_FONT_MONTSERRAT_28_COMPRESSED
    #define LV_FONT_MONTSERRAT_28_COMPRESSED 1
#endif

#if LV_FONT_MONTSERRAT_28_COMPRESSED

/*-----------------
 *    BITMAPS
 *----------------*/

/*Store the image of the glyphs*/
static LV_ATTRIBUTE_LARGE_CONST const uint8_t glyph_bitmap[] = {
    /* U+0020 " " */

    /* U+0021 "!" */
    0xc, 0xff, 0x28, 0x6, 0x10, 0x70, 0x3, 0x80,
    0x80, 0x7c, 0x20, 0x60, 0x1f, 0x18, 0x8, 0x0,
    0x43, 0xc0, 0x3f, 0x84, 0x4, 0x3, 0xe3, 0x3,
    0x0, 0x3c, 0x40, 0x0, 0x4e, 0xa0, 0x1f, 0x14,
    0x30, 0x3, 0xde, 0x58, 0x8, 0x1, 0xc1, 0x20,
    0x72,

    /* U+0022 "\"" */
    0x3f, 0xf0, 0x81, 0x7f, 0x88, 0x3, 0xf8, 0x40,
    0x2, 0x6, 0x0, 0x30, 0xf, 0xfe, 0x31, 0x80,
    0x61, 0x0, 0x8, 0x7, 0xf0, 0xb3, 0x80, 0x4c,
    0xc0,

    /* U+0023 "#" */
    0x0, 0xf5, 0x7a, 0x80, 0x66, 0xfa, 0x0, 0xfe,
    0x10, 0x60, 0xc, 0x20, 0x40, 0x1f, 0xcc, 0x4,
    0x1, 0xb8, 0x38, 0x3, 0xf8, 0x80, 0x40, 0x31,
    0x1, 0x0, 0x7e, 0x10, 0x20, 0xe, 0x60, 0x60,
    0xc, 0xbf, 0xf7, 0x1, 0x7f, 0xf1, 0x6, 0xff,
    0xb8, 0x3, 0xff, 0x90, 0xbf, 0xf3, 0x83, 0xff,
    0xeb, 0x3, 0xff, 0xb8, 0x3, 0x84, 0x38, 0x3,
    0x8, 0x30, 0x7, 0xf1, 0x1, 0x0, 0x62, 0x2,
    0x0, 0xfe, 0x60, 0x60, 0xd, 0xc1, 0xc0, 0x1f,
    0xc4, 0x4, 0x1, 0x8c, 0x8, 0x3, 0xf0, 0x80,
    0x80, 0x73, 0x3, 0x80, 0x64, 0xff, 0xb8, 0x7,
    0xff, 0x8c, 0x33, 0xfe, 0x0, 0xff, 0xe4, 0x27,
    0xfd, 0x0, 0xff, 0xfb, 0x40, 0xbf, 0xf0, 0x7,
    0x70, 0x70, 0x6, 0x60, 0x60, 0xf, 0xe2, 0x2,
    0x0, 0xc4, 0x4, 0x1, 0xfc, 0xc0, 0xc0, 0x1b,
    0x83, 0x80, 0x3f, 0x88, 0x8, 0x3, 0x10, 0x10,
    0x7, 0x0,

    /* U+0024 "$" */
    0x0, 0xfb, 0xf0, 0x3, 0xff, 0xbc, 0x31, 0x9c,
    0x7, 0xfb, 0x28, 0x1, 0xcd, 0xce, 0x62, 0x1,
    0x13, 0x5e, 0x90, 0x1, 0x64, 0x0, 0x6e, 0x5,
    0xa, 0x20, 0x46, 0x0, 0xb0, 0x5, 0x64, 0x0,
    0xbd, 0x75, 0x30, 0x82, 0x8, 0x32, 0x80, 0x7c,
    0xb2, 0x0, 0x10, 0x7, 0x80, 0x7f, 0xf0, 0x4,
    0x1, 0x80, 0x1f, 0xfc, 0x4, 0x10, 0x58, 0x0,
    0xff, 0xe0, 0x40, 0x1, 0xfe, 0xc0, 0x3f, 0xcd,
    0x42, 0x0, 0x40, 0x3e, 0x94, 0x0, 0xf2, 0xfc,
    0xa0, 0x4, 0x2d, 0x78, 0x60, 0x1c, 0x2d, 0x7c,
    0x2, 0x40, 0x3, 0xc3, 0x0, 0xf8, 0x40, 0xb7,
    0xc, 0x1, 0xe0, 0x1f, 0xf1, 0xe1, 0x1, 0x88,
    0x7, 0xff, 0x1, 0x40, 0x4, 0x8, 0x1, 0xfe,
    0x30, 0x1, 0x2d, 0xd1, 0x0, 0x7c, 0xa8, 0x6,
    0x3e, 0xb, 0xb9, 0xe, 0x3, 0x19, 0x40, 0xf,
    0xa, 0x60, 0x1, 0xbc, 0x1, 0x39, 0x80, 0xd9,
    0x80, 0x27, 0xa1, 0x4, 0x3, 0x1c, 0x72, 0x0,
    0x61, 0x7b, 0xef, 0x3, 0xfc, 0x70, 0xf, 0xfe,
    0xc0,

    /* U+0025 "%" */
    0x0, 0x3e, 0xff, 0x40, 0x7, 0xf6, 0xf9, 0x80,
    0x6a, 0x80, 0x62, 0x7c, 0x0, 0xfa, 0x49, 0x8c,
    0x2, 0x45, 0x4d, 0x9c, 0x73, 0x70, 0xe, 0x46,
    0x29, 0x0, 0xde, 0x10, 0x1, 0x40, 0x50, 0x7,
    0x48, 0x70, 0x7, 0x28, 0x18, 0x6, 0x32, 0x10,
    0xa, 0x49, 0x8c, 0x3, 0x84, 0x40, 0x1c, 0xc0,
    0x19, 0x18, 0xa4, 0x3, 0xc2, 0x20, 0xe, 0x60,
    0xd, 0x41, 0xc0, 0x1f, 0x28, 0x18, 0x6, 0x32,
    0x10, 0x91, 0x73, 0x0, 0xfb, 0xc2, 0x0, 0x28,
    0xa, 0x4, 0x62, 0x80, 0xf, 0xc8, 0xa9, 0xb3,
    0x8e, 0x6e, 0x14, 0x1c, 0x0, 0x58, 0x82, 0x80,
    0x6a, 0x80, 0x62, 0x7c, 0x9, 0x17, 0x31, 0xca,
    0x77, 0x56, 0x8, 0x4, 0xfb, 0xfd, 0x0, 0x8c,
    0x50, 0x14, 0x6d, 0xdc, 0x73, 0xa0, 0xf, 0xea,
    0xe, 0x2, 0x43, 0x91, 0x14, 0x1a, 0x18, 0x7,
    0xd2, 0x2e, 0x60, 0x81, 0x40, 0x1a, 0x81, 0x0,
    0x3c, 0x8c, 0x50, 0x0, 0xe0, 0x20, 0xc, 0x41,
    0xe0, 0x1c, 0x34, 0x1c, 0x1, 0x78, 0x8, 0x6,
    0x10, 0xf, 0xac, 0x5c, 0xc0, 0x23, 0xd, 0x0,
    0xda, 0x1a, 0x1, 0x91, 0x4a, 0x0, 0x32, 0xb,
    0x8, 0x0, 0x58, 0x54, 0x2, 0x1a, 0xe, 0x0,
    0xf5, 0xf, 0xc3, 0xf8, 0xd0, 0x6, 0xb1, 0x73,
    0x0, 0xf2, 0x62, 0xbc, 0x2e, 0x20, 0x0,

    /* U+0026 "&" */
    0x0, 0xc3, 0x3b, 0xfe, 0xd8, 0x0, 0xff, 0x1f,
    0x31, 0x0, 0x9, 0xf4, 0x3, 0xfb, 0x80, 0x6f,
    0xfd, 0x40, 0x50, 0x1, 0xf1, 0x90, 0x5a, 0x0,
    0x15, 0x81, 0x0, 0x3e, 0x70, 0x3, 0x0, 0x61,
    0x0, 0xfe, 0x60, 0x2, 0x0, 0x42, 0xc0, 0x80,
    0x1f, 0x11, 0x84, 0x10, 0x16, 0x8, 0xa4, 0x3,
    0xf7, 0x80, 0x3e, 0x34, 0x87, 0x44, 0x3, 0xf1,
    0xd0, 0x13, 0x82, 0x79, 0x80, 0x7f, 0x1c, 0x80,
    0x43, 0x42, 0x1, 0xfc, 0xd8, 0x62, 0xe0, 0x38,
    0x20, 0x11, 0x20, 0x4, 0xd2, 0xb, 0xf1, 0x40,
    0x58, 0x20, 0xb, 0xb8, 0xa, 0x41, 0x28, 0x41,
    0x68, 0xb, 0x4, 0x10, 0x30, 0x28, 0x1, 0x20,
    0x19, 0x68, 0xb, 0x18, 0x81, 0x0, 0xc0, 0x48,
    0x3, 0x96, 0x80, 0xa4, 0x10, 0xc0, 0x40, 0x44,
    0x1, 0xe5, 0xa0, 0xb, 0x80, 0x18, 0x0, 0xb0,
    0xf, 0xb0, 0x2, 0x81, 0x6, 0x20, 0x5e, 0x75,
    0x45, 0x9e, 0x60, 0x40, 0x2c, 0x10, 0xc4, 0x1,
    0x8a, 0xba, 0x61, 0x14, 0xdd, 0x1, 0x48, 0xd,
    0xd9, 0x88, 0x0, 0x29, 0x3c, 0xc0, 0xb4, 0x90,
    0x1, 0x24, 0xef, 0xfb, 0xad, 0x80, 0x32, 0xd8,
    0x0,

    /* U+0027 "'" */
    0x3f, 0xf0, 0x80, 0x61, 0x0, 0x8, 0x7, 0xe3,
    0x0, 0xf8, 0x59, 0xc0,

    /* U+0028 "(" */
    0x0, 0x93, 0xfc, 0x80, 0x14, 0x1, 0xa0, 0x1,
    0xc, 0x24, 0x2, 0xf0, 0x15, 0x0, 0x90, 0x1c,
    0x2, 0x50, 0x6, 0x80, 0x58, 0x0, 0x40, 0x9,
    0x40, 0x80, 0x31, 0x83, 0x80, 0x44, 0x0, 0xd0,
    0x8, 0x40, 0x2, 0x1, 0x38, 0x7, 0x84, 0x0,
    0x60, 0x1f, 0xe1, 0x0, 0x18, 0x4, 0xe0, 0x1e,
    0x10, 0x0, 0x80, 0x44, 0x0, 0xd0, 0xc, 0x60,
    0xe0, 0x19, 0x40, 0x80, 0x36, 0x0, 0x10, 0x2,
    0x50, 0x6, 0x80, 0x65, 0x7, 0x0, 0xdc, 0x2,
    0xa0, 0x12, 0x18, 0x48, 0x6, 0x80, 0x34,

    /* U+0029 ")" */
    0xd, 0xfb, 0x0, 0xd6, 0x8, 0x60, 0x13, 0x10,
    0x40, 0x6, 0xa0, 0x42, 0x0, 0x94, 0x0, 0xa0,
    0x11, 0x10, 0x2c, 0x3, 0x28, 0x18, 0x6, 0xe0,
    0x1, 0x80, 0x4a, 0x0, 0x50, 0x8, 0xc0, 0x1e,
    0x1, 0xe3, 0x0, 0xc2, 0x2, 0x1, 0x8c, 0x1c,
    0x3, 0xfe, 0x30, 0x70, 0xc, 0x20, 0x20, 0x1e,
    0x30, 0x8, 0xc0, 0x1e, 0x1, 0x28, 0x1, 0x40,
    0x2e, 0x0, 0x18, 0x4, 0xa0, 0x60, 0x11, 0x10,
    0x2c, 0x2, 0x50, 0x2, 0x80, 0x54, 0x8, 0x40,
    0x6, 0x20, 0x80, 0xa, 0xc1, 0xc, 0x0,

    /* U+002A "*" */
    0x0, 0xc7, 0xec, 0x1, 0xff, 0xc3, 0x1e, 0x80,
    0xe, 0x7e, 0x32, 0x37, 0xea, 0xc, 0xd8, 0x6,
    0x3d, 0x80, 0x60, 0x52, 0x6f, 0x50, 0x3, 0x20,
    0x4, 0x76, 0x20, 0x7, 0xf5, 0x10, 0x13, 0xd9,
    0x13, 0x80, 0x9d, 0x8, 0xb2, 0x67, 0x39, 0xf6,
    0x0, 0x93, 0x65, 0x81, 0x84, 0x3, 0xcc, 0x1,
    0xc5, 0xa, 0x1, 0x80,

    /* U+002B "+" */
    0x0, 0xf2, 0x20, 0x40, 0x3f, 0xeb, 0xb2, 0x0,
    0x7f, 0xf9, 0xc, 0xf8, 0x0, 0x46, 0x78, 0x4b,
    0x33, 0x80, 0x11, 0x99, 0x9c, 0x3, 0xff, 0x84,
    0x5f, 0xfc, 0x0, 0xaf, 0xfc, 0xc0, 0x1f, 0xfe,
    0xc0,

    /* U+002C "," */
    0x1a, 0xb4, 0x9, 0x54, 0xa0, 0x10, 0x0, 0x85,
    0x90, 0x18, 0x13, 0x85, 0x0, 0x10, 0x1c, 0x8,
    0x4c, 0x41, 0x2, 0x80, 0x0,

    /* U+002D "-" */
    0x25, 0x5f, 0x81, 0x2a, 0xbe, 0x20, 0xf, 0xe0,

    /* U+002E "." */
    0x1a, 0xc4, 0x8, 0x53, 0xa0, 0x30, 0x0, 0x84,
    0x10, 0xc8, 0x0,

    /* U+002F "/" */
    0x0, 0xfe, 0x58, 0x60, 0xf, 0xeb, 0x7e, 0x0,
    0xfc, 0x62, 0x16, 0x1, 0xfa, 0x80, 0x8c, 0x3,
    0xf3, 0x85, 0x0, 0x7e, 0x41, 0x5, 0x0, 0xfd,
    0xc0, 0x64, 0x1, 0xf9, 0x42, 0xc0, 0x3f, 0x28,
    0x1, 0x80, 0x3f, 0x78, 0x18, 0x80, 0x7e, 0x50,
    0xa0, 0xf, 0xcc, 0x0, 0x70, 0xf, 0xda, 0x8,
    0x20, 0x1f, 0xa, 0x7, 0x0, 0x7e, 0x70, 0x2,
    0x80, 0x7e, 0xa0, 0x50, 0xf, 0xc4, 0x61, 0xe0,
    0x1f, 0x94, 0x5, 0x40, 0x3f, 0x50, 0x38, 0x7,
    0xe2, 0x20, 0x68, 0x7, 0xea, 0x1, 0x40, 0xf,
    0xca, 0xe, 0x1, 0xf8, 0xc8, 0x28, 0x3, 0xf5,
    0x1, 0x18, 0x7, 0xe7, 0x5, 0x0, 0xfc, 0x82,
    0x14, 0x1, 0xfb, 0x40, 0x88, 0x1, 0xf8,

    /* U+0030 "0" */
    0x0, 0xe7, 0xcf, 0xf7, 0x5a, 0x80, 0x7e, 0x4e,
    0x83, 0x0, 0xa, 0x55, 0x88, 0x7, 0x25, 0x88,
    0x13, 0x3a, 0x80, 0x13, 0x4, 0x2, 0x1a, 0x0,
    0x46, 0xcc, 0x57, 0x20, 0x15, 0x80, 0x52, 0x20,
    0xee, 0x0, 0xc3, 0x64, 0xa, 0x60, 0x6, 0x1,
    0x80, 0xf, 0xa4, 0x1, 0x40, 0x62, 0xe, 0x1,
    0xf9, 0x4, 0x1c, 0x10, 0x1, 0x80, 0x1f, 0xca,
    0x2, 0x2e, 0x0, 0x30, 0x7, 0xf1, 0x80, 0x8,
    0xc0, 0x2, 0x1, 0xfd, 0xe0, 0x7, 0x0, 0xff,
    0xe2, 0x98, 0x0, 0x40, 0x3f, 0xbc, 0x0, 0xfc,
    0x0, 0x60, 0xf, 0xe3, 0x0, 0x12, 0x0, 0x30,
    0x3, 0xf9, 0x40, 0x44, 0x62, 0xe, 0x1, 0xf9,
    0x4, 0x1c, 0x0, 0xc0, 0x30, 0x1, 0xf4, 0x80,
    0x28, 0x1, 0x22, 0xe, 0xe0, 0xc, 0x36, 0x40,
    0xa6, 0x0, 0x1a, 0x0, 0x46, 0xcc, 0x57, 0x20,
    0x15, 0x80, 0x64, 0xb1, 0x2, 0x67, 0x50, 0x1,
    0xe0, 0x80, 0x72, 0x74, 0x18, 0x0, 0x52, 0xb0,
    0x40, 0x20,

    /* U+0031 "1" */
    0xcf, 0xff, 0x30, 0x7, 0xe9, 0x88, 0x94, 0x2,
    0x57, 0x7b, 0xc0, 0x3f, 0xff, 0xe0, 0x1f, 0xfe,
    0xc0,

    /* U+0032 "2" */
    0x0, 0x8e, 0x33, 0xbf, 0xd9, 0x4, 0x1, 0xc5,
    0x78, 0xe6, 0x20, 0x3, 0x7d, 0x60, 0x8, 0xb1,
    0x0, 0x8, 0xce, 0xa2, 0x0, 0x95, 0x0, 0x8c,
    0x1b, 0xae, 0x62, 0xb9, 0xc0, 0x16, 0x0, 0x2c,
    0x99, 0x8, 0x7, 0x41, 0x80, 0x90, 0x0, 0x58,
    0x3, 0xf2, 0x0, 0x18, 0x3, 0xff, 0x80, 0x20,
    0x6, 0x0, 0xff, 0xe0, 0x38, 0x9, 0x0, 0x7f,
    0xce, 0x20, 0xe0, 0x1f, 0xf2, 0x40, 0xc, 0x0,
    0x7f, 0x8e, 0xc0, 0x14, 0x20, 0x1f, 0xc9, 0x80,
    0xb, 0x40, 0xf, 0xe4, 0xb0, 0x5, 0xa0, 0x7,
    0xf2, 0x58, 0x2, 0xd0, 0x3, 0xf9, 0x2c, 0x1,
    0x68, 0x1, 0xfc, 0xb6, 0x0, 0xc4, 0x0, 0xfe,
    0x5a, 0x0, 0x61, 0x80, 0x7f, 0x2d, 0x0, 0x15,
    0x1d, 0xff, 0x0, 0x28, 0x2, 0x58, 0x8f, 0xe2,
    0x0, 0xff, 0xe2, 0x0,

    /* U+0033 "3" */
    0xf, 0xff, 0xfe, 0x8, 0x7, 0xff, 0x16, 0x62,
    0x3f, 0x10, 0x2, 0x0, 0xc, 0xef, 0xf0, 0x90,
    0x2b, 0x80, 0x7f, 0x1c, 0x81, 0x50, 0x7, 0xf0,
    0xe8, 0x3, 0xc0, 0x3f, 0xda, 0x21, 0x64, 0x1,
    0xfd, 0x26, 0x10, 0x80, 0x1f, 0xca, 0xc0, 0xe,
    0x61, 0x0, 0xfc, 0x40, 0x13, 0x4f, 0x38, 0x7,
    0xc8, 0xee, 0x52, 0x0, 0x44, 0x80, 0x78, 0xe2,
    0x15, 0xb6, 0x0, 0x63, 0x0, 0xff, 0x24, 0x80,
    0x2c, 0x3, 0xfe, 0x70, 0x1, 0x80, 0x7f, 0xf0,
    0x84, 0x14, 0x3, 0xf9, 0xc0, 0x4, 0x75, 0x88,
    0x1, 0xe5, 0x90, 0x5, 0x48, 0x1d, 0xf5, 0x44,
    0x27, 0x68, 0x1, 0x5, 0x4c, 0x0, 0x15, 0x77,
    0x31, 0x0, 0x21, 0xc0, 0x13, 0xd2, 0xa4, 0x0,
    0x13, 0x8d, 0x70, 0x0,

    /* U+0034 "4" */
    0x0, 0xfe, 0x4f, 0xf5, 0x80, 0x7f, 0xf0, 0x4a,
    0x80, 0x68, 0x3, 0xff, 0x83, 0xc2, 0x1a, 0x20,
    0x1f, 0xfc, 0x9, 0x30, 0x83, 0x0, 0xff, 0xe0,
    0x2b, 0x2, 0xb8, 0x7, 0xff, 0x0, 0xa8, 0xa,
    0x80, 0x3f, 0xf8, 0x3e, 0x0, 0xe0, 0xf, 0xfe,
    0xd, 0x10, 0x49, 0x80, 0x7f, 0xf0, 0x1d, 0x41,
    0x58, 0x2, 0xab, 0x70, 0xf, 0x1c, 0x1, 0x50,
    0x6, 0x54, 0xd0, 0xe, 0x1d, 0x0, 0x78, 0x7,
    0xff, 0x6, 0x84, 0x28, 0x80, 0x3f, 0xf8, 0x10,
    0x80, 0x46, 0xcd, 0xe0, 0x3, 0x36, 0x26, 0x0,
    0x14, 0xcf, 0xc0, 0x3, 0x99, 0x90, 0x40, 0x3f,
    0xf8, 0xbf, 0xff, 0xf0, 0x1, 0x7f, 0xe6, 0x0,
    0xff, 0xfe, 0x0,

    /* U+0035 "5" */
    0x0, 0xf, 0xff, 0xfc, 0x1, 0x88, 0x3, 0xff,
    0x86, 0xe0, 0x6, 0x88, 0xfc, 0x1, 0x88, 0x1,
    0x4e, 0xff, 0x80, 0x37, 0x0, 0x8, 0x3, 0xff,
    0x80, 0x20, 0x1, 0x0, 0xff, 0xe0, 0x10, 0x8,
    0x7, 0xff, 0x5, 0xc0, 0xc0, 0x3f, 0xf8, 0x24,
    0x5, 0xff, 0x76, 0x4a, 0x0, 0x78, 0x40, 0x38,
    0x4d, 0xaf, 0x8, 0x3, 0x44, 0x79, 0x8c, 0x0,
    0x78, 0x20, 0x13, 0xbf, 0xa7, 0x34, 0xc0, 0x60,
    0x3, 0xfe, 0x2d, 0x0, 0x38, 0x7, 0xff, 0x0,
    0x4c, 0x4, 0x3, 0xff, 0x8c, 0x62, 0x1, 0xf8,
    0x4c, 0x8, 0x1, 0xde, 0xc0, 0x1e, 0x2d, 0x0,
    0x50, 0x29, 0xc, 0xfd, 0xcc, 0x4d, 0xe9, 0x81,
    0x38, 0x2d, 0x10, 0x1, 0x19, 0xd9, 0x0, 0x9,
    0x80, 0x12, 0xed, 0xb1, 0x88, 0x9, 0x35, 0xd8,
    0x40, 0x0,

    /* U+0036 "6" */
    0x0, 0xe2, 0x8c, 0xef, 0xf6, 0xd2, 0x0, 0x70,
    0xd6, 0xb9, 0x88, 0x0, 0x96, 0xd0, 0x2, 0x1c,
    0x50, 0x2, 0x3c, 0xbb, 0x9, 0x20, 0x5, 0xa4,
    0x9, 0xb7, 0xd, 0x13, 0xde, 0x1, 0x31, 0x82,
    0x59, 0x0, 0x78, 0x80, 0x2b, 0x0, 0x50, 0x7,
    0xfc, 0x44, 0x5, 0x10, 0xf, 0xf9, 0x0, 0x18,
    0x1, 0xff, 0xc0, 0xe0, 0x2, 0x82, 0xd7, 0x7f,
    0x6c, 0x10, 0x4, 0x40, 0x15, 0xd2, 0x88, 0x9,
    0x3e, 0xb8, 0x7, 0x72, 0x3, 0x55, 0xca, 0x80,
    0x21, 0x80, 0xc0, 0x4, 0x4e, 0x95, 0x46, 0xab,
    0x0, 0x40, 0x70, 0x5, 0xc2, 0x1, 0xc8, 0xe0,
    0x2a, 0x60, 0x1, 0x30, 0xf, 0xb0, 0x0, 0x4a,
    0x1, 0xff, 0xc2, 0x11, 0x28, 0x9, 0x80, 0x7d,
    0x80, 0x2, 0x8, 0x0, 0x70, 0x80, 0x72, 0x38,
    0x12, 0x1, 0x40, 0x17, 0x4a, 0xa2, 0xd5, 0x80,
    0x24, 0x2, 0x79, 0x0, 0x35, 0x5d, 0x28, 0xd,
    0xa0, 0x6, 0x6e, 0x83, 0x10, 0x13, 0x8f, 0x40,
    0x0,

    /* U+0037 "7" */
    0x2f, 0xff, 0xfe, 0x15, 0x0, 0x7f, 0xf0, 0xcc,
    0x2, 0x48, 0x8f, 0xc6, 0x0, 0xc0, 0x8, 0xdd,
    0xff, 0x18, 0xa, 0x80, 0x7f, 0xcc, 0x0, 0x90,
    0xf, 0xfe, 0x5, 0x80, 0x18, 0xb, 0xfd, 0x0,
    0x1e, 0x42, 0x6, 0x10, 0xf, 0xfa, 0x0, 0x16,
    0x1, 0xff, 0x19, 0x81, 0x8, 0x3, 0xfe, 0x90,
    0x7, 0x80, 0x7f, 0xc2, 0xa0, 0x48, 0x1, 0xff,
    0x48, 0x2, 0xc0, 0x3f, 0xf8, 0xc, 0x2, 0xc0,
    0x1f, 0xf3, 0x8, 0x30, 0x7, 0xff, 0x2, 0xc0,
    0x12, 0x1, 0xff, 0x21, 0x2, 0x88, 0x7, 0xfd,
    0xe0, 0x9, 0x0, 0xff, 0x89, 0x0, 0xcc, 0x1,
    0xff, 0x58, 0x2, 0x40, 0x3f, 0xe1, 0x60, 0x15,
    0x0, 0xf8,

    /* U+0038 "8" */
    0x0, 0xcb, 0x5b, 0xfe, 0xda, 0x60, 0xf, 0x1e,
    0xd2, 0x90, 0x0, 0x96, 0x74, 0xc0, 0x22, 0xd2,
    0x2, 0x8a, 0xa4, 0x10, 0x16, 0x10, 0x2, 0x4,
    0x1f, 0x5d, 0x54, 0xfa, 0xe0, 0x8, 0x0, 0x20,
    0xc, 0x0, 0x7a, 0x8, 0x14, 0x0, 0x20, 0xa0,
    0x1f, 0x9c, 0x3, 0xcc, 0x1, 0xf9, 0x80, 0x32,
    0x81, 0x28, 0x7, 0x94, 0xc1, 0x40, 0x16, 0x21,
    0x54, 0x42, 0x22, 0xd5, 0x0, 0x6c, 0x0, 0x38,
    0x60, 0xb7, 0xba, 0xa5, 0x3, 0xc1, 0x0, 0xa8,
    0xc0, 0x3f, 0x1d, 0x0, 0x56, 0xe0, 0x75, 0xbf,
    0xed, 0x92, 0x7, 0xb0, 0x54, 0x4, 0xc5, 0x20,
    0x1, 0x36, 0xa8, 0x22, 0xf8, 0x2, 0x40, 0x3f,
    0x40, 0x3, 0xc4, 0x0, 0x40, 0x1f, 0x88, 0x0,
    0xc4, 0x0, 0x40, 0xf, 0xc8, 0x0, 0x6d, 0x0,
    0x49, 0x0, 0x78, 0xa4, 0x1, 0xe8, 0xa0, 0x5b,
    0x4c, 0x8a, 0xd5, 0xa4, 0x8, 0xa1, 0x4c, 0x0,
    0x59, 0xba, 0x95, 0x0, 0x35, 0x80, 0x53, 0xb0,
    0x62, 0x0, 0x13, 0x8d, 0x90, 0x0,

    /* U+0039 "9" */
    0x0, 0xcb, 0x7d, 0xfd, 0xb2, 0x60, 0x1e, 0x2c,
    0xa4, 0x10, 0x12, 0x6c, 0x90, 0xc, 0x3e, 0x60,
    0x73, 0x74, 0xe4, 0xd, 0x40, 0x14, 0x10, 0x3e,
    0x32, 0x2c, 0x6a, 0x82, 0xb0, 0x1, 0x80, 0xa0,
    0x3, 0xd4, 0x21, 0x0, 0x40, 0x5, 0x0, 0xfc,
    0xe0, 0x2a, 0x1, 0xff, 0xc3, 0xc2, 0x0, 0x28,
    0x7, 0xe7, 0x0, 0x28, 0x28, 0x14, 0x0, 0x7a,
    0x84, 0x0, 0x21, 0x0, 0x7, 0xd7, 0x55, 0x46,
    0xa8, 0x88, 0xc, 0xb, 0x4, 0xa, 0x2a, 0x8e,
    0x43, 0xa0, 0x1c, 0x7d, 0x6, 0x20, 0x29, 0x3c,
    0x62, 0x6, 0x1, 0x9f, 0x3b, 0xfa, 0xd8, 0x8,
    0x0, 0xe0, 0x1f, 0xfc, 0x4, 0x0, 0x68, 0x7,
    0xff, 0x2, 0x80, 0xc, 0x1, 0xff, 0x39, 0x82,
    0x88, 0x0, 0x44, 0x1, 0xe8, 0x80, 0xd, 0x80,
    0x53, 0xd5, 0xc, 0xf5, 0xce, 0x0, 0xd1, 0x0,
    0x9c, 0x15, 0xe6, 0x14, 0x40, 0xf4, 0xc0, 0x36,
    0x31, 0x80, 0x44, 0xd7, 0x84, 0x1, 0x0,

    /* U+003A ":" */
    0x2d, 0xe6, 0x8, 0x21, 0x90, 0x30, 0x0, 0x84,
    0x29, 0xd0, 0xd, 0x62, 0x0, 0x7f, 0xf4, 0xc6,
    0xb1, 0x2, 0x14, 0xe8, 0xc, 0x0, 0x21, 0x4,
    0x32, 0x0,

    /* U+003B ";" */
    0x2d, 0xe6, 0x8, 0x21, 0x90, 0x30, 0x0, 0x84,
    0x29, 0xd0, 0xd, 0x62, 0x0, 0x7f, 0xf4, 0xc6,
    0xad, 0x2, 0x55, 0x28, 0x4, 0x0, 0x21, 0x64,
    0x6, 0x4, 0xe1, 0x40, 0x4, 0x7, 0x2, 0x13,
    0x10, 0x40, 0xa0, 0x0,

    /* U+003C "<" */
    0x0, 0xff, 0x8e, 0x58, 0x3, 0xf0, 0xb6, 0xe3,
    0x0, 0x7c, 0x75, 0xd2, 0x40, 0x88, 0x0, 0x85,
    0xf7, 0x14, 0xa, 0x3a, 0xc8, 0x12, 0xba, 0x8,
    0x17, 0x35, 0xc4, 0x0, 0x56, 0xa0, 0x73, 0xf4,
    0x60, 0x1f, 0x86, 0x9c, 0x3, 0xf1, 0x28, 0xc,
    0xf5, 0x20, 0x7, 0xd5, 0xd2, 0x40, 0xb7, 0xae,
    0x20, 0x1c, 0x2d, 0xb8, 0xc0, 0x51, 0xd6, 0xa0,
    0x1e, 0x39, 0xf9, 0x30, 0x4a, 0x60, 0xf, 0xcd,
    0x98, 0x60, 0xf, 0xfe, 0x1, 0xcf, 0xb8,

    /* U+003D "=" */
    0x2f, 0xff, 0xfe, 0x3, 0x0, 0x7f, 0xf0, 0x8b,
    0x33, 0xff, 0x80, 0xe0, 0x67, 0xff, 0xc0, 0x10,
    0xf, 0xfe, 0x89, 0x9f, 0xff, 0x0, 0x4b, 0x33,
    0xff, 0x80, 0xe0, 0x1f, 0xfc, 0x20,

    /* U+003E ">" */
    0x1b, 0x50, 0xf, 0xf8, 0xd2, 0xba, 0x8, 0x3,
    0xf1, 0x30, 0x89, 0xf7, 0x14, 0x3, 0xe9, 0xea,
    0x40, 0x3a, 0xe9, 0x20, 0xf, 0x2d, 0xeb, 0x88,
    0x9b, 0x71, 0x80, 0x3c, 0x51, 0xd6, 0xa0, 0x72,
    0xc0, 0x1f, 0x93, 0x90, 0x3, 0xf8, 0xa3, 0xad,
    0x0, 0xd4, 0x3, 0x2e, 0x6b, 0x88, 0x9f, 0x70,
    0xc0, 0xe7, 0xe8, 0xc1, 0x2b, 0xa0, 0x80, 0x3,
    0x8c, 0x2, 0xfb, 0x6a, 0x1, 0xc6, 0x9, 0x5d,
    0x4, 0x1, 0xf1, 0x75, 0xa8, 0x7, 0xf8,

    /* U+003F "?" */
    0x0, 0x92, 0x77, 0xbf, 0xd9, 0x24, 0x1, 0x8a,
    0xec, 0xc4, 0x20, 0x3, 0x6d, 0x80, 0x1, 0xe2,
    0x0, 0xb4, 0x4b, 0x90, 0x1, 0xd8, 0x1c, 0x1,
    0x1d, 0x2e, 0xd1, 0xb0, 0x0, 0x80, 0x4f, 0xa7,
    0x0, 0xf3, 0x90, 0x8, 0x80, 0x54, 0x3, 0xf3,
    0x0, 0x7f, 0xf0, 0x94, 0x4, 0x40, 0x1f, 0xe8,
    0x10, 0x60, 0xf, 0xf4, 0x38, 0x1c, 0x0, 0x7f,
    0x4b, 0x81, 0xe0, 0x7, 0xf4, 0x30, 0x26, 0x0,
    0x7f, 0x13, 0x81, 0x58, 0x7, 0xf9, 0x40, 0x16,
    0x1, 0xff, 0x12, 0xa9, 0x0, 0x3f, 0xe5, 0xaa,
    0x10, 0x7, 0xff, 0x38, 0x61, 0x80, 0x3f, 0xf8,
    0x18, 0xf2, 0xc0, 0x1f, 0xf1, 0x0, 0x38, 0x3,
    0xfe, 0x91, 0x29, 0x0, 0xe0,

    /* U+0040 "@" */
    0x0, 0xfc, 0x4f, 0x7b, 0xff, 0x75, 0xc1, 0x80,
    0x7f, 0xf0, 0x93, 0x36, 0x10, 0x84, 0x84, 0x48,
    0xf9, 0x85, 0x0, 0xff, 0xaa, 0xcc, 0xa3, 0x7f,
    0xb7, 0xbf, 0x64, 0xcd, 0x56, 0x20, 0x1f, 0xe,
    0xa8, 0xd6, 0xb9, 0x0, 0x71, 0x36, 0x59, 0x26,
    0x8, 0x7, 0xe, 0x11, 0xfa, 0x80, 0x7f, 0xc9,
    0xaa, 0x58, 0x20, 0x1a, 0xc8, 0xf0, 0x40, 0x3,
    0x1b, 0xfe, 0xd8, 0x10, 0xff, 0x45, 0x21, 0x58,
    0x4, 0x6a, 0x1c, 0x1, 0x27, 0x39, 0x0, 0x9,
    0xfd, 0x0, 0x35, 0xa, 0x98, 0x2, 0x1, 0xc8,
    0x0, 0x96, 0x0, 0x7c, 0xc5, 0x20, 0xd8, 0x6,
    0x18, 0x8, 0x1, 0x40, 0x90, 0xa, 0x80, 0x76,
    0xc, 0xcb, 0x72, 0x1, 0xe6, 0x14, 0x14, 0x3,
    0x10, 0x2, 0x88, 0x51, 0x0, 0x73, 0x28, 0x7,
    0x90, 0x17, 0x41, 0x0, 0x2f, 0x2, 0x40, 0xf,
    0xa4, 0x3, 0xda, 0x6, 0x21, 0xe0, 0x13, 0x83,
    0x80, 0x7e, 0x30, 0xf, 0x10, 0x79, 0x80, 0x80,
    0x46, 0x6, 0x1, 0xfc, 0x20, 0x1c, 0x20, 0x26,
    0x2, 0x1, 0x18, 0x18, 0x7, 0xf0, 0x80, 0x70,
    0x80, 0xf0, 0x70, 0x4, 0xe0, 0xe0, 0x1f, 0x8c,
    0x3, 0xc4, 0x1e, 0x40, 0xa0, 0x17, 0x81, 0x20,
    0x7, 0xd2, 0x1, 0xed, 0x3, 0x40, 0x31, 0x0,
    0x28, 0x85, 0x10, 0x7, 0x32, 0x80, 0x4, 0x0,
    0x2a, 0x8, 0x28, 0x12, 0x1, 0x50, 0xe, 0xc1,
    0x92, 0x54, 0x81, 0x83, 0x29, 0xe8, 0x38, 0x2,
    0x1, 0xc8, 0x0, 0x96, 0x0, 0x7c, 0xdb, 0x51,
    0xb8, 0x0, 0x56, 0x19, 0xa0, 0x0, 0x6a, 0x1c,
    0x1, 0x27, 0x39, 0x80, 0x9, 0xfd, 0x11, 0x44,
    0x2, 0xf8, 0x1, 0xa8, 0x8f, 0x4, 0x0, 0x31,
    0x9f, 0xed, 0x81, 0x0, 0x2e, 0xff, 0x40, 0x7,
    0xb0, 0x8f, 0xd4, 0x3, 0xff, 0x94, 0x38, 0xa3,
    0x5a, 0xe6, 0x1, 0xc6, 0xf8, 0x1, 0xff, 0xc0,
    0x1a, 0xb3, 0x28, 0xcf, 0xee, 0xbf, 0x20, 0xd0,
    0x3, 0xff, 0x82, 0x99, 0x88, 0x41, 0x1c, 0x4b,
    0x3c, 0x80, 0x1f, 0x0,

    /* U+0041 "A" */
    0x0, 0xfe, 0x6f, 0xf6, 0x80, 0x7f, 0xf1, 0xac,
    0x0, 0x48, 0x1, 0xff, 0xc4, 0x42, 0x0, 0xbc,
    0x3, 0xff, 0x89, 0xe0, 0x2c, 0x8, 0x40, 0x1f,
    0xfc, 0x22, 0x40, 0x68, 0x0, 0x40, 0x7, 0xff,
    0xa, 0xc0, 0x12, 0x2a, 0xa, 0x20, 0x1f, 0xfc,
    0x1, 0x60, 0x51, 0x9, 0x0, 0x48, 0x7, 0xff,
    0x1, 0x80, 0x12, 0x0, 0x33, 0x3, 0x0, 0x7f,
    0xf0, 0x24, 0x8, 0xc0, 0x2a, 0x1, 0x60, 0xf,
    0xf2, 0x88, 0x58, 0x6, 0x61, 0xb, 0x0, 0xff,
    0x40, 0xb, 0x0, 0x73, 0x1, 0x20, 0x7, 0xe4,
    0x20, 0x60, 0xf, 0x48, 0x3, 0xc0, 0x3f, 0x78,
    0x3, 0x4c, 0xfc, 0x46, 0x8, 0x60, 0x1e, 0x24,
    0x0, 0x56, 0x67, 0xc6, 0x0, 0x90, 0xf, 0x58,
    0x7, 0xff, 0x9, 0x44, 0x3, 0xb, 0x1, 0xff,
    0xff, 0xa8, 0x1, 0x20, 0x19, 0x80, 0x12, 0x1,
    0xfe, 0x51, 0x6, 0x0, 0xd2, 0x2, 0xa0, 0x1f,
    0xf3, 0x0, 0xb0, 0x1, 0x44, 0x18, 0x3, 0xff,
    0x81, 0x20, 0xb, 0x0, 0x48, 0x2, 0x40, 0x3f,
    0xf8, 0x2, 0xa0, 0x48,

    /* U+0042 "B" */
    0x1f, 0xff, 0xf6, 0xd2, 0x80, 0x7f, 0xf0, 0xc9,
    0x6b, 0x48, 0x3, 0x8e, 0xef, 0xd2, 0xc0, 0x2,
    0xf0, 0xe, 0x74, 0x4f, 0x34, 0xf1, 0x81, 0x30,
    0x7, 0xff, 0x4, 0x7c, 0x1, 0x80, 0x1f, 0xfc,
    0x22, 0x0, 0x8, 0x7, 0xff, 0x8, 0xc0, 0x4,
    0x1, 0xff, 0xc2, 0xb0, 0x6, 0x80, 0x7f, 0xc2,
    0x96, 0xc0, 0x88, 0x0, 0xc9, 0xff, 0xee, 0xb4,
    0x5, 0xb0, 0xf, 0xfe, 0x22, 0x38, 0x7, 0x1d,
    0xdf, 0xd4, 0xe2, 0x28, 0xa0, 0xc, 0xe8, 0x9f,
    0x2c, 0x71, 0x82, 0xa0, 0x7, 0xff, 0xb, 0xc0,
    0x1a, 0x1, 0xff, 0xc2, 0x30, 0x2, 0x80, 0x7f,
    0xf0, 0x8c, 0x0, 0xe0, 0x1f, 0xfc, 0x28, 0x0,
    0x60, 0x4, 0xe8, 0x9f, 0x2c, 0x72, 0x0, 0xb0,
    0x4, 0x77, 0x7f, 0x53, 0x88, 0x16, 0x8, 0x7,
    0xff, 0x0, 0x96, 0xb4, 0x80,

    /* U+0043 "C" */
    0x0, 0xf1, 0xc6, 0x77, 0xf6, 0xd2, 0x80, 0x7e,
    0x2b, 0xc7, 0x31, 0x1, 0x25, 0xae, 0x50, 0xe,
    0x5d, 0x40, 0x1, 0x2b, 0xb2, 0x0, 0x6, 0xa0,
    0x2, 0x5a, 0x0, 0x26, 0x6d, 0x44, 0xdf, 0xc8,
    0x81, 0x80, 0x6, 0x80, 0xf, 0x66, 0x1, 0xe6,
    0xc7, 0xb0, 0x5, 0x80, 0x16, 0x0, 0x3f, 0x8a,
    0x0, 0x2, 0xa0, 0xa, 0x0, 0xff, 0xe1, 0x20,
    0x1, 0x80, 0x3f, 0xf8, 0x7a, 0x0, 0xc0, 0xf,
    0xfe, 0x19, 0x0, 0x18, 0x3, 0xff, 0xac, 0x40,
    0x6, 0x0, 0xff, 0xe1, 0xe8, 0x3, 0x0, 0x3f,
    0xf8, 0x68, 0x0, 0x60, 0xf, 0xfe, 0x18, 0xa8,
    0x2, 0x80, 0x3f, 0xf8, 0x76, 0x0, 0x58, 0x0,
    0xfe, 0x29, 0x0, 0x86, 0x80, 0xf, 0x66, 0x1,
    0xe6, 0xc6, 0xb0, 0x9, 0x68, 0x0, 0x99, 0xb5,
    0x10, 0xaf, 0x91, 0x3, 0x0, 0xcb, 0xa6, 0x0,
    0x25, 0x77, 0x28, 0x0, 0x6a, 0x0, 0x38, 0xb3,
    0xe, 0x62, 0x2, 0x4b, 0x5c, 0xa0, 0x0,

    /* U+0044 "D" */
    0x1f, 0xff, 0xee, 0xc9, 0x40, 0xf, 0xfe, 0x20,
    0x9b, 0x5e, 0xa0, 0x7, 0xc5, 0x11, 0xe6, 0x30,
    0x1, 0x5c, 0x0, 0x79, 0x9d, 0xfd, 0x39, 0xac,
    0x0, 0x78, 0x0, 0xff, 0xe1, 0x14, 0xd0, 0x1,
    0xd0, 0x3, 0xff, 0x86, 0xb2, 0x0, 0x80, 0xf,
    0xfe, 0x23, 0x10, 0x19, 0x80, 0x3f, 0xf8, 0x96,
    0x0, 0x40, 0xf, 0xfe, 0x22, 0x80, 0x34, 0x3,
    0xff, 0x88, 0x40, 0x6, 0x0, 0xff, 0xec, 0x10,
    0x1, 0x80, 0x3f, 0xf8, 0x8a, 0x0, 0xd0, 0xf,
    0xfe, 0x25, 0x80, 0x10, 0x3, 0xff, 0x86, 0xc4,
    0x6, 0x60, 0xf, 0xfe, 0x12, 0x48, 0x2, 0x0,
    0x3f, 0xf8, 0x23, 0x36, 0x0, 0x74, 0x0, 0xcc,
    0xef, 0xe9, 0xce, 0x60, 0x3, 0xc0, 0x7, 0x14,
    0x47, 0x98, 0xc0, 0x5, 0x70, 0x1, 0xff, 0xc1,
    0x13, 0x6b, 0xd4, 0x0, 0xc0,

    /* U+0045 "E" */
    0x1f, 0xff, 0xfe, 0xe, 0x0, 0x7f, 0xf2, 0xa,
    0x23, 0xfd, 0x20, 0x19, 0x9d, 0xff, 0xca, 0x1,
    0xff, 0xff, 0x0, 0xfe, 0x4f, 0xff, 0xe9, 0x0,
    0xff, 0xe4, 0x94, 0xcf, 0xf5, 0x0, 0x73, 0x37,
    0xf8, 0xc0, 0x3f, 0xff, 0x2c, 0xef, 0xff, 0x8,
    0x4, 0x51, 0x1f, 0xf1, 0x80, 0x7f, 0xf1, 0x0,

    /* U+0046 "F" */
    0x1f, 0xff, 0xfe, 0xe, 0x0, 0x7f, 0xf1, 0x8a,
    0x23, 0xfd, 0x20, 0x13, 0x3b, 0xff, 0x94, 0x3,
    0xff, 0xfe, 0x1, 0x33, 0x7f, 0x8c, 0x3, 0x14,
    0xcf, 0xf5, 0x0, 0x7f, 0xf1, 0xd3, 0xff, 0xfa,
    0x40, 0x3f, 0xff, 0xe0, 0x1f, 0xfd, 0x30,

    /* U+0047 "G" */
    0x0, 0xf1, 0xc6, 0x77, 0xfb, 0x69, 0x84, 0x3,
    0xe2, 0xbc, 0x73, 0x10, 0x1, 0x2c, 0xf3, 0x80,
    0x72, 0xea, 0x0, 0x9, 0x5d, 0xca, 0x20, 0x8,
    0xb0, 0x9, 0x28, 0x0, 0x79, 0xb5, 0x10, 0xae,
    0xb2, 0x6, 0x10, 0x1b, 0x0, 0x3e, 0x18, 0x7,
    0x93, 0x5f, 0x4, 0x20, 0x0, 0xb0, 0x1, 0xfe,
    0x81, 0x1, 0x60, 0x5, 0x0, 0x7f, 0xf0, 0x90,
    0x0, 0xc0, 0x1f, 0xfc, 0x3d, 0x0, 0x60, 0x7,
    0xff, 0xc, 0x80, 0xc, 0x1, 0xff, 0xc0, 0x11,
    0x0, 0x7f, 0xf1, 0x1f, 0xb8, 0x64, 0x0, 0x60,
    0xf, 0xfe, 0x1e, 0x80, 0x30, 0x3, 0xff, 0x86,
    0x80, 0x6, 0x0, 0xff, 0xe1, 0x8b, 0x0, 0x28,
    0x3, 0xff, 0x87, 0x0, 0x5, 0x80, 0xf, 0xfe,
    0x10, 0xd8, 0x1, 0xf0, 0xc0, 0x3c, 0x76, 0x1,
    0xc9, 0x40, 0x3, 0xcd, 0xa8, 0x84, 0xee, 0x18,
    0x9, 0x0, 0x4b, 0xa8, 0x0, 0x25, 0x77, 0x31,
    0x0, 0x1b, 0xc4, 0x3, 0x15, 0xe3, 0x98, 0x80,
    0x9, 0x67, 0xa4, 0x40,

    /* U+0048 "H" */
    0x1f, 0xf5, 0x80, 0x7f, 0xdf, 0xec, 0x0, 0xff,
    0xff, 0x80, 0x7f, 0xff, 0x13, 0xff, 0xfe, 0x0,
    0xff, 0xe6, 0x14, 0x47, 0xfc, 0x1, 0xe6, 0x77,
    0xff, 0x80, 0x3f, 0xff, 0xe0, 0x1f, 0xfe, 0xa0,

    /* U+0049 "I" */
    0x1f, 0xf5, 0x80, 0x7f, 0xff, 0xc0,

    /* U+004A "J" */
    0x0, 0x27, 0xff, 0xf4, 0x80, 0x7f, 0xf0, 0xd6,
    0x23, 0xce, 0x1, 0xc2, 0xef, 0xeb, 0x0, 0xff,
    0xff, 0x80, 0x7f, 0xfd, 0xdc, 0x0, 0x20, 0x6,
    0x0, 0xf6, 0x0, 0x38, 0x2a, 0x60, 0x3, 0xb,
    0x80, 0x1c, 0x5c, 0x1f, 0x61, 0xa3, 0xc0, 0xa,
    0x22, 0xf3, 0x2, 0x79, 0x71, 0x3, 0xa0, 0x1,
    0x65, 0x20, 0x80, 0xa4, 0xe0, 0x0,

    /* U+004B "K" */
    0x1f, 0xf5, 0x80, 0x7f, 0x17, 0x7f, 0x10, 0x7,
    0xff, 0x4, 0xb0, 0x43, 0x48, 0x3, 0xff, 0x80,
    0x38, 0x21, 0x66, 0x1, 0xff, 0xc0, 0x1c, 0x10,
    0xa4, 0x0, 0xff, 0xe0, 0xe, 0x10, 0x4a, 0x80,
    0x7f, 0xf0, 0x74, 0x82, 0x18, 0x3, 0xff, 0x83,
    0x86, 0xe, 0xe0, 0xf, 0xfe, 0xd, 0x98, 0x34,
    0x0, 0x7f, 0xf0, 0x6d, 0x1, 0x64, 0x3, 0xff,
    0x83, 0x48, 0x0, 0x30, 0xf, 0xfe, 0xc, 0xa8,
    0x4, 0xcc, 0x0, 0xff, 0x91, 0x80, 0x5c, 0x1,
    0x26, 0x1, 0xff, 0xc0, 0x1c, 0x87, 0x0, 0x68,
    0x80, 0x7f, 0x87, 0x8, 0x21, 0x0, 0x74, 0x3,
    0xf8, 0x70, 0x80, 0x2b, 0x30, 0x3a, 0x0, 0xfc,
    0xa4, 0x1, 0xda, 0x20, 0xae, 0x1, 0xff, 0xc2,
    0x1d, 0x0, 0x42, 0x0, 0x7f, 0xf0, 0x8e, 0xc0,
    0x14, 0x40, 0x1f, 0xfc, 0x24, 0x80, 0x1f, 0x0,
    0xff, 0xe1, 0xbb, 0x1, 0x58, 0x0,

    /* U+004C "L" */
    0x1f, 0xf5, 0x80, 0x7f, 0xff, 0xc0, 0x3f, 0xff,
    0xe0, 0x1f, 0xff, 0xf0, 0xf, 0xfe, 0x8b, 0x3b,
    0xff, 0x88, 0x2, 0x28, 0x8f, 0xf3, 0x80, 0x7f,
    0xf0, 0xc0,

    /* U+004D "M" */
    0x1f, 0xf4, 0x0, 0x7f, 0xf0, 0xf3, 0xf0, 0x2,
    0x72, 0x0, 0xff, 0xe0, 0xb1, 0x80, 0x7a, 0x0,
    0x3f, 0xf8, 0x30, 0x1, 0xf2, 0xa0, 0x7, 0xfd,
    0x2, 0x1, 0xfa, 0x40, 0x3f, 0xc4, 0xe0, 0x1f,
    0x8, 0x13, 0x0, 0x7f, 0x40, 0x7, 0xe4, 0x30,
    0x81, 0x0, 0xf9, 0x14, 0x20, 0x40, 0x3d, 0xe0,
    0x30, 0x1, 0xf4, 0x81, 0x38, 0x7, 0xc6, 0xc0,
    0xc4, 0x1, 0xcc, 0x41, 0x20, 0x1f, 0xd0, 0x0,
    0x90, 0xe, 0x80, 0x44, 0x0, 0x7f, 0xc, 0x2,
    0x28, 0x5, 0x2, 0x12, 0x1, 0xff, 0x39, 0x5,
    0x80, 0x9, 0xc1, 0xc8, 0x3, 0xff, 0x81, 0x20,
    0x2e, 0x12, 0x3, 0x0, 0x1f, 0xfc, 0x14, 0x50,
    0x86, 0x40, 0x80, 0xf, 0xfe, 0x1c, 0x0, 0x24,
    0xd, 0x80, 0x3f, 0xf8, 0x64, 0xe0, 0x17, 0x80,
    0x7f, 0xf1, 0x60, 0x41, 0x4c, 0x3, 0xff, 0x8d,
    0x4d, 0x60, 0x1f, 0xfc, 0x74, 0x91, 0x0, 0xff,
    0xe9, 0x80,

    /* U+004E "N" */
    0x1f, 0xf5, 0x0, 0x7f, 0xdf, 0xec, 0x0, 0x95,
    0x80, 0x3f, 0xf8, 0xb2, 0x60, 0x1f, 0xfc, 0x5d,
    0x10, 0xf, 0xfe, 0x20, 0xe8, 0x7, 0xff, 0x8,
    0xc8, 0xe, 0x40, 0x3f, 0xf8, 0x2f, 0xe0, 0x6,
    0x50, 0xf, 0xfe, 0x9, 0x50, 0x2, 0x88, 0x3,
    0xff, 0x82, 0xac, 0x0, 0xf1, 0x0, 0xff, 0xe0,
    0xc9, 0x81, 0x50, 0x7, 0xff, 0xb, 0x44, 0x12,
    0x0, 0x3f, 0xf8, 0x23, 0xa0, 0x7, 0x40, 0xf,
    0xfe, 0x9, 0xc8, 0x2, 0xc8, 0x3, 0xff, 0x82,
    0xca, 0x0, 0xf0, 0xf, 0xfe, 0x15, 0x18, 0x15,
    0x0, 0x7f, 0xf0, 0xb8, 0x41, 0x40, 0x3f, 0xf8,
    0x45, 0x40, 0x1f, 0xfc, 0x54, 0x80, 0xf, 0xfe,
    0x2b, 0xa8, 0x7, 0xff, 0x16, 0x88, 0x0,

    /* U+004F "O" */
    0x0, 0xf1, 0xc6, 0x77, 0xfb, 0x69, 0x80, 0x3f,
    0xe2, 0xbc, 0x73, 0x10, 0x1, 0x2c, 0xf3, 0x80,
    0x7e, 0x5d, 0x40, 0x1, 0x2b, 0xb2, 0x0, 0x6,
    0x2c, 0x40, 0x39, 0x28, 0x0, 0x99, 0xb5, 0x13,
    0x7f, 0x20, 0x4, 0xd0, 0xc, 0x34, 0x0, 0x7b,
    0x30, 0xf, 0x36, 0x90, 0x1c, 0x80, 0x50, 0x20,
    0xb0, 0x1, 0xfc, 0x5e, 0x0, 0x62, 0x1, 0x60,
    0x5, 0x0, 0x7f, 0xc5, 0x0, 0xb, 0x4, 0x0,
    0x30, 0x7, 0xff, 0x5, 0x80, 0x8, 0x1a, 0x0,
    0xc0, 0xf, 0xfe, 0x8, 0x90, 0x10, 0x10, 0x1,
    0x80, 0x3f, 0xf8, 0x4c, 0x0, 0x20, 0xf, 0xfe,
    0x51, 0x0, 0x18, 0x3, 0xff, 0x84, 0xc0, 0x2,
    0xd0, 0x6, 0x0, 0x7f, 0xf0, 0x44, 0x80, 0x81,
    0x0, 0xc, 0x1, 0xff, 0xc1, 0x60, 0x2, 0x0,
    0xb0, 0x2, 0x80, 0x3f, 0xe2, 0x80, 0x5, 0x80,
    0x20, 0x41, 0x60, 0x3, 0xf8, 0xbc, 0x0, 0xc4,
    0x0, 0x1a, 0x0, 0x3d, 0x98, 0x7, 0x9b, 0x48,
    0xe, 0x40, 0x32, 0x50, 0x1, 0x33, 0x6a, 0x26,
    0xfe, 0x40, 0x7, 0xa0, 0x1e, 0x5d, 0x40, 0x1,
    0x2b, 0xb2, 0x0, 0x51, 0x82, 0x1, 0xf1, 0x5e,
    0x39, 0x88, 0x0, 0x96, 0x7d, 0xc0, 0x38,

    /* U+0050 "P" */
    0x1f, 0xff, 0xed, 0xa5, 0x0, 0xff, 0xe1, 0x12,
    0xd6, 0x98, 0x7, 0x14, 0x47, 0x3a, 0x80, 0xb,
    0xc, 0x3, 0x33, 0xbf, 0x45, 0x7a, 0x80, 0x38,
    0x3, 0xff, 0x83, 0x48, 0x4, 0xa0, 0x1f, 0xfc,
    0x1f, 0x0, 0x60, 0x7, 0xff, 0x5, 0x40, 0x4,
    0x1, 0xff, 0xd2, 0x50, 0x1, 0x0, 0x7f, 0xf0,
    0x7c, 0x1, 0x80, 0x1f, 0xfc, 0x9, 0x40, 0x25,
    0x0, 0x99, 0xdf, 0xd5, 0xec, 0x0, 0xe0, 0xc,
    0x51, 0x1e, 0x50, 0x1, 0x61, 0x80, 0x7f, 0xc4,
    0xb5, 0xa6, 0x1, 0xc9, 0xff, 0xed, 0xa5, 0x0,
    0xff, 0xff, 0x80, 0x7f, 0xf0, 0x80,

    /* U+0051 "Q" */
    0x0, 0xf1, 0x46, 0x77, 0xfb, 0x69, 0x80, 0x3f,
    0xf8, 0x5, 0x7a, 0xe6, 0x20, 0x2, 0x59, 0xe7,
    0x0, 0xfe, 0x4d, 0x40, 0x1, 0x2b, 0xb2, 0x0,
    0x6, 0x2c, 0x40, 0x3c, 0x96, 0x0, 0x3c, 0xda,
    0x89, 0xbf, 0x80, 0x2, 0x68, 0x7, 0xd, 0x0,
    0x1f, 0xc, 0x3, 0xcf, 0xa4, 0x7, 0x0, 0x1a,
    0x4, 0x16, 0x0, 0x3f, 0x8b, 0xc0, 0xe, 0x40,
    0x13, 0x0, 0x28, 0x3, 0xfe, 0x27, 0x0, 0x50,
    0x1, 0x40, 0xc, 0x1, 0xff, 0xc1, 0x90, 0x2,
    0x80, 0x34, 0x1, 0x80, 0x1f, 0xfc, 0x11, 0x20,
    0x20, 0x0, 0x80, 0x18, 0x3, 0xff, 0x84, 0xc0,
    0x2, 0x3, 0x0, 0xff, 0xe2, 0x88, 0x0, 0xc0,
    0x80, 0xc, 0x1, 0xff, 0xc2, 0x60, 0x0, 0x86,
    0x80, 0x34, 0x3, 0xff, 0x82, 0x26, 0x4, 0x0,
    0x40, 0x3, 0x0, 0x7f, 0xf0, 0x58, 0x0, 0xa0,
    0x1, 0x50, 0x19, 0x0, 0xff, 0x8a, 0x0, 0x14,
    0x1, 0x40, 0x1, 0x9c, 0x3, 0xf8, 0x7c, 0x0,
    0xc4, 0x1, 0x15, 0x0, 0x22, 0x88, 0x3, 0xcb,
    0xe4, 0x7, 0x20, 0x1c, 0xb2, 0x0, 0x5d, 0xc8,
    0x77, 0x4f, 0x50, 0x81, 0xe8, 0x7, 0xcd, 0x84,
    0x0, 0x37, 0x88, 0x30, 0x80, 0x23, 0x4, 0x3,
    0xf1, 0xed, 0x29, 0x0, 0x62, 0x9e, 0x70, 0xf,
    0xfe, 0x2, 0xd6, 0xfc, 0x0, 0x8, 0x82, 0x1,
    0xa4, 0xc0, 0x3f, 0xe7, 0x90, 0x5, 0xd2, 0xa3,
    0x6b, 0x70, 0x7, 0xff, 0x1, 0xac, 0x41, 0x6a,
    0xe4, 0x82, 0x40, 0x3f, 0xf8, 0x29, 0xd2, 0x62,
    0x4, 0xfc, 0x80,

    /* U+0052 "R" */
    0x1f, 0xff, 0xed, 0xa5, 0x0, 0xff, 0xe1, 0x92,
    0xd6, 0x98, 0x7, 0x8a, 0x23, 0x9d, 0x40, 0x5,
    0x86, 0x1, 0xcc, 0xef, 0xd1, 0x5e, 0xa0, 0xe,
    0x0, 0xff, 0xe1, 0x52, 0x1, 0x28, 0x7, 0xff,
    0xb, 0xc0, 0x18, 0x1, 0xff, 0xc2, 0x50, 0x1,
    0x0, 0x7f, 0xf5, 0x14, 0x0, 0x40, 0x1f, 0xfc,
    0x2f, 0x0, 0x60, 0x7, 0xff, 0x6, 0x50, 0x5,
    0x40, 0x33, 0x37, 0x9e, 0x79, 0x80, 0x1a, 0x1,
    0xc5, 0x33, 0xd0, 0xc2, 0x5, 0x86, 0x1, 0xff,
    0xc2, 0x4d, 0x30, 0xf, 0x27, 0xff, 0x8c, 0x10,
    0xc0, 0x3f, 0xf8, 0x5c, 0x0, 0xd0, 0xf, 0xfe,
    0x11, 0x48, 0xd, 0x0, 0x7f, 0xf0, 0x99, 0x1,
    0x54, 0x1, 0xff, 0xc2, 0xa1, 0xb, 0x10, 0xf,
    0xfe, 0x8, 0xd0, 0xd, 0x0,

    /* U+0053 "S" */
    0x0, 0xcd, 0x7b, 0xfe, 0xec, 0x83, 0x0, 0xe5,
    0xe9, 0x42, 0x0, 0x9, 0xbe, 0x60, 0x80, 0x9,
    0x42, 0x6, 0xf3, 0x28, 0x40, 0x1, 0x98, 0x1,
    0x20, 0xa, 0xc8, 0x66, 0x3d, 0xfc, 0xb0, 0x81,
    0x90, 0x32, 0x80, 0x7c, 0xd2, 0x0, 0x60, 0x7,
    0x80, 0x7f, 0xf0, 0x18, 0x1, 0x80, 0x1f, 0xfc,
    0x3, 0x10, 0x58, 0x0, 0xff, 0xe0, 0x40, 0x1,
    0xfe, 0x98, 0x80, 0x3f, 0x35, 0x8, 0x1, 0x67,
    0x75, 0x28, 0x1, 0xe5, 0xe9, 0x40, 0x8, 0x9a,
    0xf4, 0xc0, 0x3c, 0xd7, 0xf6, 0xe6, 0x0, 0x2c,
    0x30, 0xf, 0xc9, 0x19, 0xa8, 0x0, 0xe0, 0xf,
    0xf8, 0xac, 0xc0, 0x84, 0x3, 0xff, 0x80, 0x80,
    0x2, 0x5, 0x0, 0xff, 0x10, 0x0, 0x96, 0xad,
    0x0, 0x3e, 0x65, 0x4, 0x1f, 0x4, 0xbe, 0x97,
    0x66, 0x46, 0xc8, 0x2, 0x2, 0xa0, 0x40, 0x5a,
    0x26, 0x4e, 0x40, 0x58, 0x60, 0x7, 0xea, 0x62,
    0x10, 0x12, 0x5a, 0xd3, 0x0, 0x0,

    /* U+0054 "T" */
    0xef, 0xff, 0xfe, 0x1a, 0x80, 0x7f, 0xf1, 0x62,
    0x3c, 0xe0, 0x3, 0x88, 0xf3, 0xb3, 0xbf, 0x50,
    0x1, 0x5d, 0xfc, 0x40, 0x1f, 0xff, 0xf0, 0xf,
    0xff, 0xf8, 0x7, 0xff, 0xfc, 0x3, 0xff, 0xc6,

    /* U+0055 "U" */
    0x3f, 0xf4, 0x80, 0x7f, 0x9b, 0xfc, 0xa0, 0x1f,
    0xff, 0xf0, 0xf, 0xff, 0xf8, 0x7, 0xff, 0x90,
    0x40, 0x6, 0x1, 0xfe, 0x10, 0x0, 0x90, 0x1,
    0xc0, 0x3f, 0xda, 0x0, 0x60, 0x30, 0x22, 0x0,
    0x7f, 0x20, 0x9, 0x5, 0x80, 0x20, 0x3, 0xf3,
    0x88, 0x38, 0x1, 0xc4, 0x16, 0x40, 0x3c, 0xf0,
    0x0, 0xb0, 0xa, 0x80, 0xd, 0xd5, 0x10, 0xae,
    0x80, 0x5, 0x10, 0x4, 0x98, 0x20, 0x2a, 0xee,
    0x51, 0x1, 0xb5, 0x0, 0xe3, 0xe9, 0x51, 0x0,
    0xa, 0x4f, 0x20, 0x4,

    /* U+0056 "V" */
    0xd, 0xff, 0x18, 0x7, 0xff, 0x0, 0x7f, 0xd8,
    0x16, 0x0, 0x90, 0xf, 0xfe, 0x3, 0x0, 0x24,
    0x18, 0x41, 0x44, 0x3, 0xfe, 0x90, 0x15, 0x0,
    0x30, 0x2, 0x40, 0x3f, 0xca, 0x21, 0x20, 0x14,
    0x80, 0x18, 0x3, 0xfd, 0x20, 0x6, 0x0, 0x85,
    0x40, 0x54, 0x3, 0xf1, 0x98, 0x18, 0x40, 0x34,
    0x80, 0x24, 0x3, 0xf4, 0x80, 0x2c, 0x3, 0x8c,
    0xc0, 0x66, 0x0, 0xf0, 0xa8, 0x21, 0x0, 0x7a,
    0x40, 0x12, 0x1, 0xe9, 0x0, 0x70, 0x7, 0xca,
    0x20, 0xa2, 0x1, 0xcc, 0x2, 0xa0, 0x1f, 0x98,
    0x1, 0x20, 0x19, 0x84, 0x24, 0x3, 0xfa, 0x40,
    0xc, 0x1, 0xac, 0x0, 0xc0, 0x1f, 0xc2, 0xa0,
    0x2a, 0x0, 0x42, 0x6, 0x10, 0xf, 0xf4, 0x80,
    0x24, 0x1, 0xc0, 0xb, 0x0, 0xff, 0x8c, 0xc0,
    0x66, 0x25, 0x4, 0x20, 0xf, 0xfe, 0x4, 0x80,
    0x26, 0x80, 0x1e, 0x1, 0xff, 0xc1, 0x51, 0x5,
    0x70, 0x24, 0x0, 0xff, 0xe1, 0x48, 0x6, 0xa0,
    0xf, 0xfe, 0x1b, 0x0, 0x66, 0x0, 0xff, 0xe1,
    0x8b, 0x0, 0x18, 0x40, 0x3e,

    /* U+0057 "W" */
    0xe, 0xfe, 0x0, 0xff, 0x4f, 0xf9, 0x40, 0x3f,
    0x8f, 0xfc, 0xe0, 0xe0, 0x28, 0x1, 0xfc, 0xe0,
    0xf, 0x0, 0xfe, 0xb0, 0x2, 0x86, 0x80, 0x34,
    0x3, 0xf2, 0x8, 0x1, 0x40, 0x3f, 0x94, 0xc,
    0x81, 0x4, 0x1c, 0x3, 0xf6, 0x80, 0x65, 0x0,
    0xf8, 0xc8, 0x2c, 0x2, 0x40, 0x13, 0x0, 0xf9,
    0xc1, 0x0, 0x1e, 0x1, 0xf5, 0x80, 0x14, 0x2,
    0xe0, 0x5, 0x0, 0x79, 0x4, 0x3d, 0x1, 0x40,
    0x3e, 0x50, 0x22, 0x0, 0x48, 0x0, 0x70, 0xf,
    0x68, 0x1, 0x34, 0x0, 0xa0, 0x1c, 0x44, 0xa,
    0x0, 0xe5, 0x1, 0x30, 0xe, 0x70, 0x50, 0x70,
    0x7, 0x80, 0x75, 0x0, 0x14, 0x3, 0xbc, 0x1,
    0x60, 0x19, 0x4, 0x38, 0x5, 0x1, 0x40, 0x39,
    0x40, 0x88, 0x1, 0xca, 0x0, 0x50, 0xd, 0xc0,
    0x28, 0x0, 0xd0, 0x2, 0x0, 0x44, 0x40, 0x50,
    0xf, 0x94, 0x8, 0xc0, 0x25, 0x4, 0x0, 0x9c,
    0x1, 0xc0, 0x15, 0x0, 0x2c, 0x3, 0xef, 0x0,
    0x58, 0x1, 0x0, 0x1c, 0x1, 0xa, 0x2, 0x80,
    0x4a, 0x4, 0x60, 0x1f, 0x28, 0x1, 0x40, 0x1c,
    0x2, 0x80, 0x1b, 0x80, 0x8, 0x4, 0x40, 0x50,
    0xf, 0xe5, 0x2, 0x20, 0x28, 0x38, 0x7, 0x28,
    0x3, 0x41, 0x40, 0x16, 0x1, 0xfd, 0xe0, 0xa,
    0x50, 0x6, 0x80, 0x79, 0x1, 0xc2, 0x80, 0x8c,
    0x3, 0xf9, 0x40, 0xb, 0xe0, 0x28, 0x1, 0xee,
    0x1, 0x72, 0x5, 0x0, 0xff, 0x90, 0x9, 0x41,
    0xc0, 0x3e, 0x50, 0x4, 0x0, 0x2c, 0x3, 0xfe,
    0xe0, 0xd, 0xa0, 0x1f, 0x90, 0x2, 0x13, 0x0,
    0xff, 0x90, 0x2, 0x14, 0x0, 0xfd, 0xc0, 0x13,
    0x80, 0x7f, 0xf0, 0x5, 0x0, 0xe, 0x1, 0xfc,
    0xa0, 0x15, 0x0, 0x78,

    /* U+0058 "X" */
    0xc, 0xff, 0x28, 0x7, 0xf3, 0x7f, 0xa4, 0x1,
    0xc0, 0xa, 0x20, 0xf, 0x8a, 0x40, 0xa4, 0x0,
    0x52, 0x0, 0xe0, 0xf, 0xbc, 0x1, 0xc0, 0x19,
    0x90, 0xd, 0xc0, 0x3a, 0x8, 0x20, 0xc0, 0x3a,
    0x84, 0x20, 0xc0, 0x24, 0x70, 0x47, 0x0, 0xf0,
    0xd0, 0x3, 0x80, 0x3, 0x40, 0x34, 0x1, 0xf9,
    0x18, 0xa, 0x82, 0xc4, 0x2c, 0x40, 0x3f, 0xa4,
    0x81, 0x69, 0x41, 0x94, 0x3, 0xfe, 0xf0, 0x2,
    0x81, 0x48, 0x7, 0xff, 0x0, 0xa0, 0x2, 0x90,
    0xf, 0xfe, 0x15, 0x80, 0x5c, 0x1, 0xff, 0xc1,
    0x83, 0x0, 0x95, 0x80, 0x3f, 0xe4, 0x70, 0x2d,
    0x0, 0x49, 0x0, 0x7f, 0xd, 0x0, 0x3c, 0xa4,
    0x1, 0xc0, 0x1f, 0xd6, 0x21, 0x4, 0xc, 0xa0,
    0x70, 0x1, 0xf3, 0x28, 0x23, 0x80, 0x56, 0x20,
    0xe8, 0x1, 0xc5, 0x20, 0x34, 0x1, 0x87, 0x40,
    0x14, 0x20, 0x1b, 0xc0, 0x16, 0x20, 0x1c, 0x6e,
    0x3, 0x60, 0x14, 0x90, 0x2a, 0x80, 0x3e, 0x83,
    0x5, 0x60, 0x46, 0x2, 0xa0, 0xf, 0xee, 0x0,
    0x49, 0x0,

    /* U+0059 "Y" */
    0xc, 0xff, 0x18, 0x7, 0xfd, 0xbf, 0xc0, 0xf,
    0x0, 0x78, 0x7, 0xf9, 0xc8, 0x2c, 0x0, 0x6c,
    0x6, 0xc0, 0x1f, 0x86, 0x1, 0x14, 0x2, 0x81,
    0x8, 0x0, 0xfd, 0x60, 0x9, 0x0, 0xc3, 0x0,
    0x32, 0x1, 0xe3, 0x50, 0x72, 0x0, 0xe6, 0x20,
    0x62, 0x0, 0xee, 0x1, 0x80, 0xf, 0xb8, 0x1,
    0x20, 0x19, 0x88, 0x2c, 0x3, 0xf1, 0xa8, 0x22,
    0x80, 0x6, 0x0, 0xd4, 0x3, 0xfa, 0xc0, 0x16,
    0x0, 0x81, 0xe, 0x0, 0xff, 0xc, 0x0, 0xc1,
    0xb0, 0x31, 0x0, 0x7f, 0xce, 0x40, 0xfc, 0x3,
    0x20, 0x1f, 0xfc, 0x1e, 0x0, 0x10, 0x40, 0x7,
    0xff, 0x8, 0xd4, 0x0, 0x6c, 0x1, 0xff, 0xc3,
    0xf0, 0x5, 0x80, 0x7f, 0xff, 0xc0, 0x3f, 0xfd,
    0x20,

    /* U+005A "Z" */
    0x9f, 0xff, 0xfe, 0x19, 0x80, 0x7f, 0xf1, 0x76,
    0x23, 0xfe, 0x20, 0x2, 0x1a, 0x3b, 0xff, 0x8c,
    0x80, 0x68, 0x3, 0xfe, 0x2b, 0x0, 0x68, 0x80,
    0x7f, 0xde, 0x0, 0x93, 0x0, 0xff, 0xa8, 0x81,
    0x98, 0x1, 0xff, 0x3a, 0x81, 0xc8, 0x7, 0xfc,
    0x90, 0x3, 0xc0, 0x1f, 0xf0, 0xd0, 0x2, 0x88,
    0x3, 0xfe, 0xd1, 0x8, 0x40, 0xf, 0xfa, 0x4c,
    0x11, 0xc0, 0x3f, 0xe6, 0x60, 0x15, 0x80, 0x7f,
    0xc7, 0x20, 0xf, 0x0, 0xff, 0x87, 0x40, 0x14,
    0x40, 0x1f, 0xf5, 0x8, 0x32, 0x80, 0x7f, 0xd0,
    0x80, 0x72, 0x1, 0xff, 0x2b, 0x80, 0x11, 0xdf,
    0xfe, 0x49, 0x0, 0x9e, 0x23, 0xfe, 0xd0, 0xf,
    0xfe, 0x28,

    /* U+005B "[" */
    0x1f, 0xfe, 0xc0, 0xf, 0xf2, 0x66, 0x38, 0x2,
    0x13, 0x31, 0x0, 0x7f, 0xff, 0xc0, 0x3f, 0xfe,
    0x22, 0x66, 0x20, 0x9, 0x33, 0x1c, 0x1, 0xf0,

    /* U+005C "\\" */
    0x7, 0x83, 0x0, 0xff, 0x63, 0xc0, 0x7, 0xfb,
    0x81, 0x4, 0x3, 0xf9, 0x40, 0xe, 0x1, 0xfe,
    0x50, 0xa0, 0xf, 0xf7, 0x81, 0x88, 0x7, 0xf2,
    0x80, 0x18, 0x3, 0xfc, 0xa1, 0x40, 0x1f, 0xee,
    0x2, 0x20, 0x7, 0xf2, 0x8, 0x50, 0x7, 0xf9,
    0xc1, 0x40, 0x3f, 0xd4, 0x4, 0x60, 0x1f, 0xc6,
    0x21, 0x60, 0x1f, 0xe6, 0x6, 0x0, 0xff, 0x50,
    0xa, 0x0, 0x7f, 0x11, 0x3, 0x40, 0x3f, 0xd4,
    0xe, 0x1, 0xfe, 0x50, 0x14, 0x0, 0xfe, 0x23,
    0xe, 0x0, 0xff, 0x58, 0x28, 0x7, 0xf9, 0x80,
    0xa, 0x1, 0xfc, 0x28, 0x1c, 0x1, 0xfe, 0xd0,
    0x41, 0x0, 0xfe, 0x70, 0x3, 0x80, 0x7f, 0xa,
    0x6, 0x80, 0x7f, 0xb8, 0x10, 0x40, 0x3f, 0x94,
    0x0, 0xe0,

    /* U+005D "]" */
    0x7f, 0xfe, 0x60, 0xf, 0x9b, 0x31, 0x0, 0x10,
    0x99, 0xc0, 0x1f, 0xff, 0xf0, 0xf, 0xff, 0x88,
    0x99, 0xc0, 0x13, 0x66, 0x20, 0x3, 0xfc,

    /* U+005E "^" */
    0x0, 0xe7, 0x83, 0x0, 0xfe, 0x58, 0x78, 0x0,
    0xfe, 0x90, 0x2, 0x10, 0x7, 0xc6, 0x62, 0x80,
    0xb0, 0xf, 0xa4, 0x2d, 0x81, 0x80, 0x3c, 0x2a,
    0xc, 0x2a, 0xc, 0x1, 0xd2, 0xc, 0x0, 0x90,
    0xb0, 0xe, 0x60, 0xb0, 0x1, 0x91, 0x10, 0x2,
    0x61, 0x42, 0x0, 0xac, 0x3c, 0x2, 0xb0, 0xf0,
    0xc, 0xc0, 0x84, 0x8, 0x44, 0x40, 0xe, 0x60,
    0xb0, 0xf0, 0xa0, 0xf, 0x58, 0x30, 0x0,

    /* U+005F "_" */
    0xbb, 0xff, 0xe1, 0x22, 0x7f, 0xf0, 0x80,

    /* U+0060 "`" */
    0x38, 0x83, 0x80, 0x63, 0xe7, 0x8b, 0x0, 0xcd,
    0xa4, 0x98, 0x20, 0x11, 0x6a, 0x9e, 0x8,

    /* U+0061 "a" */
    0x0, 0xb, 0xdf, 0x7f, 0xb6, 0x90, 0x3, 0x47,
    0x42, 0x8, 0x0, 0x96, 0xe4, 0x2, 0x50, 0x15,
    0x9a, 0x95, 0x0, 0x33, 0x0, 0x1d, 0x1d, 0x4c,
    0xad, 0x5a, 0x0, 0x80, 0x1, 0xb8, 0x7, 0x89,
    0xc0, 0x48, 0x3, 0xfd, 0xc0, 0x6, 0x0, 0x14,
    0x67, 0x73, 0xfe, 0x90, 0xc, 0xda, 0xe6, 0x22,
    0x0, 0xf0, 0x94, 0x80, 0xd7, 0x7f, 0xe9, 0x0,
    0x94, 0x1, 0x4a, 0x20, 0x1f, 0xb8, 0x0, 0xa0,
    0x1e, 0xe0, 0xb, 0x80, 0x8, 0x1, 0xc4, 0xe0,
    0x12, 0x80, 0x2d, 0x40, 0x2, 0xd8, 0x1, 0x8a,
    0x80, 0x6b, 0xfd, 0xd2, 0x2c, 0x1, 0x97, 0x58,
    0x40, 0x4, 0xdb, 0x40, 0x10,

    /* U+0062 "b" */
    0x7f, 0xf1, 0x80, 0x7f, 0xff, 0xc0, 0x3f, 0xf9,
    0x4b, 0x7d, 0xfb, 0x6a, 0x1, 0xf3, 0x65, 0x20,
    0x81, 0x25, 0x69, 0x0, 0x75, 0x98, 0x2c, 0x4b,
    0x88, 0x16, 0x90, 0x6, 0x12, 0xda, 0x76, 0x8e,
    0x60, 0x7, 0x80, 0x77, 0x90, 0x7, 0x4a, 0x81,
    0x28, 0x4, 0xc4, 0x1, 0xf4, 0x0, 0x3c, 0x2,
    0xc0, 0xf, 0xc4, 0x20, 0xc0, 0x13, 0x0, 0x7f,
    0x18, 0x10, 0x4, 0xc0, 0x1f, 0xc6, 0x4, 0x1,
    0x60, 0x7, 0xe3, 0x10, 0x60, 0x9, 0x88, 0x3,
    0xe8, 0x0, 0x78, 0x6, 0xf2, 0x0, 0xe9, 0x40,
    0x25, 0x0, 0x88, 0x9b, 0x4e, 0xd1, 0xcc, 0x0,
    0xf0, 0xd, 0xc6, 0xb, 0x12, 0xe2, 0x5, 0xa4,
    0x1, 0x87, 0x29, 0x4, 0x5, 0x2b, 0x48, 0x0,

    /* U+0063 "c" */
    0x0, 0xc7, 0x3b, 0xfe, 0xd9, 0x30, 0xe, 0x1b,
    0xc6, 0x20, 0x1, 0x36, 0x48, 0x4, 0x38, 0x80,
    0x2d, 0x32, 0x60, 0x3, 0x40, 0x2, 0x88, 0x17,
    0xa5, 0x99, 0x3c, 0x60, 0x11, 0xa0, 0x1d, 0x0,
    0x70, 0xee, 0xa0, 0x2c, 0x1, 0x0, 0x1f, 0x9,
    0x0, 0xc, 0x0, 0x80, 0x1f, 0xf3, 0x80, 0x80,
    0x7f, 0xf0, 0x1c, 0x4, 0x3, 0xff, 0x80, 0x60,
    0x4, 0x0, 0xff, 0xa8, 0x1, 0x0, 0x1f, 0x9,
    0x0, 0x9, 0x0, 0xe8, 0x3, 0x87, 0x75, 0x0,
    0xa, 0x20, 0x5e, 0x96, 0x64, 0xf1, 0x80, 0x61,
    0xc4, 0x1, 0x69, 0x93, 0x0, 0x1e, 0x0, 0x21,
    0xbc, 0x62, 0x0, 0x13, 0x64, 0x0, 0x0,

    /* U+0064 "d" */
    0x0, 0xff, 0xe0, 0x17, 0xfa, 0x0, 0x3f, 0xff,
    0xe0, 0x1f, 0xcb, 0x5b, 0xfd, 0x8c, 0x1, 0xf1,
    0x65, 0x29, 0x0, 0x9c, 0xeb, 0x0, 0x61, 0xc3,
    0x1, 0x69, 0x92, 0x81, 0x50, 0x6, 0xd1, 0x5,
    0xe9, 0x66, 0x57, 0x18, 0x80, 0x46, 0x60, 0x3a,
    0x0, 0xe1, 0xd1, 0x0, 0xa8, 0x1, 0x0, 0x1f,
    0xb, 0x0, 0x44, 0x0, 0x40, 0xf, 0xd6, 0x1,
    0x38, 0x8, 0x7, 0xf0, 0x80, 0x4e, 0x2, 0x1,
    0xfc, 0x20, 0x11, 0x0, 0xc, 0x3, 0xf2, 0x0,
    0x54, 0x0, 0x80, 0xf, 0xd4, 0x1, 0x19, 0x81,
    0x20, 0x3, 0xd6, 0x40, 0x1b, 0x44, 0x1f, 0x18,
    0xd1, 0xf9, 0x4, 0x3, 0xe, 0x18, 0x1c, 0xe5,
    0xc0, 0x8b, 0x40, 0x38, 0xb2, 0x90, 0x80, 0x4e,
    0x38, 0xc0, 0x20,

    /* U+0065 "e" */
    0x0, 0xc9, 0x7b, 0xfd, 0x8c, 0x20, 0x1c, 0x39,
    0x68, 0x40, 0x27, 0x3e, 0x80, 0x10, 0xf9, 0x82,
    0x4d, 0xd3, 0x0, 0xda, 0x0, 0x28, 0x42, 0xec,
    0xc8, 0xb3, 0xa4, 0x14, 0x26, 0x81, 0x28, 0x1,
    0xc5, 0xe0, 0x2f, 0x40, 0x6, 0x0, 0xf8, 0x90,
    0x34, 0x80, 0x1f, 0xff, 0xc8, 0xc, 0xe0, 0x1f,
    0xfc, 0x13, 0x70, 0x7, 0xff, 0xfd, 0xc6, 0x0,
    0x40, 0xf, 0xfa, 0xc0, 0x10, 0x1, 0xf8, 0x40,
    0x6, 0x80, 0x76, 0x20, 0x1c, 0xbe, 0x60, 0xa,
    0x20, 0x4e, 0x96, 0x64, 0x65, 0xf, 0x80, 0x7,
    0x10, 0x0, 0xd3, 0x27, 0x30, 0x2e, 0x0, 0x86,
    0xf1, 0x88, 0x40, 0x56, 0xb4, 0x80,

    /* U+0066 "f" */
    0x0, 0xc3, 0x1b, 0xfd, 0x68, 0x1, 0xe, 0x39,
    0x0, 0xa6, 0x80, 0x50, 0x40, 0xb7, 0x66, 0xa0,
    0x9, 0xc0, 0xa9, 0x11, 0x26, 0x1, 0x8, 0x20,
    0x7, 0xc2, 0x1, 0xfa, 0x7f, 0x80, 0xd, 0xff,
    0x70, 0x7, 0xff, 0x2, 0xf2, 0xc0, 0x9, 0x99,
    0x68, 0x11, 0xa8, 0x0, 0x4c, 0xf0, 0x7, 0xff,
    0xfc, 0x3, 0xff, 0xc6,

    /* U+0067 "g" */
    0x0, 0xcb, 0x7b, 0xfd, 0x8e, 0x21, 0x9f, 0x80,
    0x3, 0xda, 0x42, 0x1, 0x38, 0xf4, 0x0, 0xc7,
    0x84, 0x4, 0xf3, 0x27, 0x11, 0x59, 0x0, 0x5c,
    0x0, 0x7d, 0x86, 0x64, 0x73, 0x80, 0x80, 0x14,
    0x81, 0xa0, 0x3, 0xd0, 0xc0, 0x17, 0x80, 0x2c,
    0x3, 0xf5, 0x80, 0x4c, 0x2, 0x40, 0x1f, 0x88,
    0x40, 0x30, 0x80, 0x7f, 0x84, 0x0, 0xc0, 0x2,
    0x0, 0xfc, 0x40, 0x17, 0x80, 0x20, 0x3, 0xf4,
    0x0, 0x4a, 0x40, 0xb2, 0x1, 0xe9, 0x50, 0xd,
    0xe0, 0x6, 0xe9, 0x66, 0x4f, 0x30, 0x7, 0x16,
    0x10, 0xb, 0x4c, 0x98, 0x45, 0x60, 0x7, 0x0,
    0x1e, 0xd2, 0x10, 0x9, 0xc7, 0xa0, 0x0, 0x40,
    0x32, 0xde, 0xff, 0x63, 0x89, 0x0, 0xc, 0x3,
    0xff, 0x81, 0x40, 0xc, 0x3, 0xe6, 0x0, 0xf9,
    0x5c, 0x5, 0x43, 0xc6, 0x7a, 0x9d, 0x99, 0x19,
    0x40, 0xb, 0x0, 0x71, 0x80, 0xac, 0x4c, 0x9c,
    0xc0, 0x12, 0xa0, 0x2, 0xcc, 0x3a, 0x8, 0x0,
    0x52, 0x39, 0x80, 0x0,

    /* U+0068 "h" */
    0x7f, 0xf1, 0x80, 0x7f, 0xff, 0xc0, 0x3f, 0xf8,
    0x8d, 0x7d, 0xfd, 0x6a, 0x1, 0xe6, 0xd9, 0x41,
    0x1, 0x4a, 0xb0, 0xe, 0xa2, 0x16, 0x88, 0x28,
    0x1, 0x24, 0x3, 0x93, 0xa5, 0xdd, 0x58, 0x0,
    0x62, 0x0, 0x86, 0xc0, 0x38, 0xe0, 0x0, 0x80,
    0x14, 0x80, 0x7c, 0x80, 0xe, 0x0, 0x8c, 0x3,
    0xe2, 0x0, 0x8, 0x4, 0xe0, 0x1f, 0xe3, 0x0,
    0xff, 0xff, 0x80, 0x7f, 0xf4, 0x80,

    /* U+0069 "i" */
    0x5e, 0xd3, 0x81, 0x29, 0x60, 0x6, 0xdc, 0xd3,
    0x83, 0x28, 0x7, 0x3f, 0xf8, 0xc0, 0x3f, 0xfd,
    0x80,

    /* U+006A "j" */
    0x0, 0xe3, 0xee, 0x20, 0x7, 0x40, 0x8a, 0x0,
    0x38, 0x80, 0xa, 0x1, 0xd3, 0x38, 0x3, 0xcc,
    0xc0, 0xf, 0xfe, 0x1a, 0xff, 0x98, 0x3, 0xff,
    0xfe, 0x1, 0xff, 0xef, 0x30, 0xe, 0xc0, 0x3,
    0x5, 0xb2, 0xc3, 0x0, 0x9a, 0xa4, 0xd3, 0x80,
    0x28, 0x1d, 0x48, 0x5, 0x31, 0x0,

    /* U+006B "k" */
    0x7f, 0xf1, 0x80, 0x7f, 0xff, 0xc0, 0x3f, 0xf9,
    0xe9, 0xfe, 0xe2, 0x0, 0xff, 0x2d, 0x80, 0xe1,
    0x0, 0x7f, 0x35, 0x0, 0xe1, 0x0, 0x7f, 0x3c,
    0x80, 0xe1, 0x0, 0x7f, 0x44, 0x0, 0x70, 0x80,
    0x3f, 0xa5, 0xc0, 0x70, 0x80, 0x3f, 0x3d, 0x30,
    0x1, 0xc8, 0x3, 0xfa, 0x94, 0x2, 0x65, 0x0,
    0xfe, 0x10, 0x3a, 0x0, 0x51, 0x0, 0x7f, 0x26,
    0x2b, 0x80, 0x3c, 0x3, 0xf2, 0xd8, 0x2, 0x10,
    0xa, 0x80, 0x3e, 0x90, 0xd, 0x64, 0xa, 0xe0,
    0x1f, 0xfc, 0xf, 0x0, 0x42, 0x0, 0x7f, 0xc5,
    0x40, 0xa, 0x10, 0xf, 0xf9, 0x5c, 0x7, 0x40,

    /* U+006C "l" */
    0x7f, 0xf1, 0x80, 0x7f, 0xff, 0xc0, 0x38,

    /* U+006D "m" */
    0x7f, 0xf1, 0xc, 0x67, 0x7e, 0xc9, 0x80, 0x45,
    0x1b, 0xfe, 0xd9, 0x20, 0xf, 0x3f, 0xb9, 0x88,
    0x13, 0x63, 0x83, 0xeb, 0x90, 0x0, 0x9b, 0x58,
    0x3, 0xa8, 0x4e, 0x6e, 0x94, 0x1, 0x15, 0x0,
    0x73, 0x74, 0xa0, 0x9, 0x30, 0xe, 0x7c, 0x64,
    0x5a, 0x90, 0x2, 0x83, 0xe3, 0x22, 0xd4, 0x0,
    0x20, 0x3, 0x14, 0x0, 0x73, 0x10, 0x0, 0xe0,
    0x3, 0x9c, 0x81, 0x0, 0x35, 0x0, 0x7c, 0x80,
    0xb, 0x0, 0xf9, 0x0, 0x2, 0x1, 0x18, 0x7,
    0xdc, 0x0, 0x30, 0xf, 0x84, 0x0, 0x60, 0x13,
    0x80, 0x7f, 0x9c, 0x3, 0xfc, 0x20, 0x1f, 0xff,
    0xf0, 0xf, 0xff, 0xf8, 0x7, 0xff, 0x50,

    /* U+006E "n" */
    0x7f, 0xf1, 0x3, 0xe7, 0x7f, 0x5a, 0x80, 0x79,
    0xfa, 0xc, 0x40, 0x52, 0xac, 0x3, 0xa8, 0x4a,
    0x2a, 0x8e, 0x20, 0x92, 0x1, 0xcd, 0xae, 0xaa,
    0x8c, 0x10, 0x62, 0x0, 0x8a, 0x40, 0x38, 0xa4,
    0x0, 0x80, 0x15, 0x0, 0x7c, 0xa0, 0xe, 0x0,
    0x8c, 0x3, 0xe3, 0x0, 0x8, 0x4, 0xe0, 0x1f,
    0xe3, 0x0, 0xff, 0xff, 0x80, 0x7f, 0xf4, 0x80,

    /* U+006F "o" */
    0x0, 0xc9, 0x5b, 0xfd, 0xb0, 0x40, 0x1e, 0x1b,
    0xb2, 0x90, 0x9, 0x3e, 0xc0, 0x6, 0x1f, 0x40,
    0x16, 0x99, 0x28, 0x1, 0xec, 0x2, 0xa1, 0x5,
    0xe9, 0x66, 0x57, 0x18, 0x23, 0x81, 0xa0, 0x1d,
    0x0, 0x70, 0xe8, 0x84, 0x85, 0x80, 0x20, 0x3,
    0xe1, 0x90, 0x15, 0x30, 0x2, 0x0, 0x7e, 0x40,
    0x1, 0x38, 0x8, 0x7, 0xf0, 0x80, 0x39, 0xc0,
    0x40, 0x3f, 0x84, 0x1, 0xc6, 0x0, 0x40, 0xf,
    0xd6, 0x0, 0x2b, 0x0, 0x40, 0x7, 0xc2, 0xc0,
    0x2a, 0x68, 0x7, 0x40, 0x1c, 0x3a, 0x21, 0x20,
    0xa, 0x20, 0x5e, 0x96, 0x65, 0x71, 0x82, 0x38,
    0x0, 0x71, 0x0, 0x5a, 0x64, 0xa0, 0x7, 0xb0,
    0xc, 0x37, 0x65, 0x20, 0x12, 0x6d, 0x80, 0x8,

    /* U+0070 "p" */
    0x7f, 0xf1, 0x3, 0x5f, 0x7e, 0xda, 0x80, 0x7c,
    0xdb, 0x28, 0x20, 0x49, 0x5a, 0x40, 0x1d, 0x44,
    0x2f, 0x76, 0x93, 0x2, 0xd2, 0x0, 0xc2, 0x7f,
    0x8, 0x86, 0xc8, 0x0, 0x78, 0x6, 0x1d, 0x10,
    0xe, 0x76, 0x2, 0x50, 0x9, 0x84, 0x3, 0xeb,
    0x0, 0x78, 0x5, 0x80, 0x1f, 0x88, 0x41, 0x80,
    0x27, 0x0, 0xfe, 0x30, 0x20, 0x9, 0x80, 0x3f,
    0x8c, 0x8, 0x2, 0xc0, 0xf, 0xc6, 0x20, 0xc0,
    0x13, 0x10, 0x7, 0xd0, 0x0, 0xf0, 0xd, 0xe4,
    0x1, 0xd2, 0x80, 0x4a, 0x1, 0x9, 0x6d, 0x3b,
    0x47, 0x30, 0x3, 0xc0, 0x35, 0x18, 0x2c, 0x4b,
    0x88, 0x16, 0x90, 0x6, 0x7c, 0xa4, 0x10, 0x14,
    0xad, 0x20, 0xf, 0x96, 0xfb, 0xfa, 0xd4, 0x3,
    0xff, 0xf0,

    /* U+0071 "q" */
    0x0, 0xcb, 0x5b, 0xfd, 0x8c, 0x0, 0xff, 0x40,
    0x0, 0xb2, 0x94, 0x80, 0x4e, 0x74, 0x80, 0x30,
    0xe1, 0x80, 0xb4, 0xc9, 0x40, 0xb0, 0x3, 0x68,
    0x82, 0xf4, 0xb3, 0x2b, 0x4c, 0x40, 0x23, 0x30,
    0x1d, 0x0, 0x71, 0x68, 0x80, 0x54, 0x0, 0x80,
    0xf, 0x86, 0x40, 0x22, 0x0, 0x20, 0x7, 0xe4,
    0x0, 0x9c, 0x4, 0x3, 0xf8, 0x40, 0x27, 0x1,
    0x0, 0xfe, 0x10, 0x8, 0x80, 0x8, 0x1, 0xfa,
    0xc0, 0x2a, 0x0, 0x40, 0x7, 0xc2, 0xc0, 0x11,
    0x98, 0xe, 0x80, 0x38, 0x74, 0x40, 0x36, 0x88,
    0x2f, 0x4b, 0x32, 0xb8, 0xc4, 0x3, 0xe, 0x18,
    0xb, 0x4c, 0x94, 0xf, 0x40, 0x38, 0xb2, 0x90,
    0x80, 0x4e, 0x70, 0x40, 0x3e, 0x5b, 0xdf, 0xec,
    0x60, 0xf, 0xff, 0xc8,

    /* U+0072 "r" */
    0x7f, 0xf1, 0x3, 0x67, 0x40, 0x4, 0x3b, 0x26,
    0x20, 0x1b, 0x88, 0xd, 0xb4, 0x2, 0x22, 0x6e,
    0x4a, 0x80, 0x6c, 0x20, 0xf, 0x38, 0x80, 0x7d,
    0xa0, 0x1f, 0x98, 0x3, 0xff, 0xf0,

    /* U+0073 "s" */
    0x0, 0x8a, 0x77, 0xfd, 0xd9, 0x6, 0x1, 0xa3,
    0x58, 0x80, 0x2, 0x6f, 0x90, 0x0, 0x57, 0x1,
    0x79, 0xa9, 0x61, 0xa, 0x0, 0x70, 0x3, 0x21,
    0x95, 0xa7, 0xb0, 0x80, 0xc, 0x4, 0x40, 0xf,
    0x18, 0x4, 0xc0, 0x4e, 0x1, 0xfe, 0x80, 0x4,
    0x75, 0xc2, 0x88, 0x7, 0x1d, 0x90, 0xa, 0x3d,
    0x76, 0xa8, 0x6, 0x4d, 0xc9, 0x63, 0x0, 0x15,
    0x38, 0x7, 0x1b, 0x4e, 0x7d, 0x8, 0x40, 0x7,
    0xf9, 0x58, 0x0, 0x40, 0xae, 0x1, 0xf1, 0x80,
    0xc, 0x2e, 0x3e, 0x9d, 0x54, 0xf8, 0x80, 0x82,
    0x22, 0x0, 0x2c, 0x55, 0x20, 0xc0, 0xe8, 0x7,
    0x30, 0xe6, 0x20, 0x2, 0x5a, 0xc1, 0x0,

    /* U+0074 "t" */
    0x0, 0xa2, 0xa, 0x1, 0xf0, 0xbb, 0xbc, 0x3,
    0xff, 0x9f, 0x3f, 0xc0, 0x5, 0xff, 0xb8, 0x3,
    0xff, 0x81, 0x79, 0x60, 0x4, 0xcc, 0xb4, 0x8,
    0xd4, 0x0, 0x26, 0x78, 0x3, 0xff, 0xf2, 0x20,
    0x1f, 0xfc, 0x14, 0x0, 0xfc, 0xa0, 0x36, 0xaa,
    0xa4, 0x0, 0xa0, 0x41, 0x2a, 0x8b, 0xe0, 0x11,
    0x7b, 0x8, 0xa, 0xc0,

    /* U+0075 "u" */
    0x9f, 0xf0, 0x80, 0x7c, 0xbf, 0xe6, 0x0, 0xff,
    0xff, 0x80, 0x7f, 0xf8, 0x44, 0x0, 0x40, 0x1f,
    0x68, 0x5, 0xa0, 0x4, 0x0, 0xf9, 0x80, 0x24,
    0x0, 0x49, 0x0, 0x75, 0x8, 0x4, 0x2e, 0x3,
    0x90, 0x88, 0x6d, 0x50, 0xe, 0x85, 0x1, 0x7b,
    0xb4, 0x90, 0xd0, 0x7, 0x55, 0xa8, 0x80, 0x9c,
    0x73, 0x80, 0x40,

    /* U+0076 "v" */
    0xd, 0xfe, 0x0, 0xfe, 0x2f, 0xf3, 0x85, 0x80,
    0xb0, 0x7, 0xeb, 0x0, 0x30, 0x30, 0x85, 0x0,
    0x7c, 0x2c, 0xc, 0x20, 0x6, 0x3, 0x30, 0x7,
    0x98, 0x1, 0x60, 0x15, 0x0, 0x24, 0x3, 0xd2,
    0x8, 0x40, 0x11, 0x28, 0x28, 0x80, 0x65, 0x10,
    0xf0, 0xe, 0x90, 0x4, 0x80, 0x69, 0x2, 0x40,
    0xe, 0x32, 0x6, 0x0, 0x8c, 0xc1, 0x60, 0x1f,
    0x58, 0xa, 0x80, 0x24, 0x0, 0xc0, 0x1f, 0x30,
    0x84, 0x80, 0xa8, 0x30, 0x7, 0xf3, 0x1, 0x99,
    0x80, 0x16, 0x1, 0xfd, 0x20, 0x9, 0x90, 0x21,
    0x0, 0x7f, 0xa, 0x82, 0x88, 0x78, 0x7, 0xfd,
    0x20, 0x11, 0x20, 0x7, 0xfc, 0x66, 0x0, 0x58,
    0x7, 0x80,

    /* U+0077 "w" */
    0xaf, 0xe0, 0xf, 0xd5, 0xfc, 0x1, 0xfa, 0x7f,
    0x78, 0x5, 0x0, 0x3c, 0x2a, 0x2, 0xa0, 0x1f,
    0x38, 0x52, 0x8, 0x68, 0x7, 0x9c, 0x2, 0xe0,
    0xf, 0x28, 0x83, 0x3, 0x3, 0x0, 0x7a, 0x80,
    0x24, 0x10, 0xe, 0xe0, 0x41, 0xa, 0x0, 0x28,
    0x6, 0x23, 0xa, 0x0, 0x38, 0x6, 0x14, 0xe,
    0x0, 0x11, 0x87, 0x0, 0x6a, 0x2, 0x54, 0xa,
    0x0, 0xce, 0x2, 0xa0, 0x15, 0x82, 0x8, 0x4,
    0xc1, 0x41, 0xc0, 0x64, 0x1, 0x50, 0x38, 0x6,
    0x60, 0x3, 0x80, 0x10, 0x41, 0x81, 0x44, 0x28,
    0x0, 0x46, 0x14, 0x1, 0x85, 0x2, 0x80, 0x1c,
    0x8, 0x20, 0x7, 0x6, 0x0, 0x50, 0x11, 0x80,
    0x77, 0x1, 0x90, 0xa8, 0x70, 0x5, 0x40, 0x28,
    0xc, 0x14, 0x1, 0xe5, 0x10, 0xa7, 0x0, 0x28,
    0x4, 0x66, 0xd, 0x41, 0x6, 0x0, 0xf9, 0xc1,
    0x68, 0x18, 0x3, 0xa8, 0x1b, 0x81, 0x4, 0x3,
    0xea, 0x2, 0x30, 0xa0, 0xe, 0x70, 0x2, 0x86,
    0x80, 0x7e, 0x32, 0x0, 0x11, 0x80, 0x70, 0xa8,
    0x4, 0xc0, 0x1f, 0xd4, 0x0, 0xa0, 0xf, 0xb8,
    0x0, 0xa0, 0x1c,

    /* U+0078 "x" */
    0x1e, 0xfe, 0x10, 0xf, 0x4f, 0xf9, 0x40, 0x74,
    0x7, 0x40, 0x39, 0x58, 0x19, 0x40, 0x7, 0x20,
    0x70, 0x1, 0x15, 0x81, 0xc8, 0x6, 0x65, 0x7,
    0x50, 0x7, 0x8, 0x70, 0x7, 0xa8, 0x82, 0xca,
    0x4c, 0x28, 0x80, 0x3e, 0xf0, 0x1c, 0x60, 0x75,
    0x0, 0xfc, 0x52, 0x2, 0x7, 0x0, 0x1f, 0xe6,
    0x10, 0x4, 0x80, 0x7f, 0xd4, 0x20, 0xb, 0x20,
    0xf, 0xe6, 0x50, 0x50, 0x1e, 0x0, 0xfc, 0x72,
    0x7, 0x54, 0x3, 0x90, 0xf, 0xf, 0x0, 0xe8,
    0x2b, 0x3, 0x28, 0x7, 0x51, 0x5, 0x8, 0x2,
    0x48, 0x28, 0x80, 0x27, 0x40, 0x74, 0x0, 0xde,
    0x0, 0xf0, 0x1, 0xc0, 0x1c, 0x0, 0x71, 0x50,
    0x15, 0x0,

    /* U+0079 "y" */
    0xd, 0xfe, 0x0, 0xfe, 0x2f, 0xf3, 0x85, 0x80,
    0xb0, 0x7, 0xeb, 0x0, 0x38, 0x30, 0x85, 0x80,
    0x7c, 0x2c, 0xe, 0x1, 0x48, 0x12, 0x80, 0x7a,
    0x40, 0x12, 0x1, 0x30, 0x2, 0x40, 0x3c, 0xc0,
    0xa2, 0x1, 0xb, 0x1, 0x98, 0x3, 0x30, 0x84,
    0x80, 0x75, 0x80, 0x24, 0x3, 0x58, 0x19, 0x80,
    0x38, 0x94, 0x14, 0x40, 0x8, 0x41, 0x0, 0x1f,
    0x48, 0x2, 0x40, 0x1e, 0x4, 0x80, 0x1f, 0x19,
    0x81, 0xc0, 0x90, 0x2c, 0x3, 0xfa, 0x0, 0xf,
    0x60, 0x2c, 0x1, 0xfc, 0x84, 0x12, 0xc1, 0x20,
    0x1f, 0xf5, 0x80, 0x80, 0x18, 0x3, 0xfe, 0x61,
    0x0, 0x30, 0x80, 0x7f, 0xf0, 0x1c, 0x1, 0x60,
    0x1f, 0xfc, 0x1, 0x60, 0x42, 0x0, 0xf8, 0x40,
    0x34, 0x0, 0x20, 0x3, 0xf7, 0xdb, 0x2d, 0x30,
    0x29, 0x80, 0x7c, 0xc2, 0x93, 0x4a, 0x5, 0x60,
    0x1f, 0x9a, 0x4c, 0x0, 0x51, 0xa2, 0x1, 0xf8,

    /* U+007A "z" */
    0xbf, 0xff, 0xf9, 0x40, 0x3f, 0xf8, 0x3, 0x39,
    0x9f, 0x38, 0x1, 0x10, 0x46, 0x7e, 0x17, 0x2,
    0xa0, 0xf, 0x87, 0xc0, 0x1c, 0x20, 0x1f, 0x51,
    0x5, 0x18, 0x7, 0xd0, 0x80, 0xea, 0x1, 0xf2,
    0x38, 0x24, 0x0, 0x7c, 0x56, 0x3, 0x40, 0x1f,
    0xbc, 0x1, 0xa2, 0x1, 0xf5, 0x10, 0x49, 0x80,
    0x7c, 0xea, 0xc, 0xc0, 0xf, 0x92, 0x0, 0x68,
    0xcf, 0xe1, 0x90, 0x0, 0xe6, 0x7e, 0x93, 0x0,
    0xff, 0xe0, 0x0,

    /* U+007B "{" */
    0x0, 0xcd, 0x9f, 0xe4, 0x0, 0x9e, 0x4c, 0x3,
    0xd2, 0x0, 0x8c, 0x50, 0x0, 0x88, 0x11, 0xcc,
    0x40, 0x6, 0x0, 0x30, 0xf, 0xff, 0x68, 0x9c,
    0x0, 0x34, 0x2, 0x7c, 0x50, 0x65, 0x0, 0xf9,
    0x4, 0x2, 0x6f, 0x80, 0x27, 0x0, 0xe5, 0x0,
    0x70, 0x7, 0xff, 0xbc, 0xc0, 0x6, 0x1, 0xc2,
    0x20, 0x48, 0x31, 0x0, 0xa0, 0x0, 0xf8, 0xa0,
    0x13, 0x51, 0x80, 0x40,

    /* U+007C "|" */
    0x1f, 0xf3, 0x80, 0x7f, 0xff, 0xc0, 0x3f, 0xf9,
    0xa0,

    /* U+007D "}" */
    0x7f, 0xf5, 0xa0, 0x7, 0xc9, 0x68, 0x1, 0x35,
    0xb0, 0x2, 0x0, 0x21, 0x49, 0x20, 0x30, 0xf,
    0x38, 0x7, 0xff, 0xc8, 0x80, 0x10, 0x60, 0x1a,
    0x50, 0x1f, 0xc, 0x2, 0x35, 0x0, 0xfa, 0xc4,
    0x2b, 0xcc, 0x2, 0x30, 0x2, 0x80, 0x7f, 0xfb,
    0xdc, 0x3, 0x85, 0x24, 0x80, 0xc0, 0x26, 0xb6,
    0x0, 0x40, 0x7, 0x92, 0xd0, 0x2,

    /* U+007E "~" */
    0x0, 0x3f, 0x7e, 0x18, 0x6, 0x2f, 0xa0, 0x78,
    0x10, 0x3c, 0x60, 0x9, 0xc0, 0x82, 0x42, 0x76,
    0x2, 0x60, 0x6, 0x3, 0xc, 0x51, 0x89, 0xec,
    0x5f, 0xf8, 0x91, 0xc, 0x18, 0x1, 0x27, 0xa8,
    0x1, 0x68, 0x0,

    /* U+00B0 "°" */
    0x0, 0xc2, 0x1, 0xf3, 0xf7, 0x3f, 0x10, 0x2,
    0xa8, 0x49, 0x81, 0xb5, 0x4, 0x57, 0xa6, 0x7d,
    0x3a, 0xd, 0x8, 0x0, 0xd0, 0xa, 0x44, 0x0,
    0xe6, 0x3, 0x22, 0x8, 0x6, 0x70, 0x2a, 0xb,
    0x0, 0x86, 0xc5, 0xd, 0xd7, 0x66, 0xb8, 0x68,
    0x1, 0x14, 0x2c, 0x8b, 0xa8, 0x0,

    /* U+2022 "•" */
    0x0, 0x84, 0x2, 0x4e, 0xe6, 0x8, 0x50, 0x81,
    0xd0, 0x88, 0x2, 0x61, 0x10, 0x4, 0xe1, 0xa6,
    0x9, 0x60,

    /* U+F001 "" */
    0x0, 0xff, 0xe6, 0x89, 0x80, 0x7f, 0xf2, 0xce,
    0x37, 0xb2, 0x40, 0x3f, 0xf8, 0xc2, 0xb5, 0xd8,
    0xe4, 0x0, 0x60, 0xf, 0xfe, 0x19, 0x3e, 0x75,
    0x28, 0x80, 0x7f, 0xf1, 0x92, 0x7b, 0x60, 0xc0,
    0x3f, 0xf8, 0xc2, 0xd7, 0xf6, 0xc2, 0x1, 0xff,
    0xc8, 0x8e, 0x94, 0x0, 0xff, 0xe6, 0x38, 0x7,
    0xff, 0x78, 0x59, 0x0, 0x3f, 0xf9, 0x47, 0x19,
    0xd2, 0x60, 0x1f, 0xfc, 0x61, 0x5a, 0xec, 0x73,
    0x0, 0xff, 0xe3, 0x13, 0xe7, 0x52, 0x88, 0x7,
    0xff, 0x1d, 0x77, 0x50, 0x60, 0x1f, 0xfc, 0xb2,
    0x20, 0x7, 0xff, 0xfc, 0x3, 0xff, 0xf8, 0x2f,
    0x59, 0x68, 0x1, 0xff, 0xc9, 0x5e, 0x85, 0x34,
    0x30, 0xf, 0xfe, 0x41, 0x50, 0x7, 0xff, 0x0,
    0x4c, 0xc4, 0x1, 0xfe, 0x50, 0xf, 0xf8, 0xf3,
    0xb3, 0x1a, 0x1, 0xfe, 0x20, 0xf, 0xe2, 0x5c,
    0x30, 0xf, 0xfe, 0x12, 0xa0, 0x7, 0xe9, 0x80,
    0xf, 0xfe, 0x2d, 0xc0, 0x80, 0x64, 0xd4, 0x20,
    0xf, 0xef, 0x0, 0xfc, 0xfd, 0xcc, 0xdf, 0xb2,
    0x5, 0x0, 0xfe, 0x50, 0xf, 0xf0, 0x99, 0x0,
    0x6a, 0x50, 0xf, 0xd, 0x90, 0x7, 0xff, 0x1a,
    0xb6, 0x15, 0x4d, 0x5c, 0x80, 0x1f, 0xfc, 0x60,

    /* U+F008 "" */
    0x9b, 0x0, 0xb3, 0xff, 0xff, 0x89, 0x60, 0x15,
    0xcb, 0x2a, 0x24, 0x40, 0x1f, 0xfc, 0x45, 0x44,
    0x95, 0x80, 0x6e, 0xe1, 0x0, 0x2a, 0xff, 0xf0,
    0x0, 0x3, 0x77, 0x8, 0x4, 0xef, 0x0, 0xa,
    0xab, 0xff, 0x80, 0x20, 0x7, 0x78, 0x2, 0x38,
    0x88, 0xc0, 0x3f, 0xf8, 0x89, 0x11, 0x18, 0x7,
    0xff, 0x48, 0x40, 0x21, 0x0, 0xff, 0xe2, 0x30,
    0x4, 0x20, 0x2, 0xff, 0x88, 0x3, 0xff, 0x88,
    0x5f, 0xf1, 0x0, 0x46, 0x70, 0x0, 0xc0, 0x3f,
    0xf8, 0x66, 0x70, 0x4, 0x79, 0x91, 0x80, 0xee,
    0xff, 0xf0, 0x4, 0xf, 0x32, 0x30, 0xf, 0xe2,
    0x2f, 0xfe, 0x0, 0x1, 0xc0, 0x3f, 0xf8, 0x24,
    0x5f, 0xfc, 0x0, 0x3, 0x80, 0x78, 0xf3, 0x23,
    0x1, 0xdd, 0xff, 0xe0, 0x8, 0x1e, 0x64, 0x60,
    0x11, 0x9c, 0x0, 0x30, 0xf, 0xfe, 0x19, 0x9c,
    0x1, 0x17, 0xfc, 0x40, 0x1f, 0xfc, 0x42, 0xff,
    0x88, 0x0, 0x20, 0x10, 0x80, 0x7f, 0xf1, 0x18,
    0x2, 0x10, 0xf, 0xfe, 0x91, 0xc4, 0x46, 0x1,
    0xff, 0xc4, 0x48, 0x88, 0xc0, 0x27, 0x78, 0x0,
    0x55, 0x5f, 0xfc, 0x1, 0x0, 0x3b, 0xc0, 0x10,
    0xdd, 0xc2, 0x0, 0x55, 0xff, 0xe0, 0x0, 0x6,
    0xee, 0x10, 0x55, 0x22, 0x44, 0x1, 0xff, 0xc4,
    0x54, 0x49, 0x58,

    /* U+F00B "" */
    0x9f, 0xff, 0x58, 0x2, 0xbf, 0xff, 0xf8, 0x92,
    0xc0, 0x1e, 0x42, 0x15, 0x0, 0xff, 0xe2, 0x30,
    0x7, 0xff, 0xfc, 0x3, 0xff, 0xb2, 0xa0, 0x1e,
    0x32, 0x13, 0x0, 0xff, 0xe2, 0x2d, 0x7f, 0xfb,
    0x0, 0x19, 0xff, 0xff, 0xc4, 0xa0, 0xf, 0xfe,
    0x84, 0xff, 0xfa, 0xc0, 0x17, 0xff, 0xff, 0xc4,
    0x96, 0x0, 0xf2, 0x10, 0xa0, 0x7, 0xff, 0x11,
    0x80, 0x3f, 0xff, 0xe0, 0x1f, 0xfd, 0x95, 0x0,
    0xf1, 0x90, 0xa0, 0x7, 0xff, 0x11, 0xab, 0xff,
    0xd8, 0x0, 0xbf, 0xff, 0xfe, 0x24, 0x80, 0x7f,
    0xf4, 0x2b, 0xff, 0xd8, 0x0, 0xcf, 0xff, 0xfe,
    0x25, 0x28, 0x7, 0x8c, 0x84, 0xc0, 0x3f, 0xf8,
    0x8a, 0x1, 0xff, 0xff, 0x0, 0xff, 0xec, 0xb0,
    0x7, 0x90, 0x85, 0x40, 0x3f, 0xf8, 0x8c,

    /* U+F00C "" */
    0x0, 0xff, 0xe5, 0xaf, 0x48, 0x7, 0xff, 0x31,
    0xa8, 0x5a, 0x80, 0x3f, 0xf9, 0x4d, 0x20, 0x12,
    0xc8, 0x7, 0xff, 0x21, 0xa4, 0x3, 0x98, 0x3,
    0xff, 0x8e, 0xd2, 0x1, 0xe7, 0x0, 0xff, 0xe3,
    0x34, 0x80, 0x79, 0xe0, 0x3, 0xff, 0x8a, 0xd2,
    0x1, 0xe7, 0x80, 0xa, 0x39, 0x40, 0x3f, 0xe6,
    0x90, 0xf, 0x3c, 0x0, 0x54, 0xe3, 0x4c, 0x1,
    0xfc, 0xd2, 0x1, 0xe7, 0x80, 0xa, 0x54, 0x2,
    0x96, 0x0, 0xf9, 0xa4, 0x3, 0xcf, 0x0, 0x19,
    0x80, 0x3a, 0x58, 0x3, 0x9a, 0x40, 0x3c, 0xf0,
    0x1, 0xce, 0x1, 0xe9, 0x60, 0x9, 0xa4, 0x3,
    0xcf, 0x0, 0x1e, 0x87, 0x0, 0xf4, 0xb0, 0x34,
    0x80, 0x79, 0xe0, 0x3, 0xf4, 0x38, 0x7, 0xa6,
    0xa4, 0x3, 0xcf, 0x0, 0x1f, 0xe8, 0x70, 0xf,
    0x28, 0x7, 0x9e, 0x0, 0x3f, 0xf8, 0x10, 0xe0,
    0x1f, 0xf3, 0xc0, 0x7, 0xff, 0xa, 0x1c, 0x3,
    0xf9, 0xe0, 0x3, 0xff, 0x89, 0xe, 0x1, 0xf4,
    0x40, 0x3, 0xff, 0x8d, 0x10, 0x0, 0xe8, 0x70,
    0xf, 0xfe, 0x43, 0xc0, 0x5, 0xe, 0x1, 0xff,
    0xca, 0x78, 0x18, 0x70, 0xf, 0xfe, 0x18,

    /* U+F00D "" */
    0x0, 0xff, 0xe2, 0x8, 0x6, 0x6f, 0xf2, 0x80,
    0x7f, 0x16, 0xf5, 0x0, 0x1a, 0x40, 0x14, 0xa0,
    0x1f, 0x16, 0x10, 0x2d, 0x4, 0x0, 0x6a, 0x50,
    0xe, 0x2c, 0x10, 0x9, 0x48, 0x80, 0x1d, 0x4a,
    0x1, 0x16, 0x8, 0x7, 0x1f, 0x8, 0x7, 0x52,
    0x81, 0x60, 0x80, 0x75, 0x9, 0x60, 0x80, 0x75,
    0x3e, 0x8, 0x7, 0x52, 0x80, 0xb, 0x4, 0x3,
    0xa0, 0x40, 0x3a, 0x94, 0x3, 0x16, 0x8, 0x7,
    0xfa, 0x94, 0x3, 0xc5, 0x82, 0x1, 0xfa, 0x94,
    0x3, 0xf1, 0x50, 0x7, 0xce, 0xa0, 0x1f, 0xc5,
    0x40, 0x1f, 0x3a, 0x80, 0x7e, 0x2c, 0x10, 0xf,
    0xd4, 0xa0, 0x1e, 0x2c, 0x10, 0xf, 0xf5, 0x28,
    0x6, 0x2c, 0x10, 0xe, 0x91, 0x0, 0xea, 0x50,
    0x1, 0x60, 0x80, 0x75, 0xb6, 0x8, 0x7, 0x52,
    0x87, 0x8, 0x7, 0x5a, 0x1, 0x60, 0x80, 0x75,
    0x9, 0x0, 0x75, 0xa0, 0x4, 0x58, 0x20, 0x1c,
    0x70, 0x1, 0xad, 0x0, 0x38, 0xb0, 0x40, 0x25,
    0x26, 0x90, 0x5, 0xa0, 0x7, 0xc5, 0x84, 0xb,
    0x40, 0x6, 0xff, 0x20, 0x7, 0xf1, 0x6f, 0x50,
    0x0,

    /* U+F011 "" */
    0x0, 0xff, 0xe0, 0x34, 0xc9, 0x80, 0x3f, 0xf9,
    0x65, 0x2c, 0xc9, 0x20, 0xf, 0xfe, 0x50, 0x80,
    0x61, 0x0, 0xff, 0xe2, 0xd, 0x40, 0x7, 0xff,
    0x2, 0x28, 0x40, 0x3f, 0x8f, 0xd5, 0xd8, 0x3,
    0xfc, 0xce, 0xbe, 0x60, 0x1f, 0x16, 0x88, 0x2,
    0x0, 0x3f, 0xd0, 0x0, 0x1c, 0x30, 0xf, 0x78,
    0x80, 0x7f, 0xf1, 0xf8, 0x40, 0x34, 0x90, 0x4,
    0x3a, 0x1, 0xfe, 0xd1, 0x0, 0x8a, 0x0, 0x22,
    0x60, 0x8, 0x70, 0xc0, 0x3f, 0xc7, 0x82, 0x1,
    0x31, 0x0, 0x2c, 0x3, 0x59, 0x0, 0x7f, 0xf0,
    0xa, 0xc0, 0x35, 0x80, 0x1c, 0x2, 0x35, 0x0,
    0xff, 0xe1, 0x29, 0x80, 0x4e, 0x6, 0x20, 0x14,
    0x80, 0x7f, 0xf1, 0x28, 0x2, 0x13, 0x50, 0xc,
    0xa0, 0x1f, 0xfc, 0x46, 0x0, 0xca, 0x20, 0x10,
    0x80, 0x7f, 0xf1, 0x84, 0x2, 0xef, 0x0, 0x8c,
    0x3, 0xc2, 0x1, 0x84, 0x3, 0xc6, 0x1, 0xf8,
    0xc0, 0x3c, 0x42, 0x0, 0x12, 0x0, 0xf1, 0x80,
    0x5f, 0xc0, 0x10, 0x80, 0x7d, 0xdb, 0xae, 0x0,
    0xf8, 0x40, 0x21, 0x50, 0xc, 0xa0, 0x1f, 0x11,
    0x0, 0x3e, 0x50, 0xc, 0xa6, 0x20, 0x17, 0x0,
    0x7f, 0xf1, 0x38, 0x2, 0x13, 0x7, 0x0, 0x91,
    0x0, 0x1f, 0xfc, 0x24, 0x40, 0x4, 0xe0, 0xb,
    0x0, 0xd4, 0x40, 0x1f, 0xfc, 0x2, 0xa0, 0xd,
    0x60, 0x2, 0x60, 0x8, 0x70, 0xc0, 0x3f, 0xc7,
    0x82, 0x1, 0x31, 0x0, 0x50, 0x20, 0x10, 0xe4,
    0x0, 0x7e, 0x8c, 0x10, 0x8, 0xa0, 0x3, 0xe,
    0x80, 0x73, 0xfd, 0x3a, 0xa9, 0xeb, 0xdc, 0x3,
    0xf, 0x88, 0x7, 0x1e, 0x8, 0x7, 0x2c, 0x55,
    0x21, 0x40, 0x38, 0x74, 0x80, 0x3e, 0x3c, 0x30,
    0xf, 0xfe, 0x11, 0xf9, 0x80, 0x7f, 0x16, 0x48,
    0x80, 0x7f, 0x86, 0x70, 0x40, 0x3f, 0xf8, 0xd,
    0xd6, 0xc4, 0x1, 0x9, 0x35, 0xf3, 0x0, 0x7f,
    0xf1, 0x12, 0x77, 0xfe, 0xed, 0x94, 0x0, 0xfe,

    /* U+F013 "" */
    0x0, 0xff, 0xec, 0x16, 0x7f, 0xed, 0x60, 0xf,
    0xfe, 0x3b, 0x98, 0x6, 0x2c, 0x0, 0xff, 0xf1,
    0x68, 0x7, 0x98, 0x40, 0x3f, 0xe1, 0xc8, 0x0,
    0x2e, 0xb8, 0x7, 0x8f, 0xa0, 0x0, 0xda, 0x80,
    0x1d, 0xa6, 0xfd, 0x54, 0x20, 0xf, 0xe7, 0xcc,
    0x49, 0x51, 0x0, 0x4e, 0x60, 0x1, 0x50, 0xf,
    0xfe, 0x1, 0x98, 0x0, 0x32, 0x0, 0x18, 0x0,
    0xff, 0xe5, 0x22, 0x1, 0x80, 0x3f, 0xf9, 0x9e,
    0x16, 0x1, 0xfc, 0x31, 0x9b, 0x44, 0x1, 0xfc,
    0x83, 0xe6, 0x1, 0xf0, 0xfb, 0x99, 0x2e, 0xa0,
    0x7, 0xc3, 0x22, 0x79, 0x0, 0x1e, 0xa1, 0x0,
    0xeb, 0x10, 0xe, 0x4f, 0x60, 0x8, 0xc0, 0x38,
    0x50, 0x3, 0xe7, 0x0, 0xe2, 0x10, 0xc, 0x20,
    0x1c, 0x40, 0x1f, 0x84, 0x3, 0x8c, 0x3, 0x84,
    0x3, 0x88, 0x3, 0xf0, 0x80, 0x71, 0x0, 0x71,
    0x80, 0x70, 0xa0, 0x7, 0xce, 0x1, 0xc6, 0x20,
    0x3, 0xc8, 0x0, 0xf5, 0x8, 0x7, 0x58, 0x80,
    0x72, 0x7b, 0x7, 0x98, 0x7, 0xc3, 0xee, 0x64,
    0xba, 0x80, 0x1f, 0xc, 0x8d, 0x80, 0x7f, 0xc,
    0x66, 0xd1, 0x0, 0x7f, 0x20, 0xb0, 0x80, 0x7f,
    0xf2, 0xfc, 0x6, 0x40, 0x3f, 0xf9, 0x48, 0x80,
    0x3, 0x98, 0x0, 0x54, 0x3, 0xff, 0x80, 0x66,
    0x0, 0xd, 0x0, 0x6d, 0x37, 0xea, 0xa1, 0x0,
    0x7f, 0x3e, 0x62, 0x8a, 0x84, 0x3, 0xe, 0x40,
    0x1, 0x75, 0xc0, 0x3c, 0x7f, 0x0, 0x5, 0xd4,
    0x0, 0xff, 0xe0, 0x68, 0x7, 0x98, 0x3, 0xff,
    0xc4, 0xe6, 0x1, 0x8b, 0x0, 0x3f, 0xf8, 0xe5,
    0x9f, 0xfb, 0x58, 0x3, 0xfc,

    /* U+F015 "" */
    0x0, 0xff, 0xe1, 0x1b, 0xb0, 0x7, 0x24, 0x44,
    0x80, 0x1f, 0xfc, 0x67, 0xc8, 0x9d, 0x10, 0xb,
    0x9d, 0xee, 0x0, 0xff, 0xe2, 0xd4, 0x0, 0x45,
    0xe6, 0x0, 0x10, 0x8, 0x40, 0x3f, 0xf8, 0x63,
    0x8a, 0x1, 0xc3, 0x8c, 0x1, 0xff, 0xc8, 0x3f,
    0x30, 0x9, 0xcc, 0x2, 0x99, 0x0, 0x7f, 0xf1,
    0x9b, 0x4, 0x0, 0x39, 0x18, 0xa0, 0x13, 0x20,
    0x7, 0xff, 0x12, 0x24, 0x2, 0x2c, 0x33, 0xd,
    0x40, 0x7, 0xff, 0x18, 0x6d, 0xc0, 0x25, 0xd2,
    0x8c, 0xc1, 0x3d, 0x80, 0x7f, 0xf1, 0xb, 0x10,
    0x2, 0x7a, 0xb, 0x70, 0x2d, 0x44, 0x69, 0x0,
    0x7f, 0xf0, 0x53, 0x48, 0x2, 0xa8, 0x2d, 0x40,
    0xd, 0x6e, 0x58, 0x80, 0x12, 0x50, 0x7, 0x9e,
    0xc0, 0x21, 0xc5, 0x3c, 0x20, 0xf, 0x45, 0xd,
    0xb0, 0x4, 0xb8, 0x20, 0x15, 0x40, 0x4, 0x7e,
    0x6d, 0x82, 0x1, 0xf9, 0x70, 0x66, 0x40, 0x11,
    0xf9, 0x85, 0xa8, 0x4, 0xb8, 0x33, 0x20, 0xf,
    0xf8, 0xfc, 0xda, 0xc4, 0x0, 0x38, 0x80, 0x1a,
    0x28, 0x6d, 0x80, 0x3f, 0xf8, 0x23, 0x8a, 0x98,
    0x40, 0x13, 0xd9, 0x5, 0xb9, 0x62, 0x0, 0x7f,
    0xf1, 0x2a, 0xb, 0x50, 0x24, 0xc3, 0x35, 0x2,
    0x48, 0x3, 0xff, 0x8c, 0xe6, 0x17, 0x8c, 0x0,
    0x12, 0x0, 0x38, 0x7, 0xff, 0x21, 0xc0, 0x6,
    0x1, 0xff, 0xe3, 0xbf, 0xfc, 0x60, 0x1f, 0xfc,
    0xe6, 0x0, 0xcc, 0x1, 0xff, 0xff, 0x0, 0xff,
    0xfd, 0xa0, 0x7, 0x88, 0x80, 0x1b, 0x0, 0x3c,
    0x2a, 0x1, 0xc0,

    /* U+F019 "" */
    0x0, 0xff, 0x8d, 0x57, 0x18, 0x7, 0xff, 0x25,
    0x72, 0xab, 0x62, 0x80, 0x7f, 0xf2, 0x34, 0x3,
    0xda, 0x1, 0xff, 0xff, 0x0, 0xff, 0xff, 0x80,
    0x7f, 0xff, 0xc0, 0x3f, 0xf8, 0x23, 0x9b, 0xb9,
    0x80, 0x3c, 0xdb, 0xbb, 0x4, 0x3, 0xf2, 0x99,
    0x16, 0x10, 0xf, 0x9, 0x16, 0x35, 0x0, 0xfc,
    0x8c, 0x1, 0xff, 0xc4, 0x64, 0x0, 0xfe, 0x96,
    0x0, 0xff, 0xe1, 0x34, 0x80, 0x7f, 0xd2, 0xc0,
    0x1f, 0xfc, 0x6, 0x90, 0xf, 0xfe, 0xc, 0xb8,
    0x7, 0xf9, 0xe4, 0x3, 0xff, 0x87, 0xe, 0x1,
    0xf9, 0xe0, 0x3, 0xff, 0x8b, 0xe, 0x1, 0xe7,
    0x80, 0xf, 0xf0, 0x8f, 0xe0, 0x87, 0x0, 0xcf,
    0x0, 0x23, 0xf8, 0x2f, 0xbb, 0xf6, 0x8c, 0x38,
    0x1, 0xe0, 0x77, 0xbb, 0xf5, 0xa0, 0x7, 0xe2,
    0xf2, 0x89, 0x94, 0x16, 0x10, 0x7, 0xe4, 0x0,
    0xff, 0xe, 0x1b, 0x30, 0xf0, 0x80, 0x3f, 0xf9,
    0x23, 0x9b, 0xac, 0x10, 0xf, 0xfe, 0x61, 0x10,
    0x3, 0xff, 0xa9, 0x7c, 0x29, 0xee, 0x1, 0xff,
    0xca, 0x13, 0x7, 0x10, 0xc, 0x20, 0x1f, 0xfc,
    0x6a, 0xd1, 0x3e, 0x70, 0x0, 0xec, 0x3b, 0xff,
    0xf3, 0x23, 0x40,

    /* U+F01C "" */
    0x0, 0xf1, 0x5f, 0xff, 0xff, 0xf, 0xa0, 0x3,
    0xff, 0x80, 0x38, 0x80, 0x1f, 0xfc, 0x31, 0x78,
    0x0, 0xff, 0xac, 0x40, 0x3f, 0xf8, 0xce, 0x60,
    0x1f, 0xca, 0xa0, 0x1, 0x3b, 0xff, 0xf0, 0x58,
    0x2, 0xe0, 0xf, 0xc3, 0x60, 0x17, 0x44, 0x7f,
    0xf0, 0x65, 0x0, 0x5, 0x0, 0x1f, 0x58, 0x80,
    0x1c, 0xc0, 0x3f, 0xf8, 0x54, 0x20, 0x7, 0x30,
    0xe, 0x55, 0x0, 0xa, 0x0, 0x3f, 0xf8, 0x63,
    0x60, 0x17, 0x0, 0x61, 0xb0, 0xb, 0x80, 0x3f,
    0xf8, 0xaa, 0x80, 0x2, 0x80, 0xa, 0xc4, 0x0,
    0xe6, 0x1, 0xff, 0xc6, 0xa1, 0x0, 0x39, 0x2,
    0xa8, 0x0, 0x50, 0x1, 0xff, 0xc7, 0x1b, 0x0,
    0xbc, 0x20, 0x2, 0x68, 0x8e, 0x70, 0xf, 0xe4,
    0x88, 0xed, 0x10, 0x1, 0x29, 0x0, 0x48, 0xef,
    0xd0, 0xe0, 0x1f, 0xa9, 0xdf, 0xc2, 0x1, 0x10,
    0x7, 0xfd, 0x20, 0x1f, 0x38, 0x80, 0x7f, 0xbc,
    0x3, 0xfe, 0x17, 0x0, 0xf4, 0x80, 0x7f, 0xf3,
    0x23, 0xff, 0xc2, 0x1, 0xff, 0xff, 0x0, 0xff,
    0xfa, 0x7a, 0x0, 0x7f, 0xf4, 0x92, 0x90, 0x3,
    0xff, 0x9e, 0x34, 0x60,

    /* U+F021 "" */
    0x0, 0xff, 0xe6, 0x2c, 0x41, 0x80, 0x3f, 0x92,
    0x77, 0xbf, 0xdd, 0x6e, 0x40, 0x1d, 0xe, 0xe9,
    0x0, 0xf9, 0xb6, 0xd8, 0x84, 0x0, 0x29, 0x1b,
    0x66, 0x1, 0xff, 0xc0, 0x1b, 0x92, 0x0, 0xff,
    0x26, 0x50, 0x80, 0x80, 0x7c, 0x5e, 0x80, 0x1e,
    0x11, 0x0, 0x79, 0x7c, 0xdc, 0x3, 0xc5, 0x82,
    0x1, 0x9a, 0xff, 0xb9, 0xf6, 0x80, 0x18, 0x75,
    0x80, 0x3d, 0xe2, 0x1, 0x1e, 0xca, 0x0, 0x64,
    0xbc, 0x30, 0x8, 0x48, 0x3, 0xa4, 0x80, 0x26,
    0xc2, 0x0, 0xfc, 0x78, 0xe0, 0x1f, 0x89, 0x80,
    0x26, 0x90, 0xf, 0xc2, 0x20, 0x4, 0x38, 0x7,
    0xd6, 0x1, 0x14, 0x80, 0x7e, 0x3e, 0xe7, 0xfb,
    0x98, 0x3, 0xe7, 0x0, 0xa4, 0x3, 0xf9, 0xc0,
    0x30, 0x88, 0x3, 0xc6, 0x20, 0x1, 0x40, 0xf,
    0xfe, 0x50, 0xa1, 0x9a, 0x0, 0x3f, 0xca, 0x86,
    0x7f, 0xe5, 0x2b, 0xcc, 0x30, 0x7, 0xf8, 0x6f,
    0x33, 0xfd, 0x40, 0x1f, 0xfe, 0xca, 0xcc, 0xff,
    0x58, 0x80, 0x7f, 0x9a, 0xf2, 0xc9, 0x4c, 0xff,
    0xc8, 0xa0, 0x1f, 0xe8, 0x43, 0x40, 0xf, 0xfe,
    0x5a, 0x88, 0x0, 0x48, 0x3, 0xc2, 0x20, 0xc,
    0xe0, 0x1f, 0xd0, 0x1, 0x38, 0x7, 0xcd, 0xdf,
    0xee, 0xe1, 0x80, 0x7e, 0xa2, 0x0, 0xac, 0x3,
    0xe7, 0x80, 0x0, 0x88, 0x3, 0xf4, 0xa8, 0x4,
    0xc4, 0x1, 0xf9, 0xec, 0x80, 0x3f, 0x16, 0x30,
    0x4, 0x52, 0x1, 0xc4, 0x20, 0x12, 0x6d, 0x20,
    0x6, 0x4a, 0xd3, 0x0, 0x87, 0xc0, 0x3c, 0xda,
    0x20, 0x19, 0x6f, 0xfb, 0x9f, 0x6a, 0x1, 0x87,
    0x8, 0x3, 0xce, 0x7e, 0xa0, 0x1e, 0x11, 0x0,
    0x79, 0x3c, 0x80, 0x3e, 0x10, 0x1a, 0xc4, 0x0,
    0xff, 0x14, 0xd8, 0x80, 0x7f, 0xf0, 0xe, 0xf6,
    0xc, 0x3, 0x13, 0x5e, 0xb0, 0x7, 0xd2, 0xee,
    0x80, 0xe, 0x27, 0xcf, 0xfd, 0xb2, 0x80, 0x1f,
    0xc0,

    /* U+F026 "" */
    0x0, 0xff, 0xe6, 0x9d, 0xb0, 0x7, 0xf8, 0xf1,
    0x24, 0x3, 0xf8, 0xf0, 0x3, 0xfe, 0x3c, 0x0,
    0xff, 0x8f, 0x0, 0x38, 0xe2, 0x3d, 0x80, 0x1e,
    0xd7, 0x7f, 0x0, 0x7c, 0x20, 0x1f, 0xff, 0xf0,
    0xf, 0xfe, 0xc3, 0x0, 0x7f, 0xf0, 0x67, 0xff,
    0xce, 0x1, 0xff, 0xc1, 0x87, 0x0, 0xff, 0xe0,
    0xc3, 0x80, 0x7f, 0xf0, 0x61, 0xc0, 0x3f, 0xf8,
    0x30, 0xe0, 0x60, 0x1f, 0xf4, 0x76, 0x0,

    /* U+F027 "" */
    0x0, 0xff, 0xea, 0x1d, 0xb0, 0x7, 0xff, 0x14,
    0xf5, 0x24, 0x3, 0xff, 0x88, 0x78, 0x20, 0x1f,
    0xfc, 0x53, 0xc0, 0xf, 0xfe, 0x31, 0xe0, 0x7,
    0xff, 0x0, 0xe2, 0x3d, 0x80, 0x1f, 0xfc, 0x1d,
    0x77, 0xf0, 0x7, 0xf0, 0xdc, 0x80, 0x4, 0x3,
    0xff, 0x86, 0xe8, 0xd8, 0x1, 0xff, 0xc5, 0x73,
    0x3, 0x70, 0xf, 0xfe, 0x20, 0xe0, 0x85, 0x0,
    0x7f, 0xf1, 0x90, 0x8, 0x3, 0xff, 0x8c, 0xe0,
    0x40, 0x1f, 0xfc, 0x5b, 0x20, 0x50, 0xf, 0xfe,
    0x23, 0x20, 0x14, 0x0, 0x7f, 0xf1, 0x10, 0x93,
    0x1, 0x80, 0x3f, 0xf8, 0x65, 0xb6, 0x21, 0x3f,
    0xfe, 0x70, 0xf, 0xfe, 0x44, 0x38, 0x7, 0xff,
    0x22, 0x1c, 0x3, 0xff, 0x91, 0xe, 0x1, 0xff,
    0xc8, 0x87, 0x3, 0x0, 0xff, 0xe3, 0x46, 0xe0,
    0x7, 0xc0,

    /* U+F028 "" */
    0x0, 0xff, 0xe5, 0xc, 0x28, 0x7, 0xff, 0x45,
    0x9e, 0xa8, 0x1, 0xff, 0xd0, 0x40, 0x2, 0xe0,
    0x80, 0x7f, 0xf0, 0xce, 0xd8, 0x3, 0xf1, 0xe9,
    0x1, 0xe8, 0x7, 0xff, 0x8, 0xf1, 0x24, 0x3,
    0xc4, 0x20, 0x58, 0x60, 0x72, 0x1, 0xff, 0xc0,
    0x3c, 0x0, 0xfc, 0x9b, 0xea, 0x3, 0xa2, 0xc,
    0x60, 0x1f, 0xe3, 0xc0, 0xf, 0xe1, 0x1, 0xa6,
    0x1, 0xa0, 0x7, 0x80, 0x7f, 0x1e, 0x0, 0x7f,
    0x96, 0x0, 0x12, 0x80, 0x8a, 0x6, 0x80, 0x71,
    0x1e, 0xc0, 0xf, 0xfe, 0x3, 0xd0, 0x2, 0x80,
    0x10, 0x0, 0xe0, 0xd7, 0x7f, 0x0, 0x7f, 0xe,
    0x50, 0x82, 0xb8, 0xb, 0x1, 0x18, 0x28, 0x8,
    0x7, 0xff, 0xd, 0xcd, 0x70, 0x1, 0x20, 0xa,
    0x0, 0x20, 0x0, 0x80, 0x3f, 0xf8, 0x8e, 0x60,
    0x50, 0x2, 0xa0, 0x60, 0xc, 0x0, 0x38, 0x7,
    0xff, 0x10, 0x70, 0x81, 0x40, 0x1a, 0x0, 0x20,
    0x70, 0x1, 0x0, 0x7f, 0xf1, 0x9c, 0x8, 0x0,
    0x20, 0x1, 0x1, 0x0, 0xff, 0xe4, 0xb8, 0x10,
    0x0, 0x40, 0x2, 0x2, 0x1, 0xff, 0xc7, 0x1c,
    0x20, 0x50, 0x6, 0x80, 0x8, 0x1c, 0x0, 0x40,
    0x1f, 0xfc, 0x47, 0x30, 0x28, 0x1, 0x50, 0x30,
    0x6, 0x0, 0x1c, 0x3, 0xff, 0x88, 0xe6, 0xb8,
    0x0, 0x80, 0x5, 0x0, 0x14, 0x4, 0x98, 0x3,
    0xff, 0x86, 0x39, 0x42, 0xa, 0xc0, 0x2c, 0x6,
    0x40, 0x81, 0x3f, 0xfe, 0x70, 0xf, 0xfe, 0x3,
    0xd0, 0x2, 0x80, 0x12, 0x0, 0xe0, 0xf, 0xd0,
    0xe0, 0x1f, 0xe5, 0x80, 0x4, 0xa0, 0x22, 0x81,
    0xa0, 0x7, 0xf4, 0x38, 0x7, 0xf0, 0x80, 0xd3,
    0x0, 0xd0, 0x3, 0xc0, 0x3f, 0xe8, 0x70, 0xf,
    0xc9, 0xbe, 0xa0, 0x38, 0x20, 0xc6, 0x1, 0xff,
    0xc0, 0x87, 0x3, 0x0, 0xf1, 0x8, 0x16, 0x10,
    0x1c, 0x80, 0x7f, 0xf0, 0xa3, 0xb0, 0x3, 0xf1,
    0xe9, 0x1, 0xe8, 0x7, 0xff, 0x10, 0x40, 0x3f,
    0x90, 0x0, 0xb8, 0x20, 0x1f, 0xfc, 0xe6, 0x7a,
    0xa0, 0x7, 0x80,

    /* U+F03E "" */
    0x1b, 0xff, 0xff, 0xe6, 0x58, 0xd2, 0x0, 0x7f,
    0xf3, 0x12, 0x90, 0x3, 0xff, 0x9c, 0x80, 0x19,
    0xb3, 0x60, 0x3, 0xff, 0x96, 0xd2, 0x64, 0xf4,
    0x1, 0xff, 0xca, 0xb0, 0xc, 0xa2, 0x1, 0xff,
    0xd8, 0x27, 0x0, 0xff, 0x94, 0x3, 0x18, 0x80,
    0x7c, 0x79, 0x12, 0x1, 0xfe, 0x85, 0x0, 0x1f,
    0x0, 0x7c, 0x7a, 0x20, 0xd2, 0x1, 0xfe, 0xae,
    0xe6, 0x10, 0x7, 0x8f, 0x44, 0x2, 0x69, 0x0,
    0xff, 0x8, 0xc0, 0x1e, 0x3d, 0x10, 0xe, 0x69,
    0x0, 0xff, 0x1f, 0xe0, 0x80, 0x47, 0xa2, 0x1,
    0xf3, 0x30, 0x3, 0xf1, 0xe8, 0x9e, 0x88, 0x1e,
    0x88, 0x7, 0xf0, 0x80, 0x7c, 0x7a, 0x20, 0x3,
    0xd4, 0xd1, 0x0, 0xff, 0xe2, 0x96, 0x88, 0x6,
    0x3b, 0x10, 0xf, 0xfe, 0x32, 0x88, 0x7, 0xff,
    0xa9, 0x22, 0x3f, 0xf9, 0x8, 0x1, 0xe3, 0x77,
    0xff, 0xe4, 0x18, 0x4, 0x80, 0x1f, 0xfc, 0xe4,
    0xa4, 0x0, 0xff, 0xe6, 0x25, 0x0,

    /* U+F043 "" */
    0x0, 0xfc, 0x72, 0xa0, 0x1f, 0xfc, 0x5d, 0x6a,
    0x20, 0xf, 0xfe, 0x1a, 0x8, 0x2, 0x80, 0x3f,
    0xf8, 0x7c, 0x1, 0x30, 0x7, 0xff, 0x8, 0x54,
    0x2, 0x14, 0x0, 0xff, 0xe0, 0xc8, 0x7, 0x40,
    0x7, 0xff, 0x0, 0x5c, 0x3, 0x8d, 0x0, 0x3f,
    0xe9, 0x0, 0xfa, 0x0, 0x3f, 0xc4, 0xe0, 0x1f,
    0x1b, 0x0, 0x7f, 0x70, 0x7, 0xf4, 0x88, 0x7,
    0xce, 0x60, 0x1f, 0xea, 0x0, 0xf1, 0xc0, 0x7,
    0xfc, 0x8e, 0x1, 0xdc, 0x1, 0xff, 0xc1, 0x83,
    0x0, 0x9c, 0x80, 0x3f, 0xf8, 0x50, 0x1, 0x48,
    0x7, 0xff, 0xd, 0xc, 0x18, 0x40, 0x3f, 0xf8,
    0x94, 0x1a, 0x1, 0xff, 0xc5, 0x70, 0x60, 0xf,
    0xfe, 0x28, 0x88, 0x80, 0xb, 0xe4, 0x1, 0xff,
    0xc1, 0x21, 0x0, 0x10, 0x38, 0x7, 0xff, 0x4,
    0x4c, 0x0, 0x81, 0x80, 0x1f, 0xfc, 0x13, 0x40,
    0x1, 0x93, 0x18, 0x7, 0xfc, 0x43, 0xa0, 0x14,
    0x86, 0x30, 0x7, 0xfa, 0x81, 0xc, 0x0, 0x94,
    0x13, 0xfa, 0x60, 0x1e, 0x16, 0x0, 0x70, 0x4,
    0xba, 0xc2, 0x4e, 0x1, 0xeb, 0x10, 0x1, 0x50,
    0x4, 0x53, 0xde, 0x80, 0x1c, 0xea, 0x1, 0x96,
    0xc4, 0x3, 0xfd, 0x30, 0x1, 0xe4, 0xf7, 0x0,
    0xf9, 0x75, 0x80, 0x3f, 0xc, 0x7d, 0xcc, 0x4d,
    0xfd, 0x10, 0x7, 0x0,

    /* U+F048 "" */
    0x48, 0x88, 0x3, 0xfe, 0x77, 0x7, 0xbb, 0xc6,
    0x1, 0xfd, 0x71, 0x9, 0x0, 0xff, 0xe0, 0xe,
    0x20, 0x1, 0x40, 0x3f, 0xe1, 0xc3, 0x0, 0xff,
    0xe1, 0x17, 0x90, 0x7, 0xff, 0x8, 0xf0, 0x40,
    0x3f, 0xf8, 0x49, 0x82, 0x1, 0xff, 0xc2, 0x5b,
    0x0, 0xff, 0xe1, 0xb5, 0x0, 0x7f, 0xf0, 0xde,
    0x40, 0x3f, 0xf8, 0x71, 0x0, 0xf, 0xfe, 0x22,
    0x0, 0x7f, 0xfe, 0xe8, 0x80, 0x3f, 0xf8, 0x8d,
    0x86, 0x1, 0xff, 0xc4, 0x1c, 0x40, 0xf, 0xfe,
    0x2d, 0xa8, 0x7, 0xff, 0x16, 0x98, 0x3, 0xff,
    0x8b, 0x2e, 0x1, 0xff, 0xc5, 0x89, 0x0, 0xff,
    0xe2, 0xb5, 0x0, 0x7f, 0xf1, 0x56, 0xc0, 0x27,
    0x20, 0x0, 0x88, 0x3, 0xf2, 0x61, 0x12, 0x27,
    0xfd, 0xc4, 0x1, 0xfc, 0x7b, 0xa3,

    /* U+F04B "" */
    0x3, 0x75, 0x0, 0xff, 0xe4, 0xbe, 0x45, 0x61,
    0x80, 0x7f, 0xf1, 0xe4, 0x2, 0x3c, 0x91, 0x0,
    0xff, 0xe2, 0x88, 0x7, 0x37, 0xb0, 0x7, 0xff,
    0x2c, 0x67, 0x4c, 0x3, 0xff, 0x96, 0x59, 0x42,
    0x1, 0xff, 0xcb, 0x5e, 0x70, 0xf, 0xfe, 0x64,
    0x6a, 0x0, 0x7f, 0xf2, 0xca, 0xec, 0x40, 0x1f,
    0xfc, 0xb4, 0xd8, 0x0, 0xff, 0xe6, 0x3f, 0x28,
    0x7, 0xff, 0x2c, 0x6a, 0xc8, 0x3, 0xff, 0x96,
    0x9a, 0xe0, 0x1f, 0xfc, 0xc8, 0x40, 0xf, 0xfe,
    0x61, 0x80, 0x7f, 0xf3, 0xc, 0x3, 0xff, 0x97,
    0x8, 0x1, 0xff, 0xc9, 0x4d, 0x70, 0xf, 0xfe,
    0x40, 0xd5, 0x90, 0x7, 0xff, 0x21, 0xf9, 0x40,
    0x3f, 0xf9, 0x9, 0xb0, 0x1, 0xff, 0xc8, 0x2b,
    0xb1, 0x0, 0x7f, 0xf2, 0x23, 0x50, 0x3, 0xff,
    0x90, 0xbc, 0xe0, 0x1f, 0xfc, 0x82, 0xca, 0x10,
    0xf, 0xfe, 0x38, 0xce, 0x98, 0x7, 0xff, 0x8,
    0x40, 0x39, 0xbd, 0x80, 0x3f, 0xf8, 0x90, 0x1,
    0x1e, 0x48, 0x80, 0x7f, 0xf1, 0x5b, 0x22, 0xf0,
    0xc0, 0x3f, 0xf8, 0xe0,

    /* U+F04C "" */
    0x1a, 0xef, 0xfe, 0xd5, 0x0, 0xc3, 0x5d, 0xff,
    0xda, 0xa1, 0x6a, 0x20, 0x1c, 0x54, 0x40, 0x15,
    0xa8, 0x80, 0x71, 0x51, 0x28, 0x7, 0xf2, 0x80,
    0x4a, 0x1, 0xfc, 0xa0, 0x1f, 0xef, 0x0, 0xff,
    0xe0, 0xf8, 0x7, 0xff, 0xfc, 0x3, 0xff, 0xfe,
    0x1, 0xff, 0xff, 0x0, 0xff, 0xff, 0x80, 0x7f,
    0xff, 0xc0, 0x3f, 0xfe, 0x1e, 0x1, 0xff, 0xc1,
    0xf1, 0x0, 0xfe, 0x10, 0x8, 0x40, 0x3f, 0x86,
    0x0, 0x3f, 0x13, 0x0, 0x50, 0x1, 0xf8, 0x99,
    0x96, 0xec, 0xd9, 0xe3, 0x0, 0x33, 0x5b, 0xb3,
    0x67, 0x8c, 0x0,

    /* U+F04D "" */
    0x3, 0x88, 0xff, 0xe4, 0x30, 0x1, 0xb1, 0xdf,
    0xff, 0x91, 0x3a, 0x10, 0x1, 0xff, 0xca, 0x26,
    0x10, 0xf, 0xfe, 0x58, 0x80, 0x7f, 0xf3, 0x3c,
    0x3, 0xff, 0xfe, 0x1, 0xff, 0xff, 0x0, 0xff,
    0xff, 0x80, 0x7f, 0xff, 0xc0, 0x3f, 0xff, 0xe0,
    0x1f, 0xff, 0xef, 0x50, 0xf, 0xfe, 0x5a, 0xda,
    0x88, 0x7, 0xff, 0x1c, 0xa8, 0x80,

    /* U+F051 "" */
    0x5, 0x81, 0x0, 0xff, 0x34, 0x41, 0xd6, 0x9f,
    0xc8, 0x3, 0xfa, 0x1d, 0xd0, 0x40, 0x1, 0xc3,
    0x0, 0xfc, 0x20, 0x17, 0x80, 0x43, 0x88, 0x1,
    0xff, 0xc5, 0xb5, 0x0, 0xff, 0xe2, 0xd3, 0x0,
    0x7f, 0xf1, 0x66, 0x0, 0x3f, 0xf8, 0xaf, 0x20,
    0x1f, 0xfc, 0x56, 0xa0, 0xf, 0xfe, 0x2a, 0xd8,
    0x7, 0xff, 0x15, 0x30, 0x3, 0xff, 0x8a, 0x60,
    0x1f, 0xff, 0xa1, 0xc0, 0xf, 0xfe, 0x18, 0xe1,
    0x80, 0x7f, 0xf0, 0x8b, 0x8, 0x3, 0xff, 0x84,
    0x58, 0x40, 0x1f, 0xfc, 0x23, 0xc1, 0x0, 0xff,
    0xe1, 0x26, 0x8, 0x7, 0xff, 0x9, 0x6c, 0x3,
    0xff, 0x86, 0xd4, 0x1, 0xff, 0x78, 0x4, 0xf2,
    0x1, 0xff, 0xc0, 0x63, 0x9, 0x80, 0xf, 0xe3,
    0x0, 0x10, 0xe7, 0xb0, 0x7, 0xfb, 0x3f, 0xda,

    /* U+F052 "" */
    0x0, 0xff, 0x86, 0x69, 0x40, 0x3f, 0xf9, 0x23,
    0xec, 0xb5, 0x0, 0x1f, 0xfc, 0x71, 0xc1, 0x0,
    0x9d, 0x80, 0x3f, 0xf8, 0xda, 0x40, 0x1d, 0x2a,
    0x1, 0xff, 0xc4, 0xb3, 0x0, 0xfa, 0x90, 0x3,
    0xff, 0x85, 0x48, 0x1, 0xfd, 0x66, 0x1, 0xff,
    0xc0, 0x95, 0x0, 0xff, 0xb4, 0x80, 0x3f, 0xd0,
    0xc0, 0x1f, 0xfc, 0x1, 0xc1, 0x0, 0xfc, 0xce,
    0x1, 0xff, 0xc2, 0x1c, 0x10, 0xf, 0x2c, 0x80,
    0x7f, 0xf1, 0xb, 0x40, 0x39, 0x28, 0x3, 0xff,
    0x8c, 0x76, 0x1, 0x1d, 0x80, 0x7f, 0xf2, 0x12,
    0x80, 0x1e, 0x1, 0xff, 0xca, 0x55, 0x1, 0x80,
    0x7f, 0xf2, 0xc8, 0x14, 0x3, 0xff, 0x96, 0x81,
    0x68, 0x1, 0xff, 0xc8, 0x19, 0x30, 0x1b, 0xff,
    0xff, 0xe4, 0x73, 0x0, 0x7f, 0xf4, 0x1b, 0xff,
    0xff, 0x95, 0xa2, 0x12, 0x1, 0xff, 0xca, 0x26,
    0x0, 0xff, 0xe6, 0xf8, 0x7, 0xff, 0xa4, 0x40,
    0x3f, 0xf9, 0x7c, 0x16, 0x20, 0x1f, 0xfc, 0x94,
    0x60,

    /* U+F053 "" */
    0x0, 0xff, 0x8e, 0x4c, 0x3, 0xff, 0x80, 0x98,
    0xd8, 0x60, 0x1f, 0xe4, 0xb0, 0xb, 0x40, 0x3f,
    0x92, 0xc0, 0x31, 0x0, 0x7e, 0x4b, 0x0, 0xc5,
    0xa0, 0x1f, 0x25, 0x80, 0x62, 0xc1, 0x0, 0xf2,
    0x58, 0x6, 0x2c, 0x10, 0xf, 0x25, 0x80, 0x62,
    0xc1, 0x0, 0xf2, 0x58, 0x6, 0x2c, 0x10, 0xf,
    0x25, 0x80, 0x62, 0xc1, 0x0, 0xf2, 0x58, 0x6,
    0x2c, 0x10, 0xf, 0x1d, 0x80, 0x62, 0xc1, 0x0,
    0xf9, 0x0, 0x3a, 0x84, 0x3, 0xf3, 0xa0, 0x6,
    0x86, 0x0, 0xfe, 0xb4, 0x0, 0xd2, 0xc0, 0x1f,
    0xd6, 0x80, 0x1a, 0x58, 0x3, 0xfa, 0xd0, 0x3,
    0x4b, 0x0, 0x7f, 0x5a, 0x0, 0x69, 0x60, 0xf,
    0xeb, 0x30, 0xd, 0x2c, 0x1, 0xfd, 0x86, 0x1,
    0xa5, 0x80, 0x3f, 0xb0, 0xc0, 0x34, 0xb0, 0x7,
    0xf6, 0x18, 0x6, 0x90, 0xf, 0xf6, 0x18, 0x4,
    0xa0, 0x1f, 0xf6, 0x20, 0x2d, 0x0, 0x7f, 0xf0,
    0x2f, 0x68, 0x0,

    /* U+F054 "" */
    0x0, 0x3c, 0x0, 0x7f, 0xf0, 0x6a, 0x1e, 0xc0,
    0x3f, 0xe6, 0x50, 0x2, 0x58, 0x7, 0xf8, 0x80,
    0x32, 0x58, 0x7, 0xf2, 0x40, 0x6, 0x4b, 0x0,
    0xfe, 0x78, 0x0, 0xc9, 0x60, 0x1f, 0xcf, 0x0,
    0x19, 0x2c, 0x3, 0xf9, 0xe0, 0x3, 0x25, 0x80,
    0x7f, 0x3c, 0x0, 0x64, 0xb0, 0xf, 0xe7, 0x80,
    0xc, 0x96, 0x1, 0xfc, 0xf0, 0x1, 0x92, 0xc0,
    0x3f, 0x9e, 0x0, 0x32, 0x50, 0x7, 0xf3, 0x8,
    0x6, 0x50, 0xf, 0xc3, 0xa2, 0x1, 0xa8, 0x3,
    0xe1, 0xd3, 0x0, 0xd4, 0xa0, 0x1e, 0x1d, 0x30,
    0xd, 0x4a, 0x1, 0xe1, 0xd3, 0x0, 0xd4, 0xa0,
    0x1e, 0x1d, 0x30, 0xd, 0x4a, 0x1, 0xe1, 0xd3,
    0x0, 0xd4, 0xa0, 0x1e, 0x1c, 0x30, 0xd, 0x4a,
    0x1, 0xe1, 0xc2, 0x0, 0xd4, 0xa0, 0x1f, 0x31,
    0x0, 0x6a, 0x50, 0xf, 0xca, 0x1, 0xa9, 0x40,
    0x3f, 0x8b, 0x0, 0x14, 0xa0, 0x1f, 0xf1, 0xf7,
    0x14, 0x3, 0xfe,

    /* U+F067 "" */
    0x0, 0xff, 0x2d, 0x52, 0x4, 0x3, 0xff, 0x8e,
    0x74, 0xaa, 0x7a, 0x0, 0xff, 0xe3, 0xb0, 0x6,
    0x60, 0xf, 0xfe, 0x39, 0x80, 0x63, 0x0, 0xff,
    0xff, 0x80, 0x7f, 0xff, 0xc0, 0x3f, 0xcd, 0xff,
    0xfa, 0x40, 0x30, 0xff, 0xfe, 0xc1, 0x90, 0xf,
    0xfe, 0x51, 0xb0, 0x7, 0xff, 0x33, 0xc0, 0x3f,
    0xf9, 0x9e, 0xc0, 0x1f, 0xfc, 0xa1, 0x59, 0xdc,
    0xcf, 0x9c, 0x3, 0xe, 0x67, 0xef, 0x20, 0x23,
    0x3f, 0xb8, 0x3, 0x8c, 0xff, 0x8, 0x7, 0xff,
    0xfc, 0x3, 0xff, 0xfe, 0x1, 0xfe, 0x20, 0xc,
    0x40, 0x1f, 0xfc, 0x74, 0x72, 0x21, 0xf8, 0x7,
    0xf8,

    /* U+F068 "" */
    0x1, 0x22, 0xff, 0xe4, 0x8, 0x2, 0x3b, 0x77,
    0xff, 0x91, 0xdc, 0x17, 0x0, 0xff, 0xe5, 0xb,
    0x0, 0x7f, 0xf3, 0x3c, 0x3, 0xff, 0x99, 0xf0,
    0x1, 0xff, 0xca, 0x16, 0x7e, 0xdd, 0xff, 0xe4,
    0x77, 0x4,

    /* U+F06E "" */
    0x0, 0xff, 0x13, 0x56, 0xf7, 0xfb, 0xb2, 0x54,
    0x3, 0xff, 0x8e, 0xb9, 0xb2, 0xa4, 0x20, 0x1,
    0x36, 0xaf, 0xa2, 0x0, 0xff, 0xe1, 0x1e, 0x51,
    0x80, 0x64, 0x54, 0x20, 0xc, 0xbb, 0x22, 0x1,
    0xff, 0x46, 0x18, 0x4, 0x55, 0xf7, 0x57, 0xbc,
    0xe0, 0x19, 0xbd, 0x0, 0x3f, 0xad, 0xc0, 0x32,
    0xea, 0x80, 0x70, 0xc6, 0x8, 0x4, 0x36, 0xc0,
    0x1f, 0x62, 0x0, 0x64, 0xa0, 0x8, 0xe6, 0x4a,
    0x0, 0x3c, 0x10, 0xd, 0x2e, 0x1, 0xd4, 0x60,
    0x18, 0x6c, 0x3, 0x99, 0x95, 0xa4, 0x5, 0x60,
    0x1d, 0xa, 0x1, 0x3a, 0x80, 0x74, 0x80, 0x7f,
    0x16, 0x8, 0x29, 0x0, 0x75, 0x10, 0x14, 0x0,
    0x79, 0x40, 0x3a, 0x40, 0x30, 0xc8, 0x1, 0x40,
    0x3d, 0xc1, 0x20, 0x1f, 0x10, 0x24, 0x35, 0xa8,
    0x7, 0x28, 0x3, 0x40, 0x3c, 0x6a, 0x80, 0x1e,
    0x10, 0x1, 0xbc, 0xa0, 0x7, 0x88, 0x0, 0x20,
    0x1f, 0x62, 0x0, 0x78, 0x40, 0x2, 0x1, 0xfc,
    0x20, 0x1, 0x0, 0xfb, 0x28, 0x3, 0xe2, 0x7,
    0x0, 0xfe, 0x70, 0x6, 0x80, 0x78, 0xd4, 0x64,
    0x3, 0xca, 0x2, 0xe0, 0x1f, 0xac, 0x0, 0xa0,
    0x1e, 0xe0, 0x3, 0x28, 0x7, 0x48, 0x2, 0x14,
    0x3, 0xd6, 0x40, 0xa4, 0x1, 0xd4, 0x40, 0x15,
    0x20, 0x6, 0x1b, 0x0, 0x55, 0xa9, 0x1b, 0xf2,
    0x1, 0x58, 0x7, 0x42, 0x80, 0x75, 0xa0, 0x6,
    0x4a, 0x0, 0x25, 0x6e, 0x40, 0x81, 0xe0, 0x80,
    0x69, 0x70, 0xf, 0xae, 0x0, 0x32, 0xea, 0x80,
    0x70, 0xc6, 0x8, 0x4, 0x36, 0xc0, 0x1f, 0xcf,
    0x86, 0x1, 0x15, 0x7d, 0xd5, 0x33, 0x9c, 0x3,
    0x37, 0xa0, 0x7, 0xfc, 0x79, 0x66, 0x1, 0x91,
    0x54, 0x60, 0x19, 0x76, 0x44, 0x3, 0xff, 0x84,
    0x99, 0xb2, 0xa4, 0x1, 0x9, 0xb5, 0x7d, 0x10,
    0x7, 0xe0,

    /* U+F070 "" */
    0x4, 0x40, 0x7, 0xff, 0x51, 0x2e, 0xd0, 0x1,
    0xff, 0xd3, 0xa0, 0x3, 0xe0, 0x80, 0x7f, 0xf4,
    0x58, 0x2, 0x3f, 0x30, 0xf, 0xfe, 0x84, 0x50,
    0x4, 0x38, 0xe0, 0x18, 0x9a, 0xb7, 0xfd, 0xdb,
    0x6e, 0x60, 0x1f, 0xfc, 0x15, 0xd2, 0x0, 0xa2,
    0x85, 0xb7, 0x52, 0xa4, 0x0, 0x12, 0x48, 0xcd,
    0x70, 0xf, 0xfe, 0x1, 0x6a, 0x80, 0x4b, 0xd2,
    0x40, 0x19, 0x15, 0x4, 0x2, 0x28, 0xe6, 0x0,
    0xff, 0xe0, 0x54, 0x0, 0x7c, 0x75, 0xf7, 0x57,
    0xd8, 0x80, 0x10, 0xcd, 0x88, 0x7, 0xfc, 0xf8,
    0x20, 0x18, 0xb1, 0x40, 0x38, 0xee, 0x0, 0x32,
    0x79, 0x0, 0x7f, 0xc7, 0xe8, 0x1, 0x16, 0x8,
    0x35, 0xd2, 0x80, 0x1e, 0x0, 0x30, 0xe1, 0x0,
    0x7f, 0xc3, 0x6e, 0x1, 0x1f, 0xa0, 0xa2, 0xd6,
    0x8, 0x3a, 0x0, 0x61, 0xf1, 0x0, 0xf5, 0x69,
    0x0, 0x51, 0x60, 0x10, 0xdd, 0x80, 0x23, 0xd0,
    0x4, 0x0, 0x71, 0x50, 0x7, 0x2a, 0x8b, 0x54,
    0x2, 0x4d, 0x20, 0x8, 0xc0, 0x31, 0xa8, 0x18,
    0x80, 0x72, 0x28, 0x6, 0xb0, 0xa, 0xa4, 0x2,
    0x2d, 0x50, 0xf, 0xde, 0x0, 0x50, 0xf, 0x58,
    0x4, 0x42, 0x1, 0x9b, 0x8, 0x2, 0xa9, 0x0,
    0xf8, 0x40, 0x2, 0x1, 0xe1, 0x20, 0x1, 0x8,
    0x7, 0x1f, 0x0, 0x66, 0xc1, 0x0, 0xe1, 0x0,
    0x30, 0x7, 0x84, 0x80, 0x2b, 0x0, 0xf1, 0x80,
    0x71, 0xfa, 0x0, 0x67, 0x20, 0x20, 0xf, 0x58,
    0x6, 0x54, 0x0, 0xf2, 0x0, 0x70, 0xdc, 0x0,
    0x45, 0xb0, 0x20, 0x1c, 0xca, 0x1, 0xd6, 0x40,
    0x1d, 0x0, 0x1f, 0x3d, 0x88, 0x4, 0xe0, 0x1c,
    0x72, 0x1, 0xf7, 0x88, 0x6, 0x38, 0x0, 0xf9,
    0x3c, 0xc0, 0x3e, 0x1d, 0x0, 0xfc, 0x58, 0x40,
    0x19, 0xdc, 0x1, 0xf0, 0xe3, 0x0, 0x70, 0xe0,
    0x80, 0x7f, 0x16, 0x20, 0x6, 0x8c, 0x40, 0xf,
    0xa6, 0x80, 0x30, 0xa0, 0x7, 0xfc, 0x37, 0x22,
    0x1, 0x1d, 0xf5, 0xd5, 0xa8, 0x4, 0xba, 0x40,
    0x14, 0xd0, 0x7, 0xff, 0x1, 0xba, 0x8, 0x2,
    0x14, 0x54, 0xa8, 0x0, 0x8b, 0x54, 0x2, 0x5d,
    0x20, 0xf, 0xfe, 0x3, 0xee, 0x42, 0x10, 0x80,
    0x8, 0xd0, 0x3, 0x54, 0x0, 0x45, 0xaa, 0x1,
    0xff, 0xc1, 0x37, 0xbd, 0xef, 0xf6, 0xda, 0x0,
    0x73, 0xe0, 0x80, 0x55, 0x0, 0x1f, 0xfd, 0x3,
    0xf3, 0x0, 0x98, 0x3, 0xff, 0xa2, 0x38, 0xe0,
    0xa, 0x0, 0xff, 0xe9, 0xc5, 0xd9, 0x0,

    /* U+F071 "" */
    0x0, 0xff, 0xe1, 0xa4, 0x38, 0x80, 0x7f, 0xf4,
    0x16, 0xde, 0x30, 0x3, 0xff, 0x9e, 0x36, 0x1,
    0x13, 0x80, 0x7f, 0xf3, 0xa4, 0x40, 0x34, 0x8,
    0x7, 0xff, 0x30, 0x9c, 0x3, 0xd6, 0x1, 0xff,
    0xcc, 0x90, 0xf, 0x94, 0xc0, 0x3f, 0xf9, 0x48,
    0x80, 0xf, 0xde, 0x1, 0xff, 0xca, 0x90, 0xf,
    0xe3, 0x50, 0xf, 0xfe, 0x43, 0x10, 0x7, 0xfa,
    0xc0, 0x3f, 0xf8, 0xe3, 0x0, 0x1f, 0xf0, 0xc0,
    0x7, 0xff, 0x1a, 0x4, 0x2, 0x7f, 0xfb, 0x84,
    0x2, 0x71, 0x0, 0xff, 0xe2, 0x13, 0x0, 0x6e,
    0x0, 0x84, 0x3, 0xac, 0x3, 0xff, 0x89, 0x20,
    0x1c, 0x20, 0x18, 0x40, 0x32, 0x98, 0x7, 0xff,
    0x9, 0x10, 0x1, 0xde, 0x1, 0xfe, 0xf0, 0xf,
    0xfe, 0x14, 0x80, 0x7f, 0xf1, 0x4d, 0x40, 0x3f,
    0xf8, 0xe, 0x40, 0x1e, 0x10, 0x8, 0x40, 0x3e,
    0xb0, 0xf, 0xf8, 0x60, 0x3, 0xe3, 0x0, 0x8c,
    0x3, 0xe1, 0x80, 0xf, 0xf4, 0x0, 0x7e, 0x10,
    0x8, 0x40, 0x3f, 0x38, 0x80, 0x7e, 0x26, 0x0,
    0xfc, 0xc2, 0x0, 0x50, 0xf, 0xeb, 0x0, 0xfd,
    0x20, 0x1f, 0xc5, 0xdf, 0xe9, 0x0, 0xfe, 0x53,
    0x0, 0xf2, 0xa0, 0x7, 0xfa, 0xbf, 0x90, 0x3,
    0xfd, 0xe0, 0x1e, 0x80, 0xf, 0xf4, 0x28, 0xd,
    0x88, 0x7, 0xf1, 0xb0, 0x6, 0x72, 0x0, 0xff,
    0x18, 0x6, 0x20, 0xf, 0xf4, 0x0, 0x43, 0x0,
    0x1f, 0xf6, 0x0, 0x42, 0x60, 0x1f, 0xe1, 0x80,
    0x4, 0x0, 0x7f, 0xf0, 0x1d, 0x84, 0xf4, 0x3,
    0xff, 0x80, 0xe2, 0xc, 0x1, 0xff, 0xc1, 0x9e,
    0xc3, 0x0, 0xff, 0xe0, 0xb0, 0x8, 0x7, 0xff,
    0x48, 0x42, 0x40, 0x3f, 0xfa, 0x22, 0xc0, 0xf6,
    0xec, 0xdf, 0xfc, 0xd7, 0x8f, 0x0,

    /* U+F074 "" */
    0x0, 0xff, 0xe4, 0xb5, 0x10, 0x7, 0xff, 0x36,
    0x57, 0xc, 0x3, 0xff, 0x9c, 0x3a, 0x60, 0x2,
    0x44, 0xe3, 0x0, 0xff, 0x12, 0x24, 0x1, 0xe,
    0x98, 0x6d, 0xdf, 0x65, 0x80, 0x7e, 0x3d, 0xbb,
    0x80, 0x30, 0xe9, 0x80, 0x7c, 0x94, 0x1, 0xe3,
    0xc0, 0xf, 0xe1, 0xd0, 0xf, 0xcb, 0x20, 0x18,
    0xb4, 0x3, 0xfe, 0x30, 0xf, 0xe6, 0x80, 0x0,
    0xe0, 0x80, 0x7f, 0x8b, 0x3b, 0x77, 0x48, 0x6,
    0x20, 0x1c, 0x10, 0x8, 0xb7, 0x40, 0x18, 0xb0,
    0x44, 0x45, 0x9a, 0x0, 0x9, 0x43, 0x84, 0x1,
    0x16, 0x11, 0x0, 0x22, 0xc1, 0x0, 0xf9, 0xdc,
    0x76, 0x1a, 0x40, 0x10, 0xf8, 0x80, 0x4, 0xb,
    0x4, 0x3, 0xfa, 0x30, 0x2c, 0xc0, 0x21, 0xc2,
    0x0, 0xa9, 0xf0, 0x40, 0x3f, 0xf8, 0x14, 0x80,
    0x1b, 0x48, 0x3, 0x24, 0x8, 0x7, 0xff, 0x2,
    0x54, 0x3, 0x59, 0x80, 0x72, 0x40, 0x80, 0x7f,
    0xd0, 0xc0, 0x1a, 0x91, 0x78, 0x80, 0x2a, 0x7f,
    0x20, 0xf, 0xe7, 0x70, 0x6, 0x95, 0x4a, 0x1f,
    0x10, 0x0, 0x80, 0xe1, 0x0, 0x4, 0x8b, 0x34,
    0x0, 0x68, 0x63, 0xb0, 0x1, 0x61, 0x10, 0x2,
    0x1c, 0x20, 0xed, 0xdd, 0x20, 0x19, 0xdc, 0x4,
    0x20, 0x11, 0x6e, 0x80, 0x30, 0xe1, 0x0, 0x7f,
    0x34, 0x0, 0x7, 0x4, 0x3, 0xfc, 0x3e, 0x1,
    0xf9, 0x64, 0x3, 0x16, 0x80, 0x7f, 0xf1, 0xd2,
    0x80, 0x3c, 0x78, 0x1, 0xfc, 0x3f, 0xb7, 0x7d,
    0x96, 0x1, 0xf8, 0xf6, 0xee, 0x0, 0xc3, 0x84,
    0x44, 0x4e, 0x30, 0xf, 0xf1, 0x22, 0x40, 0x10,
    0xe9, 0x0, 0x7f, 0xf3, 0x7, 0x4c, 0x3, 0xff,
    0x97, 0x2b, 0x86, 0x1, 0x0,

    /* U+F077 "" */
    0x0, 0xff, 0x9e, 0xc8, 0x3, 0xff, 0x93, 0x10,
    0x4c, 0x20, 0xf, 0xfe, 0x3c, 0x38, 0x0, 0x70,
    0x80, 0x3f, 0xf8, 0xb0, 0xe0, 0x18, 0x70, 0x80,
    0x3f, 0xf8, 0x70, 0xe0, 0x1e, 0x1c, 0x20, 0xf,
    0xfe, 0xc, 0x38, 0x7, 0xe1, 0xc2, 0x0, 0xff,
    0xa1, 0xc0, 0x32, 0xe0, 0x6, 0x1c, 0x20, 0xf,
    0xe8, 0x70, 0xc, 0xb4, 0x78, 0x1, 0x87, 0x8,
    0x3, 0xe8, 0x70, 0xc, 0xb4, 0x0, 0x3c, 0x0,
    0xc3, 0x84, 0x1, 0xd0, 0xe0, 0x19, 0x68, 0x3,
    0x1e, 0x0, 0x61, 0xc2, 0x0, 0xa1, 0xc0, 0x32,
    0xd0, 0x7, 0x8f, 0x0, 0x30, 0xe1, 0x3, 0x38,
    0x6, 0x5a, 0x0, 0xfc, 0x78, 0x1, 0x87, 0x3,
    0xc0, 0x32, 0xd0, 0x7, 0xf8, 0xf0, 0x3, 0x8,
    0xa0, 0x80, 0xb, 0x40, 0x1f, 0xfc, 0x3, 0xc0,
    0xa, 0x44, 0x58, 0xaf, 0x40, 0x1f, 0xfc, 0x23,
    0xc4, 0xa6, 0x0,

    /* U+F078 "" */
    0x1, 0xa8, 0x0, 0xff, 0xe2, 0x1d, 0xb0, 0x0,
    0x71, 0x5e, 0x80, 0x3f, 0xf8, 0x47, 0x89, 0x2c,
    0x10, 0x40, 0x5, 0xa0, 0xf, 0xfe, 0x1, 0xe0,
    0x5, 0x23, 0xe0, 0x19, 0x68, 0x3, 0xfc, 0x78,
    0x1, 0x88, 0x59, 0xc0, 0x32, 0xd0, 0x7, 0xe3,
    0xc0, 0xc, 0x3e, 0x0, 0x87, 0x0, 0xcb, 0x40,
    0x1e, 0x3c, 0x0, 0xc3, 0x84, 0x1, 0x43, 0x80,
    0x65, 0xa0, 0xc, 0x78, 0x1, 0x87, 0x8, 0x3,
    0xa1, 0xc0, 0x32, 0xd0, 0x0, 0xf0, 0x3, 0xe,
    0x10, 0x7, 0xd0, 0xe0, 0x19, 0x68, 0xf0, 0x3,
    0xe, 0x10, 0x7, 0xf4, 0x38, 0x6, 0x5c, 0x0,
    0xc3, 0x84, 0x1, 0xff, 0x43, 0x80, 0x7e, 0x1c,
    0x20, 0xf, 0xfe, 0xc, 0x38, 0x7, 0x87, 0x8,
    0x3, 0xff, 0x87, 0xe, 0x1, 0x87, 0x8, 0x3,
    0xff, 0x8b, 0xe, 0x0, 0x1c, 0x20, 0xf, 0xfe,
    0x3c, 0x4a, 0xe1, 0x0, 0x7f, 0x80,

    /* U+F079 "" */
    0x0, 0xf2, 0x10, 0x7, 0xff, 0x52, 0x6f, 0x50,
    0x3, 0xff, 0xa5, 0x2c, 0x0, 0xb4, 0x0, 0xca,
    0xef, 0xff, 0xc1, 0x60, 0xf, 0xe9, 0x60, 0xd,
    0x68, 0x0, 0x4a, 0x88, 0xff, 0xe0, 0xcb, 0x80,
    0x7d, 0x2c, 0x1, 0xeb, 0x40, 0x60, 0xf, 0xfe,
    0x1f, 0x0, 0x7a, 0x58, 0x3, 0xf5, 0xa1, 0x58,
    0x7, 0xff, 0x22, 0x58, 0x3, 0xc2, 0x1, 0x5a,
    0x23, 0xff, 0xfe, 0x10, 0xf, 0xcc, 0x1, 0x58,
    0x80, 0x1d, 0x80, 0x2d, 0x0, 0xff, 0xe4, 0xc0,
    0x2, 0x90, 0x3, 0x4a, 0x80, 0xd8, 0x7, 0xff,
    0x25, 0xf3, 0x54, 0x3, 0xd5, 0x7e, 0x40, 0x1f,
    0xfc, 0xa3, 0x20, 0xf, 0xc8, 0x20, 0x1f, 0xff,
    0xf0, 0xf, 0xfe, 0xd0, 0xcc, 0x0, 0x7c, 0x54,
    0xe0, 0x1f, 0xfc, 0xad, 0x67, 0xa0, 0xe, 0x2c,
    0x58, 0x60, 0xf, 0xfe, 0x49, 0x0, 0x16, 0x40,
    0x23, 0xf1, 0x0, 0x78, 0x7, 0xe5, 0x77, 0xff,
    0x18, 0x50, 0x4, 0xc2, 0x0, 0x52, 0x0, 0x1c,
    0x0, 0x7e, 0x38, 0x8f, 0xf6, 0x2a, 0x58, 0x7,
    0xf1, 0xe8, 0x80, 0x7f, 0xf2, 0x28, 0x12, 0xc0,
    0x3e, 0x3d, 0x10, 0xf, 0x38, 0x7, 0xff, 0x9,
    0x40, 0x9, 0x80, 0x1c, 0x7a, 0x20, 0x1f, 0x4f,
    0xff, 0xff, 0xa, 0x80, 0x23, 0xc0, 0x8, 0xf4,
    0x40, 0x3f, 0xfa, 0x7, 0x84, 0x98, 0x20, 0x18,

    /* U+F07B "" */
    0x1b, 0xff, 0xfe, 0xc1, 0x0, 0xff, 0xe1, 0xd2,
    0x0, 0x7f, 0x1e, 0x88, 0x7, 0xff, 0x9, 0x0,
    0x3f, 0xe3, 0xd1, 0x0, 0xff, 0xe7, 0x1e, 0x44,
    0x7f, 0x9c, 0xc0, 0x3f, 0xf8, 0x64, 0xef, 0xfe,
    0x8c, 0x50, 0xf, 0xfe, 0x7d, 0x80, 0x7f, 0xf3,
    0xc4, 0x3, 0xff, 0xfe, 0x1, 0xff, 0xff, 0x0,
    0xff, 0xff, 0x80, 0x7f, 0xff, 0xc0, 0x3f, 0xfb,
    0x8, 0x1, 0xff, 0xce, 0x4a, 0x40, 0xf, 0xfe,
    0x62, 0x50,

    /* U+F093 "" */
    0x0, 0xff, 0xe0, 0x98, 0x80, 0x7f, 0xf3, 0x5f,
    0x3c, 0x80, 0x3f, 0xf9, 0x6f, 0x0, 0x38, 0x40,
    0x1f, 0xfc, 0x97, 0x80, 0x8, 0x70, 0x80, 0x3f,
    0xf8, 0xef, 0x0, 0x1c, 0x38, 0x40, 0x1f, 0xfc,
    0x57, 0x80, 0xf, 0x87, 0x8, 0x3, 0xff, 0x86,
    0xf0, 0x1, 0xfc, 0x38, 0x40, 0x1f, 0xfc, 0x17,
    0x80, 0xf, 0xf8, 0x70, 0x80, 0x3f, 0xe7, 0x80,
    0xf, 0xfe, 0x8, 0xe1, 0x0, 0x7f, 0x2c, 0x0,
    0x7f, 0xf0, 0xc7, 0x0, 0x3f, 0x8c, 0x3, 0xff,
    0x8a, 0x20, 0x1f, 0xcd, 0xbb, 0xc6, 0x1, 0xe9,
    0xdd, 0xdc, 0x1, 0xfe, 0x22, 0xe7, 0x0, 0xf1,
    0x17, 0x8, 0x7, 0xff, 0xfc, 0x3, 0xff, 0xfe,
    0x1, 0xff, 0xcc, 0x11, 0xf8, 0x3, 0xff, 0x80,
    0x23, 0xf0, 0x5f, 0x77, 0xd8, 0x2, 0x1, 0xe7,
    0xc, 0xee, 0xfa, 0xd0, 0x3, 0xe3, 0x1b, 0x76,
    0x6d, 0x26, 0x26, 0x1, 0xf2, 0x0, 0x7f, 0x68,
    0x44, 0xce, 0x61, 0xd0, 0xf, 0xfe, 0x31, 0xf6,
    0x67, 0xb8, 0xc0, 0x3f, 0xf9, 0x6, 0x7e, 0x0,
    0xff, 0xe9, 0x5f, 0xa, 0x7b, 0x80, 0x7f, 0xf2,
    0x84, 0xc1, 0xc4, 0x3, 0x8, 0x7, 0xff, 0x1a,
    0xb4, 0x4f, 0x9c, 0x0, 0x3b, 0xe, 0xff, 0xfc,
    0xc8, 0xd0,

    /* U+F095 "" */
    0x0, 0xff, 0xe4, 0xb3, 0x8, 0x3, 0xff, 0x9a,
    0x93, 0x2d, 0xeb, 0x73, 0x0, 0xff, 0xe4, 0xf8,
    0x4, 0x29, 0x19, 0x80, 0xf, 0xfe, 0x39, 0x20,
    0x7, 0xc6, 0x1, 0xff, 0xc7, 0xb0, 0xf, 0xc2,
    0x1, 0xff, 0xc6, 0x16, 0x0, 0xff, 0xe7, 0xb0,
    0x7, 0xf1, 0x0, 0x7f, 0xf1, 0xa8, 0x3, 0xf9,
    0x80, 0x3f, 0xf8, 0xcc, 0x1, 0xfd, 0xa0, 0x1f,
    0xfc, 0x6b, 0x40, 0xf, 0xc8, 0x1, 0xff, 0xc7,
    0xb7, 0x0, 0xf8, 0xc0, 0x3f, 0xf9, 0x11, 0x0,
    0xe, 0x60, 0xf, 0xfe, 0x56, 0x80, 0x76, 0x80,
    0x7f, 0xf2, 0x4d, 0x40, 0x31, 0x20, 0x7, 0xff,
    0x27, 0xc0, 0x3a, 0x0, 0x3f, 0xf9, 0x32, 0x60,
    0x18, 0xd4, 0x3, 0xff, 0x90, 0xcc, 0x0, 0xef,
    0x0, 0xff, 0x13, 0x8, 0x7, 0xcb, 0x20, 0x1c,
    0xe6, 0x1, 0xf9, 0x2f, 0x67, 0x40, 0x3c, 0xd4,
    0x1, 0xc7, 0x0, 0x1f, 0x36, 0xda, 0x0, 0xe,
    0x80, 0x35, 0x48, 0x7, 0xe, 0x80, 0x79, 0xfe,
    0x48, 0x3, 0x95, 0xc1, 0x79, 0x40, 0x38, 0x70,
    0x40, 0x3d, 0x0, 0x1f, 0xd1, 0xb4, 0x20, 0x1c,
    0x38, 0x40, 0x1f, 0x18, 0x7, 0xf8, 0x80, 0x3c,
    0x58, 0x40, 0x1f, 0x90, 0x3, 0xff, 0x88, 0xba,
    0x40, 0x1f, 0xd8, 0x1, 0xff, 0xc2, 0x19, 0xa0,
    0xf, 0xf9, 0x40, 0x3f, 0xf8, 0x2d, 0xec, 0x1,
    0xff, 0xc0, 0x12, 0x0, 0xfe, 0x16, 0xe9, 0x10,
    0xf, 0xfe, 0x12, 0x0, 0x78, 0x9a, 0xfa, 0x44,
    0x3, 0xff, 0x89, 0x11, 0x4d, 0x67, 0xec, 0xa0,
    0x7, 0xff, 0x14,

    /* U+F0C4 "" */
    0x0, 0x96, 0x64, 0xe4, 0x1, 0xff, 0xc6, 0x2d,
    0xa6, 0x64, 0x6b, 0x80, 0x7f, 0xb, 0xdd, 0xa0,
    0x40, 0x70, 0x80, 0x3a, 0x1c, 0x3, 0xe2, 0xf8,
    0x44, 0x3f, 0x9c, 0x88, 0x4, 0x20, 0x14, 0x8,
    0x7, 0x16, 0x8, 0x6, 0x17, 0x50, 0x0, 0xf7,
    0x20, 0x2, 0x40, 0xc, 0x58, 0x20, 0x1d, 0x48,
    0x40, 0x6, 0x10, 0x70, 0x8, 0x80, 0x22, 0xc1,
    0x0, 0xea, 0x50, 0x10, 0x2, 0x0, 0x10, 0x2,
    0x10, 0x1, 0x60, 0x80, 0x75, 0x28, 0x1, 0x0,
    0x7, 0xb7, 0x60, 0x8, 0x80, 0xf0, 0x40, 0x3a,
    0x94, 0x2, 0x80, 0x8, 0x90, 0x3, 0x52, 0xe8,
    0x80, 0x75, 0x28, 0x6, 0x2b, 0x0, 0xfc, 0x34,
    0x20, 0x1d, 0x4a, 0x1, 0xe4, 0xe7, 0x32, 0x0,
    0xff, 0xa9, 0x40, 0x3f, 0xc, 0x66, 0xd0, 0x7,
    0xf5, 0x28, 0x7, 0xff, 0x5, 0x60, 0x3, 0xe8,
    0x50, 0xf, 0xfe, 0x11, 0xc8, 0x7, 0xd2, 0x40,
    0x1f, 0xf2, 0xcc, 0xb4, 0x40, 0x3e, 0x1c, 0x20,
    0xf, 0xc5, 0xb4, 0xcc, 0x10, 0xf, 0xe1, 0xc2,
    0x0, 0xf0, 0xe1, 0x0, 0x7e, 0x60, 0xe, 0x1c,
    0x20, 0xe, 0x91, 0x0, 0x84, 0x3, 0x4c, 0xa8,
    0x3, 0x87, 0x8, 0x3, 0x28, 0x0, 0x7b, 0x90,
    0x1, 0x18, 0x2d, 0x0, 0x70, 0xe1, 0x0, 0x44,
    0x0, 0x61, 0x7, 0x0, 0x88, 0x0, 0xb4, 0x1,
    0xc3, 0x84, 0x0, 0x10, 0x2, 0x0, 0x10, 0x2,
    0x10, 0x9, 0x68, 0x3, 0x87, 0x8, 0x10, 0x0,
    0x7b, 0x76, 0x0, 0x90, 0x3, 0x2d, 0x0, 0x70,
    0xe1, 0x40, 0x4, 0x48, 0x1, 0x31, 0x0, 0x72,
    0xd8, 0x7, 0xb, 0x15, 0x80, 0x79, 0x24, 0x3,
    0xe4, 0xc4, 0x10, 0x4c, 0x40, 0x4e, 0x73, 0x24,
    0xab, 0x0, 0xfe, 0x3b, 0xef, 0xb3, 0x0,

    /* U+F0C5 "" */
    0x0, 0xf8, 0x51, 0x3f, 0x88, 0xc, 0x3, 0xff,
    0x81, 0x97, 0x7f, 0xd4, 0x1b, 0x60, 0x1f, 0xf1,
    0x0, 0x7f, 0xf0, 0x12, 0xc0, 0x3f, 0xf9, 0x89,
    0x60, 0x1f, 0xfc, 0xc4, 0xb0, 0xf, 0xfe, 0x62,
    0x22, 0xbf, 0xf2, 0x0, 0x7f, 0xc4, 0x1d, 0xff,
    0x91, 0x40, 0x3f, 0xf8, 0x8b, 0x97, 0x7c, 0x60,
    0x1f, 0xfc, 0x63, 0x44, 0xe6, 0x0, 0xff, 0xff,
    0x80, 0x7f, 0xff, 0xc0, 0x3f, 0xff, 0xe0, 0x1f,
    0xff, 0xf0, 0xf, 0xfe, 0x90, 0x82, 0x80, 0x7f,
    0xf0, 0xc5, 0x0, 0x3b, 0x82, 0xbf, 0xff, 0xf8,
    0x7c, 0x20, 0x1c, 0x94, 0x40, 0x1f, 0xfc, 0xb5,
    0xdf, 0xff, 0xe8, 0x0, 0xff, 0xea, 0x8, 0x7,
    0xff, 0x13, 0x80, 0x3e, 0xd8, 0x77, 0xff, 0xe1,
    0x4b, 0x0, 0x7c,

    /* U+F0C7 "" */
    0x4, 0x88, 0xff, 0xe1, 0xb9, 0x0, 0x79, 0xad,
    0xdf, 0xff, 0x87, 0x1a, 0x80, 0x1d, 0x0, 0x1f,
    0xfc, 0x6b, 0x40, 0xc, 0x20, 0x1f, 0xfc, 0x7b,
    0x40, 0xf, 0x37, 0xff, 0xff, 0x4, 0x80, 0x2b,
    0x40, 0xe, 0x10, 0xf, 0xfe, 0xb, 0x0, 0x6b,
    0x30, 0xf, 0xfe, 0x66, 0x80, 0x7f, 0xf3, 0x4,
    0xc0, 0x3f, 0xf9, 0x8e, 0x1, 0xff, 0xd0, 0x57,
    0x7f, 0xfe, 0xa, 0x80, 0x7f, 0x8a, 0x23, 0xff,
    0x82, 0x20, 0x1f, 0xff, 0x77, 0xde, 0xa1, 0x0,
    0xff, 0xe3, 0xc4, 0x8, 0x57, 0x40, 0x3f, 0xf8,
    0xee, 0x1, 0x8d, 0x0, 0x3f, 0xf8, 0xa6, 0x1,
    0xe3, 0x0, 0xff, 0xe2, 0x90, 0x7, 0x84, 0x3,
    0xff, 0x8a, 0x26, 0x1, 0xce, 0x1, 0xff, 0xc6,
    0xe1, 0x0, 0xa4, 0x40, 0x3f, 0xf8, 0xc5, 0xf5,
    0x3a, 0xc0, 0x1f, 0xfc, 0x81, 0x56, 0x20, 0xf,
    0xf3, 0xa8, 0x7, 0xff, 0x28, 0x4e, 0xd4, 0x40,
    0x3f, 0xf8, 0xe7, 0xa0,

    /* U+F0C9 "" */
    0x0, 0xff, 0xe6, 0xef, 0xff, 0xff, 0x2d, 0x48,
    0x3, 0xff, 0x97, 0xa0, 0x1f, 0xfc, 0xd5, 0x11,
    0xff, 0xe5, 0x16, 0x57, 0x77, 0xff, 0x2b, 0x50,
    0x3, 0xff, 0xfe, 0xf7, 0x7f, 0xfc, 0xaa, 0x28,
    0x44, 0xff, 0xe5, 0x2a, 0x80, 0x3f, 0xf9, 0x9e,
    0x20, 0x1f, 0xfc, 0xbe, 0xfb, 0xbf, 0xfe, 0x56,
    0x30, 0xa2, 0x7f, 0xf2, 0x8c, 0x3, 0xff, 0xfe,
    0x15, 0xdd, 0xff, 0xca, 0xd4, 0x51, 0x1f, 0xfe,
    0x51, 0x60, 0x7, 0xff, 0x34, 0x80, 0x3f, 0xf9,
    0x7b, 0xaf, 0xff, 0xfe, 0x5a, 0x80,

    /* U+F0E0 "" */
    0x1b, 0xff, 0xff, 0xe6, 0x58, 0xd2, 0x0, 0x7f,
    0xf3, 0x12, 0x90, 0x3, 0xff, 0x9c, 0x80, 0x1f,
    0xfd, 0x5, 0x0, 0xff, 0xe7, 0x2d, 0x40, 0x7,
    0xff, 0x32, 0x28, 0x1f, 0x4, 0x3, 0xff, 0x90,
    0x38, 0xe1, 0xca, 0x7e, 0x80, 0x1f, 0xfc, 0x64,
    0xf3, 0x5e, 0x1a, 0x91, 0xb8, 0x0, 0xff, 0xe2,
    0x45, 0x8c, 0xd0, 0x80, 0x1b, 0x49, 0xf0, 0x80,
    0x3f, 0xf8, 0x5, 0x8e, 0x5a, 0xc0, 0x1c, 0x5a,
    0xc7, 0xaa, 0x1, 0xfe, 0x5d, 0x36, 0xd2, 0x0,
    0xfd, 0x34, 0x35, 0x20, 0x1f, 0xa6, 0x82, 0xa4,
    0x3, 0xfe, 0x5f, 0x36, 0xd2, 0x0, 0xc5, 0xac,
    0x7a, 0xa0, 0x1f, 0xfc, 0x11, 0xc6, 0x2d, 0x81,
    0x14, 0x69, 0x3e, 0x10, 0x7, 0xff, 0x12, 0x6c,
    0x5f, 0xb8, 0xe3, 0x70, 0x1, 0xff, 0xc7, 0x4e,
    0x95, 0x54, 0xf2, 0x0, 0x7f, 0xf2, 0x9a, 0xa8,
    0xc0, 0x1f, 0xff, 0x84, 0x0, 0xff, 0xe7, 0x25,
    0x20, 0x7, 0xff, 0x31, 0x28,

    /* U+F0E7 "" */
    0x0, 0xa2, 0xef, 0xf4, 0x0, 0x7e, 0x37, 0x44,
    0xfc, 0xe4, 0x1, 0xf2, 0x80, 0x7f, 0x88, 0x3,
    0xee, 0x0, 0xfe, 0x40, 0xf, 0xc4, 0x1, 0xfd,
    0xc0, 0x1f, 0x98, 0x3, 0xf9, 0x0, 0x3f, 0x10,
    0x7, 0xe4, 0x10, 0xf, 0xc2, 0x1, 0xfb, 0x40,
    0x3f, 0x18, 0x7, 0xf3, 0x80, 0x7e, 0x60, 0xf,
    0xee, 0xff, 0xed, 0x20, 0x20, 0xf, 0xfe, 0x19,
    0x28, 0x70, 0x7, 0xff, 0x11, 0x80, 0x80, 0x3f,
    0xf8, 0x70, 0x20, 0xc0, 0x1f, 0xfc, 0x22, 0x70,
    0x1, 0x0, 0x7f, 0xf0, 0xa0, 0x2, 0x50, 0xf,
    0xfe, 0xa, 0x28, 0x5, 0x5f, 0xfe, 0xa0, 0xf,
    0x48, 0x7, 0xfd, 0xa0, 0x1c, 0xc4, 0x1, 0xff,
    0x20, 0x6, 0x18, 0x0, 0xff, 0xe0, 0x18, 0x6,
    0x81, 0x0, 0xff, 0x8c, 0x3, 0x13, 0x0, 0x7f,
    0xf0, 0x10, 0x3, 0x48, 0x7, 0xff, 0x7, 0x0,
    0x24, 0x40, 0x7, 0xff, 0x5, 0x40, 0x29, 0x0,
    0xff, 0xe0, 0x90, 0x80, 0x1c, 0x80, 0x3f, 0xf8,
    0x28, 0x0, 0x18, 0x0, 0xff, 0xe1, 0x60, 0x2,
    0x0, 0x3f, 0xf8, 0x66, 0x6, 0xc0, 0x1f, 0xfc,
    0x39, 0xcc, 0x0, 0x7f, 0x80,

    /* U+F0EA "" */
    0x0, 0xf8, 0x50, 0xc0, 0x3f, 0xf9, 0x2f, 0xd7,
    0x9c, 0x60, 0x1f, 0xfc, 0x14, 0x99, 0xe8, 0x1,
    0x1, 0xd9, 0x9d, 0x2, 0x1, 0xf5, 0xb3, 0x70,
    0x2f, 0x70, 0x44, 0xcd, 0x9e, 0x0, 0x3f, 0xf9,
    0x86, 0x1, 0xff, 0xc2, 0x5f, 0xe1, 0x0, 0xff,
    0xf1, 0x9c, 0xdd, 0xfd, 0x20, 0x1f, 0xfc, 0x24,
    0xc6, 0x44, 0xf8, 0xc0, 0x3f, 0xf8, 0x52, 0x15,
    0xff, 0xf5, 0x6, 0xb0, 0x7, 0xfc, 0x42, 0xa0,
    0x1f, 0xc7, 0x2c, 0x1, 0xff, 0xcc, 0x96, 0x0,
    0xff, 0xe6, 0x4b, 0x0, 0x7f, 0xf3, 0x24, 0xc0,
    0x3f, 0xf8, 0xf5, 0x77, 0x88, 0x3, 0xff, 0x8a,
    0x82, 0x89, 0xc2, 0x1, 0xff, 0xc5, 0x1e, 0xff,
    0xe5, 0x0, 0xff, 0xff, 0x80, 0x7f, 0xf9, 0x94,
    0x3, 0xff, 0x99, 0x5f, 0xfe, 0x0, 0xff, 0xff,
    0x80, 0x7f, 0xf3, 0x84, 0x40, 0x1f, 0xfc, 0x27,
    0x0, 0xfe, 0xfb, 0xbf, 0xfe, 0xe, 0x90,

    /* U+F0F3 "" */
    0x0, 0xff, 0x9e, 0x88, 0x3, 0xff, 0x92, 0xb0,
    0xbe, 0x1, 0xff, 0xc9, 0x20, 0x1, 0x0, 0x7f,
    0xf1, 0xca, 0x28, 0x2, 0xa5, 0x0, 0xff, 0xe2,
    0x4e, 0xb9, 0x0, 0x4b, 0x5a, 0x80, 0x1f, 0xfc,
    0x1, 0xc6, 0x0, 0xfc, 0x56, 0xa0, 0x1f, 0xf6,
    0x98, 0x7, 0xfd, 0x48, 0x1, 0xfc, 0xc6, 0x1,
    0xff, 0xc1, 0x90, 0xf, 0xe8, 0x0, 0xff, 0xe1,
    0x12, 0x80, 0x7c, 0x62, 0x1, 0xff, 0xc3, 0xe0,
    0xf, 0x94, 0x3, 0xff, 0x88, 0xc0, 0x1f, 0x8,
    0x7, 0xff, 0x10, 0x80, 0x3e, 0xf0, 0xf, 0xfe,
    0x60, 0x80, 0x7f, 0xf1, 0x44, 0x3, 0xca, 0x1,
    0xff, 0xc5, 0x50, 0xf, 0x18, 0x7, 0xff, 0x14,
    0xc0, 0x38, 0xc0, 0x3f, 0xf8, 0xd8, 0x1, 0xd4,
    0x1, 0xff, 0xc6, 0x41, 0x0, 0x8d, 0x80, 0x3f,
    0xf8, 0xf6, 0x0, 0x1d, 0x0, 0xff, 0xe4, 0x2b,
    0x85, 0x8, 0x7, 0xff, 0x26, 0xd, 0x40, 0x3f,
    0xf9, 0x69, 0x0, 0x1f, 0xfc, 0xa2, 0x66, 0x7f,
    0xff, 0xf2, 0xb4, 0x40, 0x3f, 0xfa, 0xa7, 0xff,
    0xeb, 0x0, 0xff, 0xe2, 0x98, 0x80, 0x76, 0x80,
    0x7f, 0xf1, 0xb4, 0x40, 0x26, 0x60, 0x7, 0xff,
    0x18, 0xfe, 0xa3, 0x24, 0x3, 0xfc,

    /* U+F11C "" */
    0x1b, 0xff, 0xff, 0xe7, 0xf2, 0x85, 0x20, 0x7,
    0xff, 0x3c, 0x68, 0xd0, 0x3, 0xff, 0xa4, 0x80,
    0x11, 0x3b, 0x98, 0x0, 0xef, 0x0, 0x19, 0xdc,
    0x20, 0xae, 0xe4, 0x2, 0x77, 0x30, 0x5, 0xe0,
    0x12, 0xc4, 0x24, 0xe, 0x22, 0x30, 0x98, 0x82,
    0x5, 0xc4, 0x3c, 0x2a, 0x21, 0x20, 0x1f, 0xfd,
    0xb1, 0x0, 0x8, 0x8, 0x4, 0x20, 0x20, 0x6,
    0x2, 0x0, 0x10, 0x70, 0x0, 0x40, 0x3e, 0x6f,
    0xf7, 0x1, 0x7f, 0xc4, 0x1d, 0xfe, 0x30, 0xcf,
    0xf4, 0x83, 0x7f, 0xb8, 0x3, 0xfc, 0x46, 0x61,
    0x1, 0x33, 0x10, 0x0, 0xcc, 0x40, 0x3, 0x38,
    0x3, 0xff, 0x83, 0xf9, 0x88, 0x8, 0xcc, 0x70,
    0x3e, 0x63, 0x40, 0xb3, 0x25, 0x0, 0xff, 0xe4,
    0x8, 0x7, 0x8, 0x7, 0xff, 0x34, 0x40, 0x38,
    0x40, 0x3f, 0xf8, 0xbf, 0x98, 0x80, 0x8c, 0xc7,
    0x3, 0xe6, 0x34, 0xb, 0x32, 0x50, 0xf, 0xfe,
    0x1, 0x19, 0x84, 0x4, 0xcc, 0x40, 0x3, 0x31,
    0x0, 0xc, 0xe0, 0xf, 0xf3, 0x7f, 0xb8, 0xb,
    0xff, 0xff, 0x83, 0x20, 0xdf, 0xee, 0x0, 0xf8,
    0x40, 0x2, 0x2, 0x1, 0xff, 0xc1, 0x20, 0xe0,
    0x0, 0x80, 0x7f, 0xf6, 0xd6, 0x21, 0x20, 0x71,
    0x1f, 0xfc, 0x1f, 0xa, 0x88, 0x48, 0x7, 0xc4,
    0xee, 0x60, 0x3, 0xbf, 0xff, 0x5, 0x0, 0x9d,
    0xcc, 0x1, 0x7a, 0x0, 0x7f, 0xf4, 0x92, 0x90,
    0x3, 0xff, 0x9e, 0x34, 0x60,

    /* U+F124 "" */
    0x0, 0xff, 0xe6, 0xab, 0x90, 0x7, 0xff, 0x35,
    0xba, 0xa3, 0x50, 0x3, 0xff, 0x92, 0x31, 0xd2,
    0x20, 0x14, 0x80, 0x7f, 0xf1, 0xca, 0x79, 0xc4,
    0x3, 0x8c, 0x3, 0xff, 0x8a, 0x75, 0xac, 0x1,
    0xf9, 0xc0, 0x3f, 0xf8, 0x69, 0x98, 0x50, 0xf,
    0xf5, 0x80, 0x7f, 0xf0, 0x5b, 0x6c, 0xc0, 0x3f,
    0xe4, 0x20, 0xf, 0xf0, 0xbf, 0x49, 0x0, 0x7f,
    0xf0, 0x60, 0x3, 0xf8, 0x63, 0xa0, 0x40, 0x3f,
    0xf8, 0x46, 0x60, 0xf, 0x8a, 0xb9, 0xc0, 0x3f,
    0xf8, 0xb2, 0x1, 0xe4, 0xbd, 0x50, 0xf, 0xfe,
    0x31, 0x28, 0x6, 0x1b, 0xb2, 0x0, 0x7f, 0xf2,
    0x2c, 0x3, 0xa9, 0x0, 0x3f, 0xf9, 0x22, 0xc0,
    0x1c, 0x80, 0x1f, 0xfc, 0xa9, 0x0, 0xf1, 0x0,
    0x7f, 0xf2, 0x98, 0x3, 0xd2, 0x1, 0xff, 0xc9,
    0x61, 0x0, 0xf2, 0x7e, 0x5d, 0xff, 0x10, 0x7,
    0xf5, 0x80, 0x7f, 0x1a, 0x27, 0xf0, 0x80, 0x7e,
    0x52, 0x0, 0xff, 0xe8, 0x48, 0x7, 0xff, 0x41,
    0xc, 0x3, 0xff, 0xa1, 0xe0, 0x1f, 0xfd, 0x2,
    0x40, 0xf, 0xfe, 0x84, 0x0, 0x7f, 0xf4, 0x5,
    0x40, 0x3f, 0xfa, 0x12, 0x1, 0xff, 0xcb, 0x70,
    0xe, 0x70, 0xf, 0xfe, 0x58, 0x80, 0x67, 0x0,
    0xff, 0xe6, 0x40, 0x4, 0x50, 0x1, 0xff, 0xcc,
    0x2e, 0x89, 0xd0, 0xf, 0xfe, 0x0,

    /* U+F15B "" */
    0x14, 0x4f, 0xf8, 0x40, 0xc0, 0x3d, 0x97, 0x7f,
    0xf2, 0x86, 0x50, 0x7, 0x10, 0x7, 0xff, 0x9,
    0x68, 0x3, 0xff, 0x90, 0xb4, 0x1, 0xff, 0xc8,
    0x5a, 0x0, 0xff, 0xe4, 0x2d, 0x0, 0x7f, 0xf2,
    0x16, 0x40, 0x3f, 0xf8, 0x48, 0x9e, 0x20, 0xf,
    0xfe, 0x6, 0x5, 0xdf, 0xc0, 0x1f, 0xfc, 0x7,
    0xff, 0xfc, 0x1, 0xff, 0xff, 0x0, 0xff, 0xff,
    0x80, 0x7f, 0xff, 0xc0, 0x3f, 0xff, 0xe0, 0x1f,
    0xfe, 0xd2, 0x0, 0xff, 0xe3, 0x96, 0x5d, 0xff,
    0xf1, 0xf0,

    /* U+F1EB "" */
    0x0, 0xff, 0xf1, 0x9b, 0x4d, 0x5e, 0x5d, 0x4b,
    0x18, 0x7, 0xff, 0x20, 0x9f, 0x3f, 0x25, 0x95,
    0xd, 0x15, 0xa7, 0x3f, 0x1c, 0x80, 0x3f, 0xf8,
    0x47, 0x7b, 0x6, 0x1, 0xff, 0xc1, 0x38, 0xdb,
    0x30, 0xf, 0xf1, 0x66, 0x10, 0x3, 0xff, 0x8e,
    0x99, 0x82, 0x0, 0xfa, 0x34, 0xc0, 0x3f, 0x12,
    0x2a, 0xc8, 0x40, 0x1f, 0x8f, 0x60, 0x3, 0xe,
    0xb8, 0x7, 0x8e, 0x33, 0xf6, 0xea, 0xab, 0xdf,
    0xc8, 0x30, 0xf, 0x3e, 0x88, 0x1f, 0x90, 0x7,
    0x36, 0xe3, 0x98, 0x7, 0xf1, 0xbe, 0x6b, 0x0,
    0x71, 0x79, 0xe8, 0x80, 0x64, 0xd9, 0x20, 0xf,
    0xfe, 0x19, 0x4e, 0xa0, 0x6, 0x1d, 0x90, 0xd,
    0x36, 0x40, 0x1f, 0xfc, 0x72, 0xb9, 0x0, 0xd2,
    0xf0, 0x3, 0xac, 0x1, 0xe4, 0x8b, 0xef, 0xfb,
    0x6e, 0x10, 0x3, 0xcd, 0xa4, 0x10, 0xe0, 0xf7,
    0xe4, 0x1, 0x86, 0x3a, 0xdd, 0x4, 0x2, 0x24,
    0x7b, 0xe8, 0x10, 0xc, 0x59, 0x6e, 0x1, 0x20,
    0x80, 0x66, 0xf7, 0x10, 0xf, 0xf8, 0x5f, 0xd8,
    0x3, 0xa, 0x0, 0x7e, 0x1b, 0x91, 0x0, 0xff,
    0xe1, 0x8c, 0xd8, 0x80, 0x7f, 0xf0, 0x29, 0x0,
    0x3c, 0x6f, 0x13, 0xe, 0x60, 0x1e, 0x4d, 0x0,
    0xff, 0xe0, 0x18, 0x6, 0x18, 0xfc, 0x87, 0x67,
    0x8c, 0xf8, 0x10, 0xc, 0x80, 0x1f, 0xfc, 0x8,
    0x70, 0x3, 0x73, 0x80, 0x7f, 0x3f, 0x30, 0x1,
    0xa0, 0x3, 0xff, 0x83, 0x13, 0x72, 0x1, 0xff,
    0xc1, 0x9b, 0x89, 0x0, 0xff, 0xe1, 0xb2, 0x0,
    0x7c, 0x20, 0x1f, 0x23, 0x80, 0x7f, 0xf2, 0xdf,
    0xfb, 0xdc, 0x3, 0xff, 0xa2, 0xf0, 0x1, 0x43,
    0x80, 0x7f, 0xf4, 0x24, 0x3, 0xa4, 0x3, 0xff,
    0xa0, 0x20, 0x1c, 0x20, 0x1f, 0xfd, 0x3, 0x0,
    0xe3, 0x0, 0xff, 0xe8, 0x40, 0x7, 0x40, 0x7,
    0xff, 0x41, 0x31, 0x9, 0x31, 0x0, 0x3f, 0xf8,
    0x40,

    /* U+F240 "" */
    0x2, 0x67, 0x7f, 0xfe, 0x82, 0x0, 0x4b, 0xb3,
    0x11, 0xff, 0xd0, 0xb9, 0x0, 0x40, 0x7, 0xff,
    0x4d, 0x84, 0x8, 0x3, 0xff, 0xa8, 0x40, 0x19,
    0xff, 0xff, 0xf9, 0xb6, 0x1, 0x64, 0x80, 0x7f,
    0xf5, 0x98, 0x3, 0xb3, 0xff, 0xff, 0x94, 0x1,
    0xff, 0xd8, 0x67, 0x30, 0xf, 0xfe, 0xa1, 0x42,
    0x0, 0x7f, 0xff, 0xc0, 0x3f, 0xf9, 0xe7, 0xee,
    0x1, 0xff, 0xd4, 0x70, 0xf, 0xf6, 0x7f, 0xff,
    0xf2, 0x80, 0x3c, 0x20, 0x11, 0xbb, 0xff, 0xf3,
    0x74, 0x2, 0x5d, 0x0, 0x92, 0x23, 0xff, 0x9a,
    0xc0, 0x14, 0x9a, 0x0, 0x7f, 0xf4, 0xc4, 0xc2,
    0x90, 0x3, 0xff, 0xa2, 0x5a, 0x0, 0x1b, 0xff,
    0xff, 0xe8, 0xe9, 0x80, 0x0,

    /* U+F241 "" */
    0x2, 0x67, 0x7f, 0xfe, 0x82, 0x0, 0x4b, 0xb3,
    0x11, 0xff, 0xd0, 0xb9, 0x0, 0x40, 0x7, 0xff,
    0x4d, 0x84, 0x8, 0x3, 0xff, 0xa8, 0x40, 0x19,
    0xff, 0xff, 0xf9, 0xb6, 0x1, 0x64, 0x80, 0x7f,
    0xf5, 0x98, 0x3, 0xbb, 0xff, 0xff, 0x89, 0x20,
    0x1f, 0xfd, 0xd6, 0x73, 0x0, 0xff, 0xea, 0x14,
    0x20, 0x7, 0xff, 0xfc, 0x3, 0xff, 0x9e, 0x7e,
    0xe0, 0x1f, 0xfd, 0x47, 0x0, 0xff, 0x77, 0xff,
    0xff, 0x12, 0x40, 0x3f, 0xe1, 0x0, 0x8d, 0xdf,
    0xff, 0x9b, 0xa0, 0x12, 0xe8, 0x4, 0x91, 0x1f,
    0xfc, 0xd6, 0x0, 0xa4, 0xd0, 0x3, 0xff, 0xa6,
    0x26, 0x14, 0x80, 0x1f, 0xfd, 0x12, 0xd0, 0x0,
    0xdf, 0xff, 0xff, 0x47, 0x4c, 0x0,

    /* U+F242 "" */
    0x2, 0x67, 0x7f, 0xfe, 0x82, 0x0, 0x4b, 0xb3,
    0x11, 0xff, 0xd0, 0xb9, 0x0, 0x40, 0x7, 0xff,
    0x4d, 0x84, 0x8, 0x3, 0xff, 0xa8, 0x40, 0x19,
    0xff, 0xff, 0xf9, 0xb6, 0x1, 0x64, 0x80, 0x7f,
    0xf5, 0x98, 0x3, 0xbb, 0xff, 0xfe, 0x50, 0xf,
    0xff, 0x13, 0x39, 0x80, 0x7f, 0xf5, 0xa, 0x10,
    0x3, 0xff, 0xfe, 0x1, 0xff, 0xcf, 0x3f, 0x70,
    0xf, 0xfe, 0xa3, 0x80, 0x7f, 0xbb, 0xff, 0xfe,
    0x50, 0xf, 0xfe, 0x20, 0x80, 0x46, 0xef, 0xff,
    0xcd, 0xd0, 0x9, 0x74, 0x2, 0x48, 0x8f, 0xfe,
    0x6b, 0x0, 0x52, 0x68, 0x1, 0xff, 0xd3, 0x13,
    0xa, 0x40, 0xf, 0xfe, 0x89, 0x68, 0x0, 0x6f,
    0xff, 0xff, 0xa3, 0xa6, 0x0,

    /* U+F243 "" */
    0x2, 0x67, 0x7f, 0xfe, 0x82, 0x0, 0x4b, 0xb3,
    0x11, 0xff, 0xd0, 0xb9, 0x0, 0x40, 0x7, 0xff,
    0x4d, 0x84, 0x8, 0x3, 0xff, 0xa8, 0x40, 0x19,
    0xff, 0xff, 0xf9, 0xb6, 0x1, 0x64, 0x80, 0x7f,
    0xf5, 0x98, 0x3, 0xbb, 0xff, 0xc2, 0x1, 0xff,
    0xe7, 0x67, 0x30, 0xf, 0xfe, 0xa1, 0x42, 0x0,
    0x7f, 0xff, 0xc0, 0x3f, 0xf9, 0xe7, 0xee, 0x1,
    0xff, 0xd4, 0x70, 0xf, 0xf7, 0x7f, 0xf8, 0x40,
    0x3f, 0xf9, 0x22, 0x1, 0x1b, 0xbf, 0xff, 0x37,
    0x40, 0x25, 0xd0, 0x9, 0x22, 0x3f, 0xf9, 0xac,
    0x1, 0x49, 0xa0, 0x7, 0xff, 0x4c, 0x4c, 0x29,
    0x0, 0x3f, 0xfa, 0x25, 0xa0, 0x1, 0xbf, 0xff,
    0xfe, 0x8e, 0x98, 0x0,

    /* U+F244 "" */
    0x2, 0x67, 0x7f, 0xfe, 0x82, 0x0, 0x4b, 0xb3,
    0x11, 0xff, 0xd0, 0xb9, 0x0, 0x40, 0x7, 0xff,
    0x4d, 0x84, 0x8, 0x3, 0xff, 0xa8, 0x40, 0x19,
    0xff, 0xff, 0xf9, 0xb6, 0x1, 0x64, 0x80, 0x7f,
    0xf5, 0x98, 0x3, 0xff, 0xe8, 0xce, 0x60, 0x1f,
    0xfd, 0x42, 0x84, 0x0, 0xff, 0xff, 0x80, 0x7f,
    0xf3, 0xcf, 0xdc, 0x3, 0xff, 0xa8, 0xe0, 0x1f,
    0xfd, 0xb1, 0x0, 0x8d, 0xdf, 0xff, 0x9b, 0xa0,
    0x12, 0xe8, 0x4, 0x91, 0x1f, 0xfc, 0xd6, 0x0,
    0xa4, 0xd0, 0x3, 0xff, 0xa6, 0x26, 0x14, 0x80,
    0x1f, 0xfd, 0x12, 0xd0, 0x0, 0xdf, 0xff, 0xff,
    0x47, 0x4c, 0x0,

    /* U+F287 "" */
    0x0, 0xff, 0xe4, 0x8, 0x7, 0xff, 0x55, 0x7b,
    0xa4, 0x0, 0xff, 0xe8, 0x9, 0x1d, 0x8, 0xd,
    0x88, 0x7, 0xff, 0x34, 0x6f, 0xb7, 0x0, 0x39,
    0x0, 0x3f, 0xf9, 0xb4, 0x80, 0x64, 0x1, 0xc2,
    0x1, 0xff, 0xcc, 0x54, 0x1e, 0xcd, 0x30, 0x8,
    0xd0, 0x3, 0xff, 0x99, 0x1, 0x62, 0x0, 0xc8,
    0x58, 0xc0, 0xf, 0xfe, 0x21, 0x8, 0x7, 0x90,
    0x88, 0xa0, 0x19, 0xe9, 0xc0, 0x3f, 0xf8, 0x69,
    0xdb, 0xd8, 0x40, 0x1a, 0x2, 0x0, 0x3f, 0xf8,
    0x6e, 0x80, 0x1c, 0x96, 0x20, 0x3, 0xc1, 0x0,
    0x21, 0x8a, 0x80, 0x7f, 0xf0, 0xee, 0xe2, 0x0,
    0xa0, 0x3, 0x86, 0xcd, 0x68, 0x1d, 0x13, 0xff,
    0x88, 0x20, 0x9a, 0xe0, 0x3, 0x0, 0xf2, 0xe5,
    0x8, 0x35, 0xdf, 0xff, 0x10, 0x80, 0x28, 0xd0,
    0x10, 0xf, 0xa, 0x27, 0x18, 0x0, 0xd1, 0x3f,
    0xf8, 0x0, 0x18, 0x60, 0x18, 0x3, 0xd5, 0x77,
    0xd8, 0xc0, 0xd7, 0x7f, 0xfc, 0x3, 0x0, 0x3f,
    0x28, 0x42, 0x0, 0x67, 0x40, 0xf, 0x48, 0xa9,
    0x0, 0x7f, 0xf0, 0x13, 0x60, 0x3, 0x5d, 0x3c,
    0x64, 0x0, 0x7e, 0x90, 0x80, 0xf, 0xfb, 0x2c,
    0x80, 0x3c, 0xb0, 0xe6, 0x1, 0xfc, 0xc0, 0xa4,
    0x0, 0x6d, 0xdd, 0x84, 0x1, 0xff, 0xcb, 0x17,
    0xe, 0x0, 0x79, 0x16, 0x36, 0x0, 0xff, 0xe6,
    0x41, 0x9b, 0xf5, 0xc0, 0x3f, 0xfa, 0x7a, 0xa0,
    0x42, 0x1, 0xff, 0xd3, 0x1a, 0xfe, 0x60, 0xf,
    0xfe, 0xb0, 0x88, 0x3, 0xff, 0xaf, 0x1f, 0xfc,
    0x80, 0x1f, 0x80,

    /* U+F293 "" */
    0x0, 0xfe, 0x13, 0x43, 0x20, 0xf, 0xfe, 0x11,
    0xd7, 0x73, 0x2f, 0x37, 0xed, 0x40, 0x3f, 0xd5,
    0x8a, 0x20, 0x1e, 0x4a, 0xc1, 0x0, 0xf0, 0xe2,
    0x80, 0x63, 0x80, 0xe, 0x3c, 0x10, 0xe, 0xa3,
    0x0, 0xf3, 0xb8, 0x3, 0x8b, 0x40, 0x32, 0xa0,
    0x7, 0xe8, 0x60, 0xe, 0x35, 0x0, 0xa4, 0x3,
    0xfd, 0x2a, 0x1, 0xdc, 0x0, 0x23, 0x0, 0xff,
    0xa9, 0x0, 0x32, 0x8, 0x20, 0x6, 0x50, 0xe,
    0x39, 0xb, 0x30, 0xc, 0x81, 0x80, 0x13, 0x56,
    0x8, 0x6, 0x69, 0xd, 0x20, 0xb, 0x41, 0x80,
    0x24, 0x13, 0xd1, 0x0, 0x8, 0x10, 0x8, 0x80,
    0x22, 0x2, 0x0, 0x8b, 0x4, 0xf5, 0xc0, 0x9,
    0x61, 0x46, 0x1, 0x30, 0x8, 0x6, 0x2c, 0x13,
    0xa0, 0x2b, 0x9, 0x50, 0xc, 0x20, 0x1f, 0x16,
    0x8, 0x80, 0x27, 0x60, 0xe, 0x31, 0x0, 0xf8,
    0xb0, 0x40, 0xb, 0x0, 0x1f, 0xfc, 0x46, 0x10,
    0x3, 0x0, 0x7e, 0x10, 0xf, 0x92, 0xc0, 0x23,
    0xc0, 0xf, 0x18, 0x7, 0xc9, 0x60, 0x40, 0x22,
    0x3b, 0x0, 0xf8, 0x40, 0x32, 0x58, 0x2f, 0x1,
    0xe0, 0xa5, 0x0, 0x61, 0x2, 0x0, 0x92, 0xc1,
    0x68, 0x2, 0x2c, 0x5, 0x80, 0x9, 0x81, 0x40,
    0x39, 0x68, 0x2, 0x10, 0xb0, 0x1a, 0x0, 0xb4,
    0x30, 0x2, 0x4d, 0xa0, 0xe, 0x96, 0x1c, 0x20,
    0x8, 0xc1, 0x0, 0x31, 0x0, 0x42, 0x6, 0xc3,
    0x84, 0x1, 0x90, 0x5, 0x40, 0x3f, 0xc3, 0x84,
    0x1, 0x94, 0x2, 0x80, 0xf, 0xe1, 0xc2, 0x0,
    0xe8, 0x0, 0x8a, 0x40, 0x3e, 0x1d, 0x20, 0xe,
    0x72, 0x0, 0xcd, 0x20, 0x1c, 0x7a, 0x60, 0x1c,
    0x90, 0x1, 0xe6, 0xd5, 0x0, 0x84, 0xc0, 0x30,
    0xc5, 0x80, 0x7e, 0x2a, 0xea, 0x75, 0x44, 0x2b,
    0xdf, 0x38, 0x6,

    /* U+F2ED "" */
    0x0, 0xfc, 0x28, 0x9e, 0x30, 0xf, 0xfe, 0x20,
    0xf5, 0xdf, 0xb2, 0x40, 0x3f, 0x2c, 0x47, 0xb4,
    0x3, 0xf3, 0x44, 0x7c, 0x34, 0xef, 0xe3, 0x0,
    0xfe, 0x77, 0xf9, 0x80, 0x3f, 0xf9, 0x9e, 0x80,
    0x1f, 0xfc, 0xa1, 0xcb, 0xff, 0xff, 0xe5, 0x72,
    0x1, 0x44, 0x7f, 0xf2, 0x18, 0x2, 0x67, 0x7f,
    0xfe, 0x45, 0x0, 0x7f, 0xfb, 0xaa, 0x80, 0x13,
    0x68, 0x4, 0x7c, 0x60, 0x1f, 0xfc, 0x7, 0x70,
    0x5, 0xe4, 0x20, 0x6, 0x16, 0x0, 0xff, 0xff,
    0x80, 0x7f, 0xff, 0xc0, 0x3f, 0xff, 0xe0, 0x1f,
    0xff, 0x27, 0x70, 0x5, 0xe4, 0x20, 0x6, 0x16,
    0x0, 0xf9, 0xc0, 0x35, 0x50, 0x2, 0x6d, 0x0,
    0x8f, 0x8c, 0x2, 0x70, 0x8, 0x40, 0x3f, 0xf9,
    0x2, 0x1, 0x13, 0x0, 0x7f, 0xf1, 0xe0, 0x3,
    0x4d, 0xc3, 0xbf, 0xff, 0xe, 0x78, 0x80, 0x0,

    /* U+F304 "" */
    0x0, 0xff, 0xe5, 0x33, 0x4, 0x3, 0xff, 0x98,
    0x5b, 0x32, 0xf3, 0x0, 0xff, 0xe5, 0x16, 0x10,
    0x0, 0x70, 0xc0, 0x3f, 0xf9, 0x5, 0x82, 0x1,
    0xd8, 0x60, 0x1f, 0xfc, 0x7b, 0x10, 0xf, 0xb0,
    0xc0, 0x3f, 0xf8, 0x82, 0x12, 0x80, 0x1f, 0xbc,
    0x3, 0xff, 0x86, 0x5f, 0x81, 0x68, 0x1, 0xf1,
    0x80, 0x7f, 0xf0, 0x8b, 0x4, 0xf0, 0x2d, 0x0,
    0x3c, 0x80, 0x1f, 0xfc, 0x12, 0xc1, 0x0, 0x1e,
    0x5, 0xa0, 0x6, 0x1a, 0x0, 0xff, 0xe0, 0x16,
    0x8, 0x6, 0x3c, 0xb, 0x40, 0x0, 0xe0, 0x80,
    0x7f, 0xc5, 0x82, 0x1, 0xe3, 0xc0, 0xb4, 0x1c,
    0x20, 0xf, 0xf8, 0xb0, 0x40, 0x3f, 0x1e, 0x5,
    0xf9, 0x0, 0x7f, 0xc5, 0x82, 0x1, 0xfe, 0x3a,
    0x1, 0x0, 0xff, 0x8b, 0x4, 0x3, 0xfe, 0x18,
    0x0, 0xff, 0xe0, 0x16, 0x8, 0x7, 0xfc, 0x38,
    0x40, 0x1f, 0xf1, 0x60, 0x80, 0x7f, 0xc3, 0x84,
    0x1, 0xff, 0x16, 0x8, 0x7, 0xfc, 0x38, 0x40,
    0x1f, 0xf1, 0x60, 0x80, 0x7f, 0xc3, 0x84, 0x1,
    0xff, 0x16, 0x8, 0x7, 0xfc, 0x38, 0x40, 0x1f,
    0xf1, 0x60, 0x80, 0x7f, 0xc3, 0x84, 0x1, 0xff,
    0xe, 0x8, 0x7, 0xfc, 0x38, 0x40, 0x1f, 0xfc,
    0x4, 0x10, 0xf, 0xf8, 0x70, 0x80, 0x3f, 0xf8,
    0x24, 0x1, 0xff, 0xe, 0x10, 0x7, 0xff, 0xb,
    0x80, 0x3f, 0xc3, 0x84, 0x1, 0xff, 0xc3, 0x20,
    0xf, 0xe1, 0xc2, 0x0, 0xff, 0xe2, 0x30, 0x7,
    0xe1, 0xc2, 0x0, 0xff, 0xe2, 0x90, 0x7, 0xc3,
    0x84, 0x1, 0xff, 0xc6, 0x10, 0xe, 0x13, 0xc2,
    0x0, 0xff, 0xe3, 0xec, 0x4d, 0xef, 0xf6, 0x10,
    0x7, 0xff, 0x1c,

    /* U+F55A "" */
    0x0, 0xfe, 0x8e, 0xff, 0xff, 0xe4, 0xea, 0x80,
    0x7f, 0x5b, 0x88, 0x7, 0xff, 0x24, 0xa9, 0x40,
    0x3e, 0xb4, 0x0, 0xff, 0xe6, 0xc0, 0x7, 0xad,
    0x0, 0x3f, 0xf9, 0xc4, 0x1, 0xd6, 0x80, 0x1f,
    0xcc, 0xc0, 0xf, 0x33, 0x0, 0x3f, 0xf8, 0x36,
    0x80, 0x1f, 0xcf, 0x32, 0x70, 0xc, 0xf3, 0x28,
    0x0, 0xff, 0xad, 0x0, 0x3f, 0x86, 0x0, 0x10,
    0xe0, 0x7, 0x80, 0x3, 0x88, 0x7, 0xf5, 0xa0,
    0x7, 0xf8, 0x60, 0x2, 0x87, 0x74, 0x0, 0x50,
    0x20, 0x1f, 0xad, 0x0, 0x3f, 0xf8, 0xf, 0x0,
    0x14, 0x40, 0x2, 0x87, 0x0, 0xfd, 0x48, 0x1,
    0xff, 0xc2, 0x78, 0x0, 0xf4, 0x38, 0x7, 0xf2,
    0x80, 0x7f, 0xf1, 0x1d, 0x0, 0x32, 0x38, 0x7,
    0xf9, 0x40, 0x3f, 0xf8, 0x8e, 0x80, 0x19, 0x1c,
    0x3, 0xfd, 0x48, 0x1, 0xff, 0xc2, 0x78, 0x0,
    0xf4, 0x38, 0x7, 0xfa, 0xd0, 0x3, 0xff, 0x80,
    0xf0, 0x1, 0x44, 0x0, 0x28, 0x70, 0xf, 0xf5,
    0xa0, 0x7, 0xf8, 0x60, 0x2, 0x87, 0x74, 0x0,
    0x50, 0x20, 0x1f, 0xeb, 0x40, 0xf, 0xe1, 0x80,
    0x4, 0x38, 0x1, 0xe0, 0x1, 0x2, 0x1, 0xff,
    0x5a, 0x0, 0x7f, 0x3c, 0xc9, 0xc0, 0x33, 0xcc,
    0x9c, 0x3, 0xff, 0x83, 0x68, 0x1, 0xfc, 0xcc,
    0x0, 0xf3, 0x30, 0x3, 0xff, 0x87, 0x68, 0x1,
    0xff, 0xce, 0x20, 0xf, 0xad, 0x40, 0x3f, 0xf9,
    0xb0, 0x1, 0xfa, 0x9c, 0x40, 0x3f, 0xf9, 0x25,
    0x4a,

    /* U+F7C2 "" */
    0x0, 0xf0, 0xbc, 0x47, 0xf3, 0xa8, 0x7, 0xc3,
    0x90, 0xef, 0xfd, 0x15, 0x82, 0x1, 0x8b, 0x8,
    0x3, 0xff, 0x80, 0x76, 0x1, 0x16, 0x10, 0x7,
    0xff, 0x9, 0x40, 0x5, 0x82, 0x1b, 0xfc, 0xb,
    0xfe, 0x12, 0xff, 0x38, 0x6, 0x2c, 0x10, 0xf,
    0xfe, 0x29, 0xe0, 0x80, 0x7f, 0xf1, 0xb4, 0x40,
    0x3f, 0xf8, 0xe2, 0x1, 0xff, 0xce, 0xdf, 0xe0,
    0x5f, 0xf0, 0x97, 0xf9, 0xc0, 0x3f, 0xff, 0xe0,
    0x1f, 0xff, 0xf0, 0xf, 0xff, 0xf8, 0x7, 0xff,
    0xfc, 0x3, 0xff, 0x8c, 0x20, 0x1f, 0xfc, 0x71,
    0x60, 0xf, 0xfe, 0x3b, 0x42, 0x80, 0x7f, 0xf1,
    0x5a, 0x2, 0xb6, 0xef, 0xff, 0x87, 0xb2, 0x0,

    /* U+F8A2 "" */
    0x0, 0xff, 0xe7, 0xa0, 0x7, 0xff, 0x3e, 0x6c,
    0x40, 0x3f, 0xf9, 0xb2, 0xc0, 0x1f, 0xfc, 0xe9,
    0x60, 0xf, 0xe5, 0xc5, 0x0, 0xff, 0xe1, 0x1b,
    0x0, 0x7f, 0x3d, 0x1c, 0x0, 0x7f, 0xf0, 0x9c,
    0x3, 0xfa, 0x20, 0x1, 0xff, 0xce, 0x97, 0x0,
    0xff, 0xe7, 0x53, 0x0, 0x7f, 0xf3, 0xad, 0x40,
    0x38, 0xbf, 0xff, 0xf8, 0x56, 0x1, 0xd4, 0x80,
    0x1f, 0xfc, 0xf6, 0x0, 0xff, 0xe8, 0x70, 0x80,
    0x7f, 0xf3, 0x44, 0x45, 0xe4, 0x1, 0xc3, 0x55,
    0xff, 0xc6, 0xf0, 0x0, 0xe1, 0x80, 0x63, 0x55,
    0xff, 0xe3, 0x8, 0x4, 0x38, 0x60, 0x1f, 0xfd,
    0xc, 0x40, 0xf, 0xfe, 0x85, 0xa8, 0x20, 0x7,
    0xff, 0x3a, 0xba, 0x40, 0x3f, 0xf9, 0x0
};


/*---------------------
 *  GLYPH DESCRIPTION
 *--------------------*/

static const lv_font_fmt_txt_glyph_dsc_t glyph_dsc[] = {
    {.bitmap_index = 0, .adv_w = 0, .box_w = 0, .box_h = 0, .ofs_x = 0, .ofs_y = 0} /* id = 0 reserved */,
    {.bitmap_index = 0, .adv_w = 121, .box_w = 0, .box_h = 0, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 0, .adv_w = 120, .box_w = 5, .box_h = 20, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 41, .adv_w = 175, .box_w = 9, .box_h = 8, .ofs_x = 1, .ofs_y = 12},
    {.bitmap_index = 66, .adv_w = 315, .box_w = 19, .box_h = 20, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 196, .adv_w = 278, .box_w = 16, .box_h = 26, .ofs_x = 1, .ofs_y = -3},
    {.bitmap_index = 341, .adv_w = 378, .box_w = 22, .box_h = 20, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 524, .adv_w = 307, .box_w = 18, .box_h = 21, .ofs_x = 1, .ofs_y = -1},
    {.bitmap_index = 685, .adv_w = 94, .box_w = 4, .box_h = 8, .ofs_x = 1, .ofs_y = 12},
    {.bitmap_index = 697, .adv_w = 151, .box_w = 7, .box_h = 26, .ofs_x = 2, .ofs_y = -5},
    {.bitmap_index = 776, .adv_w = 151, .box_w = 7, .box_h = 26, .ofs_x = 0, .ofs_y = -5},
    {.bitmap_index = 855, .adv_w = 179, .box_w = 11, .box_h = 11, .ofs_x = 0, .ofs_y = 10},
    {.bitmap_index = 907, .adv_w = 261, .box_w = 14, .box_h = 13, .ofs_x = 1, .ofs_y = 4},
    {.bitmap_index = 940, .adv_w = 102, .box_w = 5, .box_h = 8, .ofs_x = 1, .ofs_y = -4},
    {.bitmap_index = 961, .adv_w = 172, .box_w = 9, .box_h = 3, .ofs_x = 1, .ofs_y = 7},
    {.bitmap_index = 969, .adv_w = 102, .box_w = 5, .box_h = 4, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 980, .adv_w = 158, .box_w = 12, .box_h = 27, .ofs_x = -1, .ofs_y = -3},
    {.bitmap_index = 1083, .adv_w = 299, .box_w = 17, .box_h = 20, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1221, .adv_w = 166, .box_w = 8, .box_h = 20, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1238, .adv_w = 257, .box_w = 16, .box_h = 20, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1354, .adv_w = 256, .box_w = 15, .box_h = 20, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1462, .adv_w = 300, .box_w = 18, .box_h = 20, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1561, .adv_w = 257, .box_w = 16, .box_h = 20, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1675, .adv_w = 276, .box_w = 16, .box_h = 20, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1812, .adv_w = 268, .box_w = 16, .box_h = 20, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1910, .adv_w = 289, .box_w = 16, .box_h = 20, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 2052, .adv_w = 276, .box_w = 16, .box_h = 20, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2187, .adv_w = 102, .box_w = 5, .box_h = 15, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 2213, .adv_w = 102, .box_w = 5, .box_h = 19, .ofs_x = 1, .ofs_y = -4},
    {.bitmap_index = 2249, .adv_w = 261, .box_w = 14, .box_h = 13, .ofs_x = 1, .ofs_y = 3},
    {.bitmap_index = 2320, .adv_w = 261, .box_w = 14, .box_h = 9, .ofs_x = 1, .ofs_y = 5},
    {.bitmap_index = 2350, .adv_w = 261, .box_w = 14, .box_h = 13, .ofs_x = 1, .ofs_y = 3},
    {.bitmap_index = 2421, .adv_w = 257, .box_w = 15, .box_h = 20, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2522, .adv_w = 463, .box_w = 27, .box_h = 25, .ofs_x = 1, .ofs_y = -5},
    {.bitmap_index = 2798, .adv_w = 328, .box_w = 22, .box_h = 20, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 2946, .adv_w = 339, .box_w = 18, .box_h = 20, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 3063, .adv_w = 324, .box_w = 19, .box_h = 20, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 3198, .adv_w = 370, .box_w = 20, .box_h = 20, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 3315, .adv_w = 300, .box_w = 16, .box_h = 20, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 3363, .adv_w = 284, .box_w = 15, .box_h = 20, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 3402, .adv_w = 346, .box_w = 19, .box_h = 20, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 3542, .adv_w = 364, .box_w = 18, .box_h = 20, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 3574, .adv_w = 139, .box_w = 4, .box_h = 20, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 3580, .adv_w = 230, .box_w = 13, .box_h = 20, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 3634, .adv_w = 322, .box_w = 19, .box_h = 20, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 3760, .adv_w = 266, .box_w = 15, .box_h = 20, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 3786, .adv_w = 428, .box_w = 22, .box_h = 20, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 3916, .adv_w = 364, .box_w = 18, .box_h = 20, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 4019, .adv_w = 376, .box_w = 22, .box_h = 20, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 4186, .adv_w = 323, .box_w = 17, .box_h = 20, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 4272, .adv_w = 376, .box_w = 23, .box_h = 24, .ofs_x = 1, .ofs_y = -4},
    {.bitmap_index = 4483, .adv_w = 326, .box_w = 18, .box_h = 20, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 4592, .adv_w = 278, .box_w = 16, .box_h = 20, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 4726, .adv_w = 263, .box_w = 17, .box_h = 20, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 4758, .adv_w = 354, .box_w = 18, .box_h = 20, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 4834, .adv_w = 319, .box_w = 21, .box_h = 20, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 4983, .adv_w = 504, .box_w = 31, .box_h = 20, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 5219, .adv_w = 302, .box_w = 19, .box_h = 20, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 5365, .adv_w = 290, .box_w = 20, .box_h = 20, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 5470, .adv_w = 294, .box_w = 17, .box_h = 20, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 5568, .adv_w = 149, .box_w = 7, .box_h = 26, .ofs_x = 2, .ofs_y = -5},
    {.bitmap_index = 5592, .adv_w = 158, .box_w = 13, .box_h = 27, .ofs_x = -2, .ofs_y = -3},
    {.bitmap_index = 5698, .adv_w = 149, .box_w = 7, .box_h = 26, .ofs_x = 0, .ofs_y = -5},
    {.bitmap_index = 5721, .adv_w = 261, .box_w = 13, .box_h = 12, .ofs_x = 2, .ofs_y = 4},
    {.bitmap_index = 5784, .adv_w = 224, .box_w = 14, .box_h = 2, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 5791, .adv_w = 269, .box_w = 8, .box_h = 4, .ofs_x = 3, .ofs_y = 17},
    {.bitmap_index = 5806, .adv_w = 268, .box_w = 14, .box_h = 15, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 5899, .adv_w = 306, .box_w = 16, .box_h = 21, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 6003, .adv_w = 256, .box_w = 15, .box_h = 15, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 6098, .adv_w = 306, .box_w = 16, .box_h = 21, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 6205, .adv_w = 274, .box_w = 15, .box_h = 15, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 6299, .adv_w = 158, .box_w = 11, .box_h = 21, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 6351, .adv_w = 309, .box_w = 16, .box_h = 20, .ofs_x = 1, .ofs_y = -5},
    {.bitmap_index = 6491, .adv_w = 305, .box_w = 15, .box_h = 21, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 6553, .adv_w = 125, .box_w = 4, .box_h = 21, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 6570, .adv_w = 127, .box_w = 9, .box_h = 26, .ofs_x = -3, .ofs_y = -5},
    {.bitmap_index = 6616, .adv_w = 276, .box_w = 16, .box_h = 21, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 6704, .adv_w = 125, .box_w = 4, .box_h = 21, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 6711, .adv_w = 474, .box_w = 26, .box_h = 15, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 6806, .adv_w = 305, .box_w = 15, .box_h = 15, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 6862, .adv_w = 284, .box_w = 16, .box_h = 15, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 6966, .adv_w = 306, .box_w = 16, .box_h = 20, .ofs_x = 2, .ofs_y = -5},
    {.bitmap_index = 7072, .adv_w = 306, .box_w = 16, .box_h = 20, .ofs_x = 1, .ofs_y = -5},
    {.bitmap_index = 7180, .adv_w = 184, .box_w = 9, .box_h = 15, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 7210, .adv_w = 224, .box_w = 14, .box_h = 15, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 7305, .adv_w = 185, .box_w = 11, .box_h = 19, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 7357, .adv_w = 303, .box_w = 15, .box_h = 15, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 7408, .adv_w = 250, .box_w = 17, .box_h = 15, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 7506, .adv_w = 403, .box_w = 25, .box_h = 15, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 7661, .adv_w = 247, .box_w = 16, .box_h = 15, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 7759, .adv_w = 250, .box_w = 17, .box_h = 20, .ofs_x = -1, .ofs_y = -5},
    {.bitmap_index = 7887, .adv_w = 233, .box_w = 13, .box_h = 15, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 7954, .adv_w = 157, .box_w = 9, .box_h = 26, .ofs_x = 1, .ofs_y = -5},
    {.bitmap_index = 8014, .adv_w = 134, .box_w = 4, .box_h = 26, .ofs_x = 2, .ofs_y = -5},
    {.bitmap_index = 8023, .adv_w = 157, .box_w = 9, .box_h = 26, .ofs_x = 0, .ofs_y = -5},
    {.bitmap_index = 8077, .adv_w = 261, .box_w = 14, .box_h = 5, .ofs_x = 1, .ofs_y = 8},
    {.bitmap_index = 8112, .adv_w = 188, .box_w = 10, .box_h = 10, .ofs_x = 1, .ofs_y = 11},
    {.bitmap_index = 8158, .adv_w = 141, .box_w = 6, .box_h = 6, .ofs_x = 1, .ofs_y = 5},
    {.bitmap_index = 8176, .adv_w = 448, .box_w = 28, .box_h = 29, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 8344, .adv_w = 448, .box_w = 28, .box_h = 21, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 8523, .adv_w = 448, .box_w = 28, .box_h = 25, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 8634, .adv_w = 448, .box_w = 28, .box_h = 21, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 8793, .adv_w = 308, .box_w = 20, .box_h = 21, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 8946, .adv_w = 448, .box_w = 28, .box_h = 29, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 9210, .adv_w = 448, .box_w = 27, .box_h = 29, .ofs_x = 1, .ofs_y = -4},
    {.bitmap_index = 9431, .adv_w = 504, .box_w = 32, .box_h = 25, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 9642, .adv_w = 448, .box_w = 28, .box_h = 29, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 9805, .adv_w = 504, .box_w = 32, .box_h = 21, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 9961, .adv_w = 448, .box_w = 28, .box_h = 29, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 10218, .adv_w = 224, .box_w = 14, .box_h = 23, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 10281, .adv_w = 336, .box_w = 21, .box_h = 23, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 10395, .adv_w = 504, .box_w = 32, .box_h = 27, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 10670, .adv_w = 448, .box_w = 28, .box_h = 21, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 10804, .adv_w = 308, .box_w = 20, .box_h = 29, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 10984, .adv_w = 392, .box_w = 18, .box_h = 26, .ofs_x = 3, .ofs_y = -3},
    {.bitmap_index = 11094, .adv_w = 392, .box_w = 25, .box_h = 29, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 11242, .adv_w = 392, .box_w = 25, .box_h = 25, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 11325, .adv_w = 392, .box_w = 25, .box_h = 25, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 11379, .adv_w = 392, .box_w = 18, .box_h = 26, .ofs_x = 3, .ofs_y = -3},
    {.bitmap_index = 11491, .adv_w = 392, .box_w = 26, .box_h = 25, .ofs_x = -1, .ofs_y = -2},
    {.bitmap_index = 11628, .adv_w = 280, .box_w = 16, .box_h = 25, .ofs_x = 1, .ofs_y = -2},
    {.bitmap_index = 11759, .adv_w = 280, .box_w = 16, .box_h = 25, .ofs_x = 1, .ofs_y = -2},
    {.bitmap_index = 11890, .adv_w = 392, .box_w = 25, .box_h = 25, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 11979, .adv_w = 392, .box_w = 25, .box_h = 7, .ofs_x = 0, .ofs_y = 7},
    {.bitmap_index = 12013, .adv_w = 504, .box_w = 32, .box_h = 21, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 12263, .adv_w = 560, .box_w = 35, .box_h = 29, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 12598, .adv_w = 504, .box_w = 33, .box_h = 29, .ofs_x = -1, .ofs_y = -4},
    {.bitmap_index = 12836, .adv_w = 448, .box_w = 28, .box_h = 25, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 13065, .adv_w = 392, .box_w = 25, .box_h = 15, .ofs_x = 0, .ofs_y = 3},
    {.bitmap_index = 13188, .adv_w = 392, .box_w = 25, .box_h = 15, .ofs_x = 0, .ofs_y = 3},
    {.bitmap_index = 13314, .adv_w = 560, .box_w = 35, .box_h = 22, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 13498, .adv_w = 448, .box_w = 28, .box_h = 21, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 13564, .adv_w = 448, .box_w = 28, .box_h = 29, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 13734, .adv_w = 448, .box_w = 29, .box_h = 29, .ofs_x = -1, .ofs_y = -4},
    {.bitmap_index = 13945, .adv_w = 392, .box_w = 25, .box_h = 25, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 14184, .adv_w = 392, .box_w = 25, .box_h = 29, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 14291, .adv_w = 392, .box_w = 25, .box_h = 25, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 14423, .adv_w = 392, .box_w = 25, .box_h = 23, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 14501, .adv_w = 448, .box_w = 28, .box_h = 21, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 14634, .adv_w = 280, .box_w = 19, .box_h = 29, .ofs_x = -1, .ofs_y = -4},
    {.bitmap_index = 14791, .adv_w = 392, .box_w = 25, .box_h = 29, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 14918, .adv_w = 392, .box_w = 25, .box_h = 29, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 15084, .adv_w = 504, .box_w = 32, .box_h = 21, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 15281, .adv_w = 448, .box_w = 30, .box_h = 29, .ofs_x = -1, .ofs_y = -4},
    {.bitmap_index = 15463, .adv_w = 336, .box_w = 21, .box_h = 29, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 15537, .adv_w = 560, .box_w = 35, .box_h = 26, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 15794, .adv_w = 560, .box_w = 35, .box_h = 19, .ofs_x = 0, .ofs_y = 1},
    {.bitmap_index = 15895, .adv_w = 560, .box_w = 35, .box_h = 19, .ofs_x = 0, .ofs_y = 1},
    {.bitmap_index = 15997, .adv_w = 560, .box_w = 35, .box_h = 19, .ofs_x = 0, .ofs_y = 1},
    {.bitmap_index = 16098, .adv_w = 560, .box_w = 35, .box_h = 19, .ofs_x = 0, .ofs_y = 1},
    {.bitmap_index = 16198, .adv_w = 560, .box_w = 35, .box_h = 19, .ofs_x = 0, .ofs_y = 1},
    {.bitmap_index = 16289, .adv_w = 560, .box_w = 36, .box_h = 23, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 16500, .adv_w = 392, .box_w = 22, .box_h = 29, .ofs_x = 1, .ofs_y = -4},
    {.bitmap_index = 16743, .adv_w = 392, .box_w = 25, .box_h = 29, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 16863, .adv_w = 448, .box_w = 29, .box_h = 29, .ofs_x = -1, .ofs_y = -4},
    {.bitmap_index = 17090, .adv_w = 560, .box_w = 35, .box_h = 21, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 17291, .adv_w = 336, .box_w = 21, .box_h = 29, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 17387, .adv_w = 451, .box_w = 29, .box_h = 19, .ofs_x = 0, .ofs_y = 1}
};

/*---------------------
 *  CHARACTER MAPPING
 *--------------------*/

static const uint16_t unicode_list_1[] = {
    0x0, 0x1f72, 0xef51, 0xef58, 0xef5b, 0xef5c, 0xef5d, 0xef61,
    0xef63, 0xef65, 0xef69, 0xef6c, 0xef71, 0xef76, 0xef77, 0xef78,
    0xef8e, 0xef93, 0xef98, 0xef9b, 0xef9c, 0xef9d, 0xefa1, 0xefa2,
    0xefa3, 0xefa4, 0xefb7, 0xefb8, 0xefbe, 0xefc0, 0xefc1, 0xefc4,
    0xefc7, 0xefc8, 0xefc9, 0xefcb, 0xefe3, 0xefe5, 0xf014, 0xf015,
    0xf017, 0xf019, 0xf030, 0xf037, 0xf03a, 0xf043, 0xf06c, 0xf074,
    0xf0ab, 0xf13b, 0xf190, 0xf191, 0xf192, 0xf193, 0xf194, 0xf1d7,
    0xf1e3, 0xf23d, 0xf254, 0xf4aa, 0xf712, 0xf7f2
};

/*Collect the unicode lists and glyph_id offsets*/
static const lv_font_fmt_txt_cmap_t cmaps[] = {
    {
        .range_start = 32, .range_length = 95, .glyph_id_start = 1,
        .unicode_list = NULL, .glyph_id_ofs_list = NULL, .list_length = 0, .type = LV_FONT_FMT_TXT_CMAP_FORMAT0_TINY
    },
    {
        .range_start = 176, .range_length = 63475, .glyph_id_start = 96,
        .unicode_list = unicode_list_1, .glyph_id_ofs_list = NULL, .list_length = 62, .type = LV_FONT_FMT_TXT_CMAP_SPARSE_TINY
    }
};

/*-----------------
 *    KERNING
 *----------------*/


/*Map glyph_ids to kern left classes*/
static const uint8_t kern_left_class_mapping[] = {
    0, 0, 1, 2, 0, 3, 4, 5,
    2, 6, 7, 8, 9, 10, 9, 10,
    11, 12, 0, 13, 14, 15, 16, 17,
    18, 19, 12, 20, 20, 0, 0, 0,
    21, 22, 23, 24, 25, 22, 26, 27,
    28, 29, 29, 30, 31, 32, 29, 29,
    22, 33, 34, 35, 3, 36, 30, 37,
    37, 38, 39, 40, 41, 42, 43, 0,
    44, 0, 45, 46, 47, 48, 49, 50,
    51, 45, 52, 52, 53, 48, 45, 45,
    46, 46, 54, 55, 56, 57, 51, 58,
    58, 59, 58, 60, 41, 0, 0, 9,
    61, 9, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0
};

/*Map glyph_ids to kern right classes*/
static const uint8_t kern_right_class_mapping[] = {
    0, 0, 1, 2, 0, 3, 4, 5,
    2, 6, 7, 8, 9, 10, 9, 10,
    11, 12, 13, 14, 15, 16, 17, 12,
    18, 19, 20, 21, 21, 0, 0, 0,
    22, 23, 24, 25, 23, 25, 25, 25,
    23, 25, 25, 26, 25, 25, 25, 25,
    23, 25, 23, 25, 3, 27, 28, 29,
    29, 30, 31, 32, 33, 34, 35, 0,
    36, 0, 37, 38, 39, 39, 39, 0,
    39, 38, 40, 41, 38, 38, 42, 42,
    39, 42, 39, 42, 43, 44, 45, 46,
    46, 47, 46, 48, 0, 0, 35, 9,
    49, 9, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0
};

/*Kern values between classes*/
static const int8_t kern_class_values[] = {
    0, 1, 0, 0, 0, 0, 0, 0,
    0, 1, 0, 0, 4, 0, 0, 0,
    0, 3, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 1, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 1, 20, 0, 12, -10, 0, 0,
    0, 0, -25, -27, 3, 21, 10, 8,
    -18, 3, 22, 1, 19, 4, 14, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 27, 4, -3, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 9, 0, -13, 0, 0, 0, 0,
    0, -9, 8, 9, 0, 0, -4, 0,
    -3, 4, 0, -4, 0, -4, -2, -9,
    0, 0, 0, 0, -4, 0, 0, -6,
    -7, 0, 0, -4, 0, -9, 0, 0,
    0, 0, 0, 0, 0, 0, 0, -4,
    -4, 0, -7, 0, -12, 0, -54, 0,
    0, -9, 0, 9, 13, 0, 0, -9,
    4, 4, 15, 9, -8, 9, 0, 0,
    -26, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -17, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, -12, -5, -22, 0, -18,
    -3, 0, 0, 0, 0, 1, 17, 0,
    -13, -4, -1, 1, 0, -8, 0, 0,
    -3, -33, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, -36, -4, 17,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -18, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 15,
    0, 4, 0, 0, -9, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 17, 4,
    1, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    -17, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 3,
    9, 4, 13, -4, 0, 0, 9, -4,
    -15, -61, 3, 12, 9, 1, -6, 0,
    16, 0, 14, 0, 14, 0, -42, 0,
    -5, 13, 0, 15, -4, 9, 4, 0,
    0, 1, -4, 0, 0, -8, 36, 0,
    36, 0, 13, 0, 19, 6, 8, 13,
    0, 0, 0, -17, 0, 0, 0, 0,
    1, -3, 0, 3, -8, -6, -9, 3,
    0, -4, 0, 0, 0, -18, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -29, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 1, -25, 0, -28, 0, 0, 0,
    0, -3, 0, 44, -5, -6, 4, 4,
    -4, 0, -6, 4, 0, 0, -24, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, -43, 0, 4, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -28, 0, 27, 0, 0, -17, 0,
    15, 0, -30, -43, -30, -9, 13, 0,
    0, -30, 0, 5, -10, 0, -7, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 12, 13, -55, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 21, 0, 3, 0, 0, 0,
    0, 0, 3, 3, -5, -9, 0, -1,
    -1, -4, 0, 0, -3, 0, 0, 0,
    -9, 0, -4, 0, -10, -9, 0, -11,
    -15, -15, -9, 0, -9, 0, -9, 0,
    0, 0, 0, -4, 0, 0, 4, 0,
    3, -4, 0, 1, 0, 0, 0, 4,
    -3, 0, 0, 0, -3, 4, 4, -1,
    0, 0, 0, -9, 0, -1, 0, 0,
    0, 0, 0, 1, 0, 6, -3, 0,
    -5, 0, -8, 0, 0, -3, 0, 13,
    0, 0, -4, 0, 0, 0, 0, 0,
    -1, 1, -3, -3, 0, 0, -4, 0,
    -4, 0, 0, 0, 0, 0, 0, 0,
    0, 0, -2, -2, 0, -4, -5, 0,
    0, 0, 0, 0, 1, 0, 0, -3,
    0, -4, -4, -4, 0, 0, 0, 0,
    0, 0, 0, 0, 0, -3, 0, 0,
    0, 0, -3, -6, 0, -7, 0, -13,
    -3, -13, 9, 0, 0, -9, 4, 9,
    12, 0, -11, -1, -5, 0, -1, -21,
    4, -3, 3, -24, 4, 0, 0, 1,
    -23, 0, -24, -4, -39, -3, 0, -22,
    0, 9, 13, 0, 6, 0, 0, 0,
    0, 1, 0, -8, -6, 0, -13, 0,
    0, 0, -4, 0, 0, 0, -4, 0,
    0, 0, 0, 0, -2, -2, 0, -2,
    -6, 0, 0, 0, 0, 0, 0, 0,
    -4, -4, 0, -3, -5, -4, 0, 0,
    -4, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -4, -4, 0, -5,
    0, -3, 0, -9, 4, 0, 0, -5,
    2, 4, 4, 0, 0, 0, 0, 0,
    0, -3, 0, 0, 0, 0, 0, 3,
    0, 0, -4, 0, -4, -3, -5, 0,
    0, 0, 0, 0, 0, 0, 4, 0,
    -4, 0, 0, 0, 0, -5, -7, 0,
    -9, 0, 13, -3, 1, -14, 0, 0,
    12, -22, -23, -19, -9, 4, 0, -4,
    -29, -8, 0, -8, 0, -9, 7, -8,
    -29, 0, -12, 0, 0, 2, -1, 4,
    -3, 0, 4, 0, -13, -17, 0, -22,
    -11, -9, -11, -13, -5, -12, -1, -9,
    -12, 3, 0, 1, 0, -4, 0, 0,
    0, 3, 0, 4, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, -4,
    0, -2, 0, -1, -4, 0, -8, -10,
    -10, -1, 0, -13, 0, 0, 0, 0,
    0, 0, -4, 0, 0, 0, 0, 2,
    -3, 0, 0, 0, 4, 0, 0, 0,
    0, 0, 0, 0, 0, 22, 0, 0,
    0, 0, 0, 0, 3, 0, 0, 0,
    -4, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -8, 0, 4, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -3, 0, 0, 0,
    -9, 0, 0, 0, 0, -22, -13, 0,
    0, 0, -7, -22, 0, 0, -4, 4,
    0, -12, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -7, 0, 0, -9,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 4, 0, -8, 0,
    0, 0, 0, 5, 0, 3, -9, -9,
    0, -4, -4, -5, 0, 0, 0, 0,
    0, 0, -13, 0, -4, 0, -7, -4,
    0, -10, -11, -13, -4, 0, -9, 0,
    -13, 0, 0, 0, 0, 36, 0, 0,
    2, 0, 0, -6, 0, 4, 0, -19,
    0, 0, 0, 0, 0, -42, -8, 15,
    13, -4, -19, 0, 4, -7, 0, -22,
    -2, -6, 4, -31, -4, 6, 0, 7,
    -16, -7, -17, -15, -19, 0, 0, -27,
    0, 26, 0, 0, -2, 0, 0, 0,
    -2, -2, -4, -12, -15, -1, -42, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    1, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -4, 0, -2, -4, -7, 0, 0,
    -9, 0, -4, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, -1, 0, -9, 0, 0, 9,
    -1, 6, 0, -10, 4, -3, -1, -12,
    -4, 0, -6, -4, -3, 0, -7, -8,
    0, 0, -4, -1, -3, -8, -5, 0,
    0, -4, 0, 4, -3, 0, -10, 0,
    0, 0, -9, 0, -8, 0, -8, -8,
    4, 0, 0, 0, 0, 0, 0, 0,
    0, -9, 4, 0, -6, 0, -3, -5,
    -14, -3, -3, -3, -1, -3, -5, -1,
    0, 0, 0, 0, 0, -4, -4, -4,
    0, 0, 0, 0, 5, -3, 0, -3,
    0, 0, 0, -3, -5, -3, -4, -5,
    -4, 0, 4, 18, -1, 0, -12, 0,
    -3, 9, 0, -4, -19, -6, 7, 0,
    0, -21, -8, 4, -8, 3, 0, -3,
    -4, -14, 0, -7, 2, 0, 0, -8,
    0, 0, 0, 4, 4, -9, -9, 0,
    -8, -4, -7, -4, -4, 0, -8, 2,
    -9, -8, 13, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 4, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -8, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    -3, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, -4, -4,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -7, 0, 0, -6,
    0, 0, -4, -4, 0, 0, 0, 0,
    -4, 0, 0, 0, 0, -2, 0, 0,
    0, 0, 0, -3, 0, 0, 0, 0,
    -7, 0, -9, 0, 0, 0, -15, 0,
    3, -10, 9, 1, -3, -21, 0, 0,
    -10, -4, 0, -18, -11, -13, 0, 0,
    -19, -4, -18, -17, -22, 0, -12, 0,
    4, 30, -6, 0, -10, -4, -1, -4,
    -8, -12, -8, -17, -18, -10, -4, 0,
    0, -3, 0, 1, 0, 0, -31, -4,
    13, 10, -10, -17, 0, 1, -14, 0,
    -22, -3, -4, 9, -41, -6, 1, 0,
    0, -29, -5, -23, -4, -33, 0, 0,
    -31, 0, 26, 1, 0, -3, 0, 0,
    0, 0, -2, -3, -17, -3, 0, -29,
    0, 0, 0, 0, -14, 0, -4, 0,
    -1, -13, -21, 0, 0, -2, -7, -13,
    -4, 0, -3, 0, 0, 0, 0, -20,
    -4, -15, -14, -4, -8, -11, -4, -8,
    0, -9, -4, -15, -7, 0, -5, -9,
    -4, -9, 0, 2, 0, -3, -15, 0,
    9, 0, -8, 0, 0, 0, 0, 5,
    0, 3, -9, 18, 0, -4, -4, -5,
    0, 0, 0, 0, 0, 0, -13, 0,
    -4, 0, -7, -4, 0, -10, -11, -13,
    -4, 0, -9, 4, 18, 0, 0, 0,
    0, 36, 0, 0, 2, 0, 0, -6,
    0, 4, 0, 0, 0, 0, 0, 0,
    0, 0, -1, 0, 0, 0, 0, 0,
    -3, -9, 0, 0, 0, 0, 0, -2,
    0, 0, 0, -4, -4, 0, 0, -9,
    -4, 0, 0, -9, 0, 8, -2, 0,
    0, 0, 0, 0, 0, 2, 0, 0,
    0, 0, 7, 9, 4, -4, 0, -14,
    -7, 0, 13, -15, -14, -9, -9, 18,
    8, 4, -39, -3, 9, -4, 0, -4,
    5, -4, -16, 0, -4, 4, -6, -4,
    -13, -4, 0, 0, 13, 9, 0, -13,
    0, -25, -6, 13, -6, -17, 1, -6,
    -15, -15, -4, 18, 4, 0, -7, 0,
    -12, 0, 4, 15, -10, -17, -18, -11,
    13, 0, 1, -33, -4, 4, -8, -3,
    -10, 0, -10, -17, -7, -7, -4, 0,
    0, -10, -9, -4, 0, 13, 10, -4,
    -25, 0, -25, -6, 0, -16, -26, -1,
    -14, -8, -15, -13, 12, 0, 0, -6,
    0, -9, -4, 0, -4, -8, 0, 8,
    -15, 4, 0, 0, -24, 0, -4, -10,
    -8, -3, -13, -11, -15, -10, 0, -13,
    -4, -10, -9, -13, -4, 0, 0, 1,
    21, -8, 0, -13, -4, 0, -4, -9,
    -10, -12, -13, -17, -6, -9, 9, 0,
    -7, 0, -22, -5, 3, 9, -14, -17,
    -9, -15, 15, -4, 2, -42, -8, 9,
    -10, -8, -17, 0, -13, -19, -5, -4,
    -4, -4, -9, -13, -1, 0, 0, 13,
    13, -3, -29, 0, -27, -10, 11, -17,
    -30, -9, -16, -19, -22, -15, 9, 0,
    0, 0, 0, -5, 0, 0, 4, -5,
    9, 3, -9, 9, 0, 0, -14, -1,
    0, -1, 0, 1, 1, -4, 0, 0,
    0, 0, 0, 0, -4, 0, 0, 0,
    0, 4, 13, 1, 0, -5, 0, 0,
    0, 0, -3, -3, -5, 0, 0, 0,
    1, 4, 0, 0, 0, 0, 4, 0,
    -4, 0, 17, 0, 8, 1, 1, -6,
    0, 9, 0, 0, 0, 4, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 13, 0, 13, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, -27, 0, -4, 8, 0, 13,
    0, 0, 44, 5, -9, -9, 4, 4,
    -3, 1, -22, 0, 0, 22, -27, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, -30, 17, 63, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -27, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -7, 0, 0, -9,
    -4, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -3, 0, -12, 0,
    0, 1, 0, 0, 4, 58, -9, -4,
    14, 12, -12, 4, 0, 0, 4, 4,
    -6, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -58, 13, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, -13,
    0, 0, 0, -12, 0, 0, 0, 0,
    -10, -2, 0, 0, 0, -10, 0, -5,
    0, -21, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, -30, 0, 0,
    0, 0, 1, 0, 0, 0, 0, 0,
    0, -4, 0, 0, -9, 0, -7, 0,
    -12, 0, 0, 0, -8, 4, -5, 0,
    0, -12, -4, -10, 0, 0, -12, 0,
    -4, 0, -21, 0, -5, 0, 0, -36,
    -9, -18, -5, -16, 0, 0, -30, 0,
    -12, -2, 0, 0, 0, 0, 0, 0,
    0, 0, -7, -8, -4, -8, 0, 0,
    0, 0, -10, 0, -10, 6, -5, 9,
    0, -3, -10, -3, -8, -9, 0, -5,
    -2, -3, 3, -12, -1, 0, 0, 0,
    -39, -4, -6, 0, -10, 0, -3, -21,
    -4, 0, 0, -3, -4, 0, 0, 0,
    0, 3, 0, -3, -8, -3, 8, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 6, 0, 0, 0, 0, 0,
    0, -10, 0, -3, 0, 0, 0, -9,
    4, 0, 0, 0, -12, -4, -9, 0,
    0, -13, 0, -4, 0, -21, 0, 0,
    0, 0, -43, 0, -9, -17, -22, 0,
    0, -30, 0, -3, -7, 0, 0, 0,
    0, 0, 0, 0, 0, -4, -7, -2,
    -7, 1, 0, 0, 8, -6, 0, 14,
    22, -4, -4, -13, 5, 22, 8, 10,
    -12, 5, 19, 5, 13, 10, 12, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 28, 21, -8, -4, 0, -4,
    36, 19, 36, 0, 0, 0, 4, 0,
    0, 17, 0, 0, -7, 0, 0, 0,
    0, 0, 0, 0, 0, 0, -3, 0,
    0, 0, 0, 0, 0, 0, 0, 6,
    0, 0, 0, 0, -38, -5, -4, -18,
    -22, 0, 0, -30, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, -7, 0, 0,
    0, 0, 0, 0, 0, 0, 0, -3,
    0, 0, 0, 0, 0, 0, 0, 0,
    6, 0, 0, 0, 0, -38, -5, -4,
    -18, -22, 0, 0, -18, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    -4, 0, 0, 0, -10, 4, 0, -4,
    4, 8, 4, -13, 0, -1, -4, 4,
    0, 4, 0, 0, 0, 0, -11, 0,
    -4, -3, -9, 0, -4, -18, 0, 28,
    -4, 0, -10, -3, 0, -3, -8, 0,
    -4, -13, -9, -5, 0, 0, 0, -7,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -3, 0, 0, 0, 0, 0, 0,
    0, 0, 6, 0, 0, 0, 0, -38,
    -5, -4, -18, -22, 0, 0, -30, 0,
    0, 0, 0, 0, 0, 22, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    -7, 0, -14, -5, -4, 13, -4, -4,
    -18, 1, -3, 1, -3, -12, 1, 10,
    1, 4, 1, 4, -11, -18, -5, 0,
    -17, -9, -12, -19, -17, 0, -7, -9,
    -5, -6, -4, -3, -5, -3, 0, -3,
    -1, 7, 0, 7, -3, 0, 14, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, -3, -4, -4, 0, 0,
    -12, 0, -2, 0, -8, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    -27, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -4, -4, 0, -6,
    0, 0, 0, 0, -4, 0, 0, -8,
    -4, 4, 0, -8, -9, -3, 0, -13,
    -3, -10, -3, -5, 0, -8, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -30, 0, 14, 0, 0, -8, 0,
    0, 0, 0, -6, 0, -4, 0, 0,
    -2, 0, 0, -3, 0, -10, 0, 0,
    19, -6, -15, -14, 3, 5, 5, -1,
    -13, 3, 7, 3, 13, 3, 15, -3,
    -12, 0, 0, -18, 0, 0, -13, -12,
    0, 0, -9, 0, -6, -8, 0, -7,
    0, -7, 0, -3, 7, 0, -4, -13,
    -4, 17, 0, 0, -4, 0, -9, 0,
    0, 6, -10, 0, 4, -4, 4, 0,
    0, -15, 0, -3, -1, 0, -4, 5,
    -4, 0, 0, 0, -18, -5, -10, 0,
    -13, 0, 0, -21, 0, 17, -4, 0,
    -8, 0, 3, 0, -4, 0, -4, -13,
    0, -4, 4, 0, 0, 0, 0, -3,
    0, 0, 4, -6, 1, 0, 0, -5,
    -3, 0, -5, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -28, 0, 10, 0,
    0, -4, 0, 0, 0, 0, 1, 0,
    -4, -4, 0, 0, 0, 9, 0, 10,
    0, 0, 0, 0, 0, -28, -26, 1,
    19, 13, 8, -18, 3, 19, 0, 17,
    0, 9, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 24, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0
};


/*Collect the kern class' data in one place*/
static const lv_font_fmt_txt_kern_classes_t kern_classes = {
    .class_pair_values   = kern_class_values,
    .left_class_mapping  = kern_left_class_mapping,
    .right_class_mapping = kern_right_class_mapping,
    .left_class_cnt      = 61,
    .right_class_cnt     = 49,
};

/*--------------------
 *  ALL CUSTOM DATA
 *--------------------*/

#if LV_VERSION_CHECK(8, 0, 0)
/*Store all the custom data of the font*/
static  lv_font_fmt_txt_glyph_cache_t cache;
static const lv_font_fmt_txt_dsc_t font_dsc = {
#else
static lv_font_fmt_txt_dsc_t font_dsc = {
#endif
    .glyph_bitmap = glyph_bitmap,
    .glyph_dsc = glyph_dsc,
    .cmaps = cmaps,
    .kern_dsc = &kern_classes,
    .kern_scale = 16,
    .cmap_num = 2,
    .bpp = 4,
    .kern_classes = 1,
    .bitmap_format = 1,
#if LV_VERSION_CHECK(8, 0, 0)
    .cache = &cache
#endif
};


/*-----------------
 *  PUBLIC FONT
 *----------------*/

/*Initialize a public general font descriptor*/
#if LV_VERSION_CHECK(8, 0, 0)
const lv_font_t lv_font_montserrat_28_compressed = {
#else
lv_font_t lv_font_montserrat_28_compressed = {
#endif
    .get_glyph_dsc = lv_font_get_glyph_dsc_fmt_txt,    /*Function pointer to get glyph's data*/
    .get_glyph_bitmap = lv_font_get_bitmap_fmt_txt,    /*Function pointer to get glyph's bitmap*/
    .line_height = 30,          /*The maximum line height required by the font*/
    .base_line = 5,             /*Baseline measured from the bottom of the line*/
#if !(LVGL_VERSION_MAJOR == 6 && LVGL_VERSION_MINOR == 0)
    .subpx = LV_FONT_SUBPX_NONE,
#endif
#if LV_VERSION_CHECK(7, 4, 0) || LVGL_VERSION_MAJOR >= 8
    .underline_position = -2,
    .underline_thickness = 1,
#endif
    .dsc = &font_dsc           /*The custom font data. Will be accessed by `get_glyph_bitmap/dsc` */
};



#endif /*#if LV_FONT_MONTSERRAT_28_COMPRESSED*/

