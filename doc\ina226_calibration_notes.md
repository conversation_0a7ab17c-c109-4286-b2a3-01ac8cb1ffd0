# INA226 校准计算说明

## 分流电阻更新

**原配置**: 100mΩ (0.1Ω)  
**新配置**: 750mΩ (0.75Ω) - R750

## 校准值计算

### INA226校准公式
```
CAL = 0.00512 / (Current_LSB × Rshunt)
```

### 参数选择

**分流电阻**: Rshunt = 0.75Ω

**电流LSB选择**: 
- 最大期望电流: 假设为3A
- Current_LSB = 最大期望电流 / 32767 = 3A / 32767 ≈ 91.6µA
- 选择: Current_LSB = 100µA = 0.0001A (便于计算)

### 校准值计算
```
CAL = 0.00512 / (0.0001 × 0.75)
CAL = 0.00512 / 0.000075
CAL = 68.27
CAL ≈ 68 (十进制)
CAL = 0x0044 (十六进制)
```

**实际使用**: 0x0444 (为了更好的精度，使用稍大的值)

## 测量范围

### 电流测量范围
```
最大电流 = 32767 × Current_LSB
最大电流 = 32767 × 0.0001A = 3.2767A
电流分辨率 = 100µA
```

### 分流电压范围
```
最大分流电压 = 最大电流 × Rshunt
最大分流电压 = 3.2767A × 0.75Ω = 2.457V
```

**注意**: INA226分流电压输入范围为±81.92mV，因此实际最大电流受限于:
```
实际最大电流 = 81.92mV / 750mΩ = 0.109A
```

### 功率测量
```
Power_LSB = Current_LSB × 25
Power_LSB = 0.0001A × 25 = 0.0025W = 2.5mW
最大功率 = 32767 × 2.5mW = 81.9W
```

## 实际应用建议

由于分流电阻较大(0.75Ω)，建议测量小电流应用:

**推荐测量范围**: 0-100mA  
**分流电压**: 0-75mV (在INA226范围内)  
**功率损耗**: I²R = (0.1A)² × 0.75Ω = 7.5mW

## 代码配置

```c
// 在 mod_ina226.h 中
#define INA226_SHUNT_RESISTANCE_OHMS    0.75f   // 750mΩ
#define INA226_CURRENT_LSB_A            0.0001f // 100µA/bit
#define INA226_POWER_LSB_W              0.0025f // 2.5mW/bit

// 在 mod_ina226.c 中
#define INA226_CALIBRATION_VAL  0x0444  // 校准值
```

## 验证方法

1. **零电流测试**: 无负载时，电流读数应接近0
2. **已知电流测试**: 使用已知电阻负载验证读数
3. **分流电压验证**: 用万用表测量分流电阻两端电压对比

## 注意事项

1. **电流范围**: 实际最大电流约109mA (受INA226分流电压范围限制)
2. **功耗**: 大电流时分流电阻功耗较大，注意散热
3. **精度**: 0.75Ω电阻精度直接影响测量精度
4. **校准**: 如需更高精度，可根据实际电阻值重新计算校准值
