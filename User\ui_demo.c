/**
 ******************************************************************************
 * @file    ui_demo.c
 * <AUTHOR>
 * @version V1.6.0
 * @date    2025-07-03
 * @brief   UI demo with total charge (mAh) calculation
 ******************************************************************************
 */

/* Includes ------------------------------------------------------------------*/
#include "ui_demo.h"
#include "app_util.h"
#include "FreeRTOS.h"
#include "task.h"

/* Private typedef -----------------------------------------------------------*/
/* Private define ------------------------------------------------------------*/
#define CHART_POINT_COUNT       300 // Number of points to display on the chart
#define AVG_SAMPLE_COUNT        10  // Number of samples for moving average

/* Private macro -------------------------------------------------------------*/
/* Private variables ---------------------------------------------------------*/

/* INA226显示对象 */
static lv_obj_t *label_val_v = NULL;
static lv_obj_t *label_val_i = NULL;
static lv_obj_t *label_val_avgi = NULL;
static lv_obj_t *label_val_p = NULL;
static lv_obj_t *label_val_max_i = NULL;
static lv_obj_t *label_val_charge = NULL;

static lv_obj_t *current_chart = NULL;
static lv_chart_series_t *chart_series_current = NULL;

/* Moving average filter variables */
static float current_samples[AVG_SAMPLE_COUNT];
static int sample_index = 0;
static float current_sum = 0.0f;
static int sample_count = 0;

/* Tracking variables */
static float max_current_mA = -999999.0f;
static double total_charge_mAh = 0.0;
static uint32_t last_update_time_ms = 0;


/* Private function prototypes -----------------------------------------------*/
static void lv_chart_draw_event_cb(lv_event_t * e);

/* Private functions ---------------------------------------------------------*/

/**
 * @brief Event handler for drawing chart ticks and labels
 * @param e: Event object
 * @retval None
 */
static void lv_chart_draw_event_cb(lv_event_t * e)
{
    lv_obj_draw_part_dsc_t * dsc = lv_event_get_draw_part_dsc(e);
    if(!lv_obj_draw_part_check_type(dsc, &lv_chart_class, LV_CHART_DRAW_PART_TICK_LABEL)) return;

    if(dsc->id == LV_CHART_AXIS_PRIMARY_Y && dsc->text) {
        lv_snprintf(dsc->text, dsc->text_length, "%d", dsc->value);
    }
}

/* Public functions ----------------------------------------------------------*/

/**
 * @brief Initialize UI demo module
 * @retval None
 */
void ui_demo_init(void) {
  // 初始化UI对象
  label_val_v = NULL;
  label_val_i = NULL;
  label_val_avgi = NULL;
  label_val_p = NULL;
  label_val_max_i = NULL;
  label_val_charge = NULL;
  current_chart = NULL;
  chart_series_current = NULL;

  // 初始化均值滤波器
  for(int i = 0; i < AVG_SAMPLE_COUNT; i++) {
      current_samples[i] = 0.0f;
  }
  sample_index = 0;
  current_sum = 0.0f;
  sample_count = 0;
  
  // 初始化跟踪变量
  max_current_mA = -999999.0f;
  total_charge_mAh = 0.0;
  last_update_time_ms = 0;

  logi("UI demo module initialized\n");
}

/**
 * @brief Cleanup UI demo resources
 * @retval None
 */
void ui_demo_cleanup(void) {
  // 清理资源
  label_val_v = NULL;
  label_val_i = NULL;
  label_val_avgi = NULL;
  label_val_p = NULL;
  label_val_max_i = NULL;
  label_val_charge = NULL;
  current_chart = NULL;
  chart_series_current = NULL;

  logi("UI demo module cleaned up\n");
}

/**
 * @brief 创建INA226数据显示界面
 * @retval None
 */
void ui_create_ina226_display(void) {
  /* 获取当前活动屏幕 */
  lv_obj_t *scr = lv_scr_act();

  /* --- 创建顶部信息栏 (绝对定位, 3行) --- */
  // Row 1
  lv_obj_t * label_title_v = lv_label_create(scr);
  lv_label_set_text(label_title_v, "Voltage:");
  lv_obj_align(label_title_v, LV_ALIGN_TOP_LEFT, 5, 5);
  label_val_v = lv_label_create(scr);
  lv_label_set_text(label_val_v, "--");
  lv_obj_align_to(label_val_v, label_title_v, LV_ALIGN_OUT_RIGHT_MID, 5, 0);

  lv_obj_t * label_title_i = lv_label_create(scr);
  lv_label_set_text(label_title_i, "Current:");
  lv_obj_align(label_title_i, LV_ALIGN_TOP_LEFT, 150, 5);
  label_val_i = lv_label_create(scr);
  lv_label_set_text(label_val_i, "--");
  lv_obj_align_to(label_val_i, label_title_i, LV_ALIGN_OUT_RIGHT_MID, 5, 0);

  // Row 2
  lv_obj_t * label_title_p = lv_label_create(scr);
  lv_label_set_text(label_title_p, "Power:");
  lv_obj_align(label_title_p, LV_ALIGN_TOP_LEFT, 5, 25);
  label_val_p = lv_label_create(scr);
  lv_label_set_text(label_val_p, "--");
  lv_obj_align_to(label_val_p, label_title_p, LV_ALIGN_OUT_RIGHT_MID, 5, 0);

  lv_obj_t * label_title_avgi = lv_label_create(scr);
  lv_label_set_text(label_title_avgi, "Avg Curr:");
  lv_obj_align(label_title_avgi, LV_ALIGN_TOP_LEFT, 150, 25);
  label_val_avgi = lv_label_create(scr);
  lv_label_set_text(label_val_avgi, "--");
  lv_obj_align_to(label_val_avgi, label_title_avgi, LV_ALIGN_OUT_RIGHT_MID, 5, 0);

  // Row 3
  lv_obj_t * label_title_max_i = lv_label_create(scr);
  lv_label_set_text(label_title_max_i, "Max Curr:");
  lv_obj_align(label_title_max_i, LV_ALIGN_TOP_LEFT, 5, 45);
  label_val_max_i = lv_label_create(scr);
  lv_label_set_text(label_val_max_i, "--");
  lv_obj_align_to(label_val_max_i, label_title_max_i, LV_ALIGN_OUT_RIGHT_MID, 5, 0);

  lv_obj_t * label_title_charge = lv_label_create(scr);
  lv_label_set_text(label_title_charge, "Charge:");
  lv_obj_align(label_title_charge, LV_ALIGN_TOP_LEFT, 150, 45);
  label_val_charge = lv_label_create(scr);
  lv_label_set_text(label_val_charge, "--");
  lv_obj_align_to(label_val_charge, label_title_charge, LV_ALIGN_OUT_RIGHT_MID, 5, 0);


  /* --- 创建图表 --- */
  current_chart = lv_chart_create(scr);
  lv_obj_set_size(current_chart, LV_HOR_RES - 20, LV_VER_RES - 75);
  lv_obj_align(current_chart, LV_ALIGN_TOP_LEFT, 10, 70);
  lv_chart_set_type(current_chart, LV_CHART_TYPE_LINE);
  lv_chart_set_point_count(current_chart, CHART_POINT_COUNT);
  lv_chart_set_range(current_chart, LV_CHART_AXIS_PRIMARY_Y, 0, 22); // Y-axis range 0-100mA
  lv_chart_set_update_mode(current_chart, LV_CHART_UPDATE_MODE_SHIFT);

  /* 添加Y轴刻度线和标签 */
  lv_chart_set_div_line_count(current_chart, 5, 14); // 5 horizontal, 10 vertical lines
  lv_obj_add_event_cb(current_chart, lv_chart_draw_event_cb, LV_EVENT_DRAW_PART_BEGIN, NULL);

  /* 添加一个数据系列 */
  chart_series_current = lv_chart_add_series(current_chart, lv_palette_main(LV_PALETTE_RED), LV_CHART_AXIS_PRIMARY_Y);

  /* 设置线条样式 - 调整线条粗细 */
  lv_obj_set_style_line_width(current_chart, 1, LV_PART_ITEMS);  // 设置线条宽度为1像素（更细）

  logi("INA226 display UI with charge calculation created\n");
}

// 扫描线更新相关变量
static uint32_t chart_scan_pos = 0;
static lv_coord_t *chart_data_array = NULL;
static uint32_t chart_point_count = 0;

// 圆点指示器相关变量
static lv_obj_t *scan_indicator = NULL;
static lv_style_t scan_indicator_style;

/**
 * @brief 创建扫描位置指示器圆点
 * @retval None
 */
static void ui_create_scan_indicator(void) {
  if (scan_indicator != NULL || current_chart == NULL) {
    return; // 已经创建或图表不存在
  }

  // 初始化圆点样式
  lv_style_init(&scan_indicator_style);
  lv_style_set_radius(&scan_indicator_style, LV_RADIUS_CIRCLE);
  lv_style_set_bg_color(&scan_indicator_style, lv_color_hex(0xFF0000)); // 红色
  lv_style_set_bg_opa(&scan_indicator_style, LV_OPA_80);
  lv_style_set_border_width(&scan_indicator_style, 2);
  lv_style_set_border_color(&scan_indicator_style, lv_color_hex(0xFFFFFF)); // 白色边框
  lv_style_set_border_opa(&scan_indicator_style, LV_OPA_100);

  // 创建圆点对象
  scan_indicator = lv_obj_create(lv_obj_get_parent(current_chart));
  lv_obj_add_style(scan_indicator, &scan_indicator_style, 0);
  lv_obj_set_size(scan_indicator, 8, 8); // 8x8像素的小圆点

  // 初始位置设置在图表左上角
  lv_coord_t chart_x = lv_obj_get_x(current_chart);
  lv_coord_t chart_y = lv_obj_get_y(current_chart);
  lv_obj_set_pos(scan_indicator, chart_x, chart_y);

  logi("Scan indicator created\r\n");
}

/**
 * @brief 更新扫描位置指示器位置
 * @param scan_pos: 当前扫描位置 (0 到 chart_point_count-1)
 * @retval None
 */
static void ui_update_scan_indicator(uint32_t scan_pos) {
  if (scan_indicator == NULL || current_chart == NULL || chart_point_count == 0) {
    return;
  }

  // 计算图表的位置和尺寸
  lv_coord_t chart_x = lv_obj_get_x(current_chart);
  lv_coord_t chart_y = lv_obj_get_y(current_chart);
  lv_coord_t chart_width = lv_obj_get_width(current_chart);
  lv_coord_t chart_height = lv_obj_get_height(current_chart);

  // 计算圆点的X位置（基于扫描位置）
  lv_coord_t indicator_x = chart_x + (scan_pos * chart_width) / chart_point_count;

  // Y位置设置在图表顶部
  lv_coord_t indicator_y = chart_y - 5; // 图表上方5像素

  // 更新圆点位置
  lv_obj_set_pos(scan_indicator, indicator_x, indicator_y);
}

/**
 * @brief 直接从DMA缓冲区扫描线更新图表 (避免全屏刷新)
 * @param dma_buffer: DMA缓冲区指针
 * @param buffer_size: 缓冲区大小
 * @param start_pos: 开始位置
 * @param count: 要处理的样本数
 * @retval None
 */
void ui_update_chart_from_dma_buffer(uint16_t *dma_buffer, uint32_t buffer_size, uint32_t start_pos, uint32_t count) {
  if (dma_buffer == NULL || current_chart == NULL || chart_series_current == NULL || count == 0) {
    return;
  }

  // 初始化图表数据数组（仅第一次）
  if (chart_data_array == NULL) {
    chart_point_count = lv_chart_get_point_count(current_chart);
    chart_data_array = lv_chart_get_y_array(current_chart, chart_series_current);
    logi("Chart scan init: %lu points\r\n", chart_point_count);

    // 创建扫描位置指示器
    ui_create_scan_indicator();
  }

  // 扫描线更新：从左到右逐点更新，避免全屏刷新
  for (uint32_t i = 0; i < count && i < 5; i++) { // 限制每次最多更新5个点
    uint32_t pos = (start_pos + i) % buffer_size;
    uint16_t raw_sample = dma_buffer[pos];

    // 快速转换为电流 (mA)
    int16_t signed_value = (int16_t)raw_sample;
    float shunt_mv = signed_value * 0.0025f; // 2.5µV per bit
    float current_a = (shunt_mv / 1000.0f) / 0.75f; // I = V / R (0.75Ω)
    float current_mA = current_a * 1000.0f;

    // 扫描线更新：直接修改数据数组
    if (chart_data_array != NULL && chart_point_count > 0) {
      chart_data_array[chart_scan_pos] = (lv_coord_t)current_mA;

      // 更新圆点指示器位置
      ui_update_scan_indicator(chart_scan_pos);

      // 移动到下一个位置
      chart_scan_pos = (chart_scan_pos + 1) % chart_point_count;

      // 每10个点刷新一次图表（局部刷新）
      if ((chart_scan_pos % 10) == 0) {
        lv_chart_refresh(current_chart);
      }
    }
  }

  // 调试：偶尔输出扫描位置
  static uint32_t scan_debug_counter = 0;
  if ((++scan_debug_counter % 100) == 0) {
    logi("Chart scan pos: %lu/%lu\r\n", chart_scan_pos, chart_point_count);
  }
}

/**
 * @brief 重置图表扫描位置
 * @retval None
 */
void ui_reset_chart_scan(void) {
  chart_scan_pos = 0;

  // 重置圆点指示器位置
  ui_update_scan_indicator(chart_scan_pos);

  logi("Chart scan position reset\r\n");
}

/**
 * @brief 更新INA226数据显示
 * @param voltage: 电压值
 * @param current: 电流值
 * @param power: 功率值
 * @param shunt_voltage: 分流电压值
 * @retval None
 */
void ui_update_ina226_data(float voltage, float current, float power, float shunt_voltage) {
  char buf[32];
  float current_mA = current * 1000.0f;
  float avg_current_mA = 0.0f;
  uint32_t current_time_ms = xTaskGetTickCount();

  // --- 时间积分计算电量 ---
  if (last_update_time_ms != 0) {
      uint32_t time_delta_ms = current_time_ms - last_update_time_ms;
      double time_delta_h = time_delta_ms / 3600000.0; // ms to hours
      total_charge_mAh += (current_mA * time_delta_h);
  }
  last_update_time_ms = current_time_ms;
  // -------------------------

  // --- Moving Average Filter ---
  current_sum -= current_samples[sample_index];
  current_samples[sample_index] = current_mA;
  current_sum += current_mA;
  sample_index = (sample_index + 1) % AVG_SAMPLE_COUNT;
  if(sample_count < AVG_SAMPLE_COUNT) {
      sample_count++;
  }
  avg_current_mA = current_sum / sample_count;
  // ---------------------------

  // --- Max Tracking ---
  if (current_mA > max_current_mA) {
      max_current_mA = current_mA;
  }
  // ------------------------

  // 更新顶部信息行 (separate labels)
  if (label_val_v) {
      sprintf(buf, "%.3f V", voltage);
      lv_label_set_text(label_val_v, buf);
  }
  if (label_val_i) {
      sprintf(buf, "%.3f mA", current_mA);
      lv_label_set_text(label_val_i, buf);
  }
  if (label_val_avgi) {
      sprintf(buf, "%.3f mA", avg_current_mA);
      lv_label_set_text(label_val_avgi, buf);
  }
  if (label_val_p) {
      sprintf(buf, "%.3f W", power);
      lv_label_set_text(label_val_p, buf);
  }
  if (label_val_max_i) {
      sprintf(buf, "%.3f mA", max_current_mA);
      lv_label_set_text(label_val_max_i, buf);
  }
  if (label_val_charge) {
      sprintf(buf, "%.3f mAh", total_charge_mAh);
      lv_label_set_text(label_val_charge, buf);
  }
}
