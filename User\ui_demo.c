/**
 ******************************************************************************
 * @file    ui_demo.c
 * <AUTHOR>
 * @version V1.6.0
 * @date    2025-07-03
 * @brief   UI demo with total charge (mAh) calculation
 ******************************************************************************
 */

/* Includes ------------------------------------------------------------------*/
#include "ui_demo.h"
#include "app_util.h"
#include "FreeRTOS.h"
#include "task.h"

/* Private typedef -----------------------------------------------------------*/
/* Private define ------------------------------------------------------------*/
#define CHART_POINT_COUNT       300 // Number of points to display on the chart
#define AVG_SAMPLE_COUNT        10  // Number of samples for moving average

/* Private macro -------------------------------------------------------------*/
/* Private variables ---------------------------------------------------------*/

/* INA226显示对象 */
static lv_obj_t *label_val_v = NULL;
static lv_obj_t *label_val_i = NULL;
static lv_obj_t *label_val_avgi = NULL;
static lv_obj_t *label_val_p = NULL;
static lv_obj_t *label_val_max_i = NULL;
static lv_obj_t *label_val_charge = NULL;

static lv_obj_t *current_chart = NULL;
static lv_chart_series_t *chart_series_current = NULL;

/* Moving average filter variables */
static float current_samples[AVG_SAMPLE_COUNT];
static int sample_index = 0;
static float current_sum = 0.0f;
static int sample_count = 0;

/* Tracking variables */
static float max_current_mA = -999999.0f;
static double total_charge_mAh = 0.0;
static uint32_t last_update_time_ms = 0;


/* Private function prototypes -----------------------------------------------*/
static void lv_chart_draw_event_cb(lv_event_t * e);

/* Private functions ---------------------------------------------------------*/

/**
 * @brief Event handler for drawing chart ticks and labels
 * @param e: Event object
 * @retval None
 */
static void lv_chart_draw_event_cb(lv_event_t * e)
{
    lv_obj_draw_part_dsc_t * dsc = lv_event_get_draw_part_dsc(e);
    if(!lv_obj_draw_part_check_type(dsc, &lv_chart_class, LV_CHART_DRAW_PART_TICK_LABEL)) return;

    if(dsc->id == LV_CHART_AXIS_PRIMARY_Y && dsc->text) {
        lv_snprintf(dsc->text, dsc->text_length, "%d", dsc->value);
    }
}

/* Public functions ----------------------------------------------------------*/

/**
 * @brief Initialize UI demo module
 * @retval None
 */
void ui_demo_init(void) {
  // 初始化UI对象
  label_val_v = NULL;
  label_val_i = NULL;
  label_val_avgi = NULL;
  label_val_p = NULL;
  label_val_max_i = NULL;
  label_val_charge = NULL;
  current_chart = NULL;
  chart_series_current = NULL;

  // 初始化均值滤波器
  for(int i = 0; i < AVG_SAMPLE_COUNT; i++) {
      current_samples[i] = 0.0f;
  }
  sample_index = 0;
  current_sum = 0.0f;
  sample_count = 0;
  
  // 初始化跟踪变量
  max_current_mA = -999999.0f;
  total_charge_mAh = 0.0;
  last_update_time_ms = 0;

  logi("UI demo module initialized\n");
}

/**
 * @brief Cleanup UI demo resources
 * @retval None
 */
void ui_demo_cleanup(void) {
  // 清理资源
  label_val_v = NULL;
  label_val_i = NULL;
  label_val_avgi = NULL;
  label_val_p = NULL;
  label_val_max_i = NULL;
  label_val_charge = NULL;
  current_chart = NULL;
  chart_series_current = NULL;

  logi("UI demo module cleaned up\n");
}

/**
 * @brief 创建INA226数据显示界面
 * @retval None
 */
void ui_create_ina226_display(void) {
  /* 获取当前活动屏幕 */
  lv_obj_t *scr = lv_scr_act();

  /* --- 创建顶部信息栏 (绝对定位, 3行) --- */
  // Row 1
  lv_obj_t * label_title_v = lv_label_create(scr);
  lv_label_set_text(label_title_v, "Voltage:");
  lv_obj_align(label_title_v, LV_ALIGN_TOP_LEFT, 5, 5);
  label_val_v = lv_label_create(scr);
  lv_label_set_text(label_val_v, "--");
  lv_obj_align_to(label_val_v, label_title_v, LV_ALIGN_OUT_RIGHT_MID, 5, 0);

  lv_obj_t * label_title_i = lv_label_create(scr);
  lv_label_set_text(label_title_i, "Current:");
  lv_obj_align(label_title_i, LV_ALIGN_TOP_LEFT, 150, 5);
  label_val_i = lv_label_create(scr);
  lv_label_set_text(label_val_i, "--");
  lv_obj_align_to(label_val_i, label_title_i, LV_ALIGN_OUT_RIGHT_MID, 5, 0);

  // Row 2
  lv_obj_t * label_title_p = lv_label_create(scr);
  lv_label_set_text(label_title_p, "Power:");
  lv_obj_align(label_title_p, LV_ALIGN_TOP_LEFT, 5, 25);
  label_val_p = lv_label_create(scr);
  lv_label_set_text(label_val_p, "--");
  lv_obj_align_to(label_val_p, label_title_p, LV_ALIGN_OUT_RIGHT_MID, 5, 0);

  lv_obj_t * label_title_avgi = lv_label_create(scr);
  lv_label_set_text(label_title_avgi, "Avg Curr:");
  lv_obj_align(label_title_avgi, LV_ALIGN_TOP_LEFT, 150, 25);
  label_val_avgi = lv_label_create(scr);
  lv_label_set_text(label_val_avgi, "--");
  lv_obj_align_to(label_val_avgi, label_title_avgi, LV_ALIGN_OUT_RIGHT_MID, 5, 0);

  // Row 3
  lv_obj_t * label_title_max_i = lv_label_create(scr);
  lv_label_set_text(label_title_max_i, "Max Curr:");
  lv_obj_align(label_title_max_i, LV_ALIGN_TOP_LEFT, 5, 45);
  label_val_max_i = lv_label_create(scr);
  lv_label_set_text(label_val_max_i, "--");
  lv_obj_align_to(label_val_max_i, label_title_max_i, LV_ALIGN_OUT_RIGHT_MID, 5, 0);

  lv_obj_t * label_title_charge = lv_label_create(scr);
  lv_label_set_text(label_title_charge, "Charge:");
  lv_obj_align(label_title_charge, LV_ALIGN_TOP_LEFT, 150, 45);
  label_val_charge = lv_label_create(scr);
  lv_label_set_text(label_val_charge, "--");
  lv_obj_align_to(label_val_charge, label_title_charge, LV_ALIGN_OUT_RIGHT_MID, 5, 0);


  /* --- 创建图表 --- */
  current_chart = lv_chart_create(scr);
  lv_obj_set_size(current_chart, LV_HOR_RES - 20, LV_VER_RES - 75);
  lv_obj_align(current_chart, LV_ALIGN_TOP_LEFT, 10, 70);
  lv_chart_set_type(current_chart, LV_CHART_TYPE_LINE);
  lv_chart_set_point_count(current_chart, CHART_POINT_COUNT);
  lv_chart_set_range(current_chart, LV_CHART_AXIS_PRIMARY_Y, 0, 10); // Y-axis range 0-100mA
  lv_chart_set_update_mode(current_chart, LV_CHART_UPDATE_MODE_SHIFT);

  /* 添加Y轴刻度线和标签 */
  lv_chart_set_div_line_count(current_chart, 5, 14); // 5 horizontal, 10 vertical lines
  lv_obj_add_event_cb(current_chart, lv_chart_draw_event_cb, LV_EVENT_DRAW_PART_BEGIN, NULL);

  /* 添加一个数据系列 */
  chart_series_current = lv_chart_add_series(current_chart, lv_palette_main(LV_PALETTE_RED), LV_CHART_AXIS_PRIMARY_Y);

  /* 设置线条样式 - 调整线条粗细 */
  lv_obj_set_style_line_width(current_chart, 1, LV_PART_ITEMS);  // 设置线条宽度为1像素（更细）

  logi("INA226 display UI with charge calculation created\n");
}

/**
 * @brief 更新INA226数据显示
 * @param voltage: 电压值
 * @param current: 电流值
 * @param power: 功率值
 * @param shunt_voltage: 分流电压值
 * @retval None
 */
void ui_update_ina226_data(float voltage, float current, float power, float shunt_voltage) {
  char buf[32];
  float current_mA = current * 1000.0f;
  float avg_current_mA = 0.0f;
  uint32_t current_time_ms = xTaskGetTickCount();

  // --- 时间积分计算电量 ---
  if (last_update_time_ms != 0) {
      uint32_t time_delta_ms = current_time_ms - last_update_time_ms;
      double time_delta_h = time_delta_ms / 3600000.0; // ms to hours
      total_charge_mAh += (current_mA * time_delta_h);
  }
  last_update_time_ms = current_time_ms;
  // -------------------------

  // --- Moving Average Filter ---
  current_sum -= current_samples[sample_index];
  current_samples[sample_index] = current_mA;
  current_sum += current_mA;
  sample_index = (sample_index + 1) % AVG_SAMPLE_COUNT;
  if(sample_count < AVG_SAMPLE_COUNT) {
      sample_count++;
  }
  avg_current_mA = current_sum / sample_count;
  // ---------------------------

  // --- Max Tracking ---
  if (current_mA > max_current_mA) {
      max_current_mA = current_mA;
  }
  // ------------------------

  // 更新顶部信息行 (separate labels)
  if (label_val_v) {
      sprintf(buf, "%.3f V", voltage);
      lv_label_set_text(label_val_v, buf);
  }
  if (label_val_i) {
      sprintf(buf, "%.3f mA", current_mA);
      lv_label_set_text(label_val_i, buf);
  }
  if (label_val_avgi) {
      sprintf(buf, "%.3f mA", avg_current_mA);
      lv_label_set_text(label_val_avgi, buf);
  }
  if (label_val_p) {
      sprintf(buf, "%.3f W", power);
      lv_label_set_text(label_val_p, buf);
  }
  if (label_val_max_i) {
      sprintf(buf, "%.3f mA", max_current_mA);
      lv_label_set_text(label_val_max_i, buf);
  }
  if (label_val_charge) {
      sprintf(buf, "%.3f mAh", total_charge_mAh);
      lv_label_set_text(label_val_charge, buf);
  }


  // 更新图表
  if (current_chart != NULL && chart_series_current != NULL) {
    lv_chart_set_next_value(current_chart, chart_series_current, (lv_coord_t)current_mA);
  }

  // 输出调试信息
//   logi("UI updated: V=%.3fV, I=%.3fA, P=%.3fW, Charge=%.6fmAh\n",
//        voltage, current, power, total_charge_mAh);
}

/**
 * @brief 测试INA226数据显示更新
 * @retval None
 */
void ui_test_ina226_update(void) {
  static float test_voltage = 3.3f;
  static float test_current = 0.05f; // 50mA
  static float test_power = 0.165f;
  static float test_shunt = 7.5f;

  // 模拟数据变化
  test_current += 0.005f; // increment by 5mA
  if (test_current > 0.1f) { // reset after 100mA
      test_current = 0.01f;
  }
  test_voltage = 3.3f;
  test_power = test_voltage * test_current;
  test_shunt = test_current * 0.15f * 1000.0f; // R_SHUNT = 0.15

  // 更新显示
  ui_update_ina226_data(test_voltage, test_current, test_power, test_shunt);

  logi("Test data updated\n");
}
