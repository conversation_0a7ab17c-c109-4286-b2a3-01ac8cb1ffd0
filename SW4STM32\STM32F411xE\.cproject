<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<?fileVersion 4.0.0?><cproject storage_type_id="org.eclipse.cdt.core.XmlProjectDescriptionStorage">
	<storageModule moduleId="org.eclipse.cdt.core.settings">
		<cconfiguration id="fr.ac6.managedbuild.config.gnu.cross.exe.debug.185059192">
			<storageModule buildSystemId="org.eclipse.cdt.managedbuilder.core.configurationDataProvider" id="fr.ac6.managedbuild.config.gnu.cross.exe.debug.185059192" moduleId="org.eclipse.cdt.core.settings" name="Debug">
				<externalSettings/>
				<extensions>
					<extension id="org.eclipse.cdt.core.ELF" point="org.eclipse.cdt.core.BinaryParser"/>
					<extension id="org.eclipse.cdt.core.GASErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
					<extension id="org.eclipse.cdt.core.GmakeErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
					<extension id="org.eclipse.cdt.core.GLDErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
					<extension id="org.eclipse.cdt.core.CWDLocator" point="org.eclipse.cdt.core.ErrorParser"/>
					<extension id="org.eclipse.cdt.core.GCCErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
				</extensions>
			</storageModule>
			<storageModule moduleId="cdtBuildSystem" version="4.0.0">
				<configuration artifactExtension="elf" artifactName="STM32F411xE" buildArtefactType="org.eclipse.cdt.build.core.buildArtefactType.exe" buildProperties="org.eclipse.cdt.build.core.buildArtefactType=org.eclipse.cdt.build.core.buildArtefactType.exe,org.eclipse.cdt.build.core.buildType=org.eclipse.cdt.build.core.buildType.debug" cleanCommand="rm -rf" description="" id="fr.ac6.managedbuild.config.gnu.cross.exe.debug.185059192" name="Debug" parent="fr.ac6.managedbuild.config.gnu.cross.exe.debug" postannouncebuildStep="Generating binary and Printing size information:" postbuildStep="arm-none-eabi-objcopy -O binary &quot;${BuildArtifactFileBaseName}.elf&quot; &quot;${BuildArtifactFileBaseName}.bin&quot; &amp;&amp; arm-none-eabi-size &quot;${BuildArtifactFileName}&quot;">
					<folderInfo id="fr.ac6.managedbuild.config.gnu.cross.exe.debug.185059192." name="/" resourcePath="">
						<toolChain id="fr.ac6.managedbuild.toolchain.gnu.cross.exe.debug.402655114" name="Ac6 STM32 MCU GCC" superClass="fr.ac6.managedbuild.toolchain.gnu.cross.exe.debug">
							<option id="fr.ac6.managedbuild.option.gnu.cross.prefix.1490651114" name="Prefix" superClass="fr.ac6.managedbuild.option.gnu.cross.prefix" value="arm-none-eabi-" valueType="string"/>
							<option id="fr.ac6.managedbuild.option.gnu.cross.mcu.1864314405" name="Mcu" superClass="fr.ac6.managedbuild.option.gnu.cross.mcu" value="STM32F411RETx" valueType="string"/>
							<option id="fr.ac6.managedbuild.option.gnu.cross.board.1294272279" name="Board" superClass="fr.ac6.managedbuild.option.gnu.cross.board" value="NUCLEO-F411RE" valueType="string"/>
							<option id="fr.ac6.managedbuild.option.gnu.cross.instructionSet.1248518152" name="Instruction Set" superClass="fr.ac6.managedbuild.option.gnu.cross.instructionSet" value="fr.ac6.managedbuild.option.gnu.cross.instructionSet.thumbII" valueType="enumerated"/>
							<option id="fr.ac6.managedbuild.option.gnu.cross.fpu.281420520" name="Floating point hardware" superClass="fr.ac6.managedbuild.option.gnu.cross.fpu" value="fr.ac6.managedbuild.option.gnu.cross.fpu.fpv4-sp-d16" valueType="enumerated"/>
							<option id="fr.ac6.managedbuild.option.gnu.cross.floatabi.258373833" name="Floating-point ABI" superClass="fr.ac6.managedbuild.option.gnu.cross.floatabi" value="fr.ac6.managedbuild.option.gnu.cross.floatabi.hard" valueType="enumerated"/>
							<targetPlatform archList="all" binaryParser="org.eclipse.cdt.core.ELF" id="fr.ac6.managedbuild.targetPlatform.gnu.cross.1268845717" isAbstract="false" osList="all" superClass="fr.ac6.managedbuild.targetPlatform.gnu.cross"/>
							<builder buildPath="${workspace_loc:/STM32F411xE}/Debug" id="fr.ac6.managedbuild.builder.gnu.cross.1487189287" keepEnvironmentInBuildfile="false" managedBuildOn="true" name="Gnu Make Builder" superClass="fr.ac6.managedbuild.builder.gnu.cross">
								<outputEntries>
									<entry flags="VALUE_WORKSPACE_PATH|RESOLVED" kind="outputPath" name="Debug"/>
								</outputEntries>
							</builder>
							<tool id="fr.ac6.managedbuild.tool.gnu.cross.c.compiler.1195731775" name="MCU GCC Compiler" superClass="fr.ac6.managedbuild.tool.gnu.cross.c.compiler">
								<option defaultValue="gnu.c.optimization.level.none" id="fr.ac6.managedbuild.gnu.c.compiler.option.optimization.level.1556233749" name="Optimization Level" superClass="fr.ac6.managedbuild.gnu.c.compiler.option.optimization.level" useByScannerDiscovery="false" value="fr.ac6.managedbuild.gnu.c.optimization.level.size" valueType="enumerated"/>
								<option id="gnu.c.compiler.option.debugging.level.671864013" name="Debug Level" superClass="gnu.c.compiler.option.debugging.level" useByScannerDiscovery="false" value="gnu.c.debugging.level.max" valueType="enumerated"/>
								<option id="gnu.c.compiler.option.include.paths.1798496432" name="Include paths (-I)" superClass="gnu.c.compiler.option.include.paths" useByScannerDiscovery="false" valueType="includePath">
									<listOptionValue builtIn="false" value="../../../"/>
									<listOptionValue builtIn="false" value="../../../../../Libraries/CMSIS/Device/ST/STM32F4xx/Include"/>
									<listOptionValue builtIn="false" value="../../../../../Libraries/STM32F4xx_StdPeriph_Driver/inc"/>
									<listOptionValue builtIn="false" value="../../../../../Utilities/STM32_EVAL/Common"/>
									<listOptionValue builtIn="false" value="../../../../../Libraries/CMSIS/Include"/>
								</option>
								<option id="gnu.c.compiler.option.preprocessor.def.symbols.1575896640" name="Defined symbols (-D)" superClass="gnu.c.compiler.option.preprocessor.def.symbols" useByScannerDiscovery="false" valueType="definedSymbols">
									<listOptionValue builtIn="false" value="USE_STDPERIPH_DRIVER"/>
									<listOptionValue builtIn="false" value="STM32F411xE"/>
								</option>
								<option id="fr.ac6.managedbuild.gnu.c.compiler.option.misc.other.990513661" superClass="fr.ac6.managedbuild.gnu.c.compiler.option.misc.other" useByScannerDiscovery="false" value="-fmessage-length=0" valueType="string"/>
								<inputType id="fr.ac6.managedbuild.tool.gnu.cross.c.compiler.input.c.1811388834" superClass="fr.ac6.managedbuild.tool.gnu.cross.c.compiler.input.c"/>
								<inputType id="fr.ac6.managedbuild.tool.gnu.cross.c.compiler.input.s.1971548810" superClass="fr.ac6.managedbuild.tool.gnu.cross.c.compiler.input.s"/>
							</tool>
							<tool id="fr.ac6.managedbuild.tool.gnu.cross.cpp.compiler.793758022" name="MCU G++ Compiler" superClass="fr.ac6.managedbuild.tool.gnu.cross.cpp.compiler">
								<option id="gnu.cpp.compiler.option.optimization.level.1597312672" name="Optimization Level" superClass="gnu.cpp.compiler.option.optimization.level" useByScannerDiscovery="false" value="gnu.cpp.compiler.optimization.level.none" valueType="enumerated"/>
								<option id="gnu.cpp.compiler.option.debugging.level.897540730" name="Debug Level" superClass="gnu.cpp.compiler.option.debugging.level" useByScannerDiscovery="false" value="gnu.cpp.compiler.debugging.level.max" valueType="enumerated"/>
							</tool>
							<tool id="fr.ac6.managedbuild.tool.gnu.cross.c.linker.1348727087" name="MCU GCC Linker" superClass="fr.ac6.managedbuild.tool.gnu.cross.c.linker">
								<option id="fr.ac6.managedbuild.tool.gnu.cross.c.linker.script.1592161325" name="Linker Script (-T)" superClass="fr.ac6.managedbuild.tool.gnu.cross.c.linker.script" value="../STM32F411RETx_FLASH.ld" valueType="string"/>
								<option id="gnu.c.link.option.libs.1900269868" name="Libraries (-l)" superClass="gnu.c.link.option.libs"/>
								<option id="gnu.c.link.option.paths.1545695916" name="Library search path (-L)" superClass="gnu.c.link.option.paths"/>
								<option id="gnu.c.link.option.ldflags.190227661" name="Linker flags" superClass="gnu.c.link.option.ldflags" value="-specs=nosys.specs -specs=nano.specs" valueType="string"/>
								<inputType id="cdt.managedbuild.tool.gnu.c.linker.input.695498745" superClass="cdt.managedbuild.tool.gnu.c.linker.input">
									<additionalInput kind="additionalinputdependency" paths="$(USER_OBJS)"/>
									<additionalInput kind="additionalinput" paths="$(LIBS)"/>
								</inputType>
							</tool>
							<tool id="fr.ac6.managedbuild.tool.gnu.cross.cpp.linker.1403898593" name="MCU G++ Linker" superClass="fr.ac6.managedbuild.tool.gnu.cross.cpp.linker"/>
							<tool id="fr.ac6.managedbuild.tool.gnu.archiver.1877480948" name="MCU GCC Archiver" superClass="fr.ac6.managedbuild.tool.gnu.archiver"/>
							<tool id="fr.ac6.managedbuild.tool.gnu.cross.assembler.1034425510" name="MCU GCC Assembler" superClass="fr.ac6.managedbuild.tool.gnu.cross.assembler">
								<option id="gnu.both.asm.option.include.paths.328352518" name="Include paths (-I)" superClass="gnu.both.asm.option.include.paths" valueType="includePath"/>
								<inputType id="cdt.managedbuild.tool.gnu.assembler.input.1491463964" superClass="cdt.managedbuild.tool.gnu.assembler.input"/>
								<inputType id="fr.ac6.managedbuild.tool.gnu.cross.assembler.input.1349876861" superClass="fr.ac6.managedbuild.tool.gnu.cross.assembler.input"/>
							</tool>
						</toolChain>
					</folderInfo>
					<sourceEntries>
						<entry excluding="SW4STM32/startup_stm32f412xg.s|SW4STM32/startup_stm32f469_479xx.s|SW4STM32/startup_stm32f446xx.s|SW4STM32/startup_stm32f429_439xx.s|SW4STM32/startup_stm32f427_437xx.s|SW4STM32/startup_stm32f412xg.s|SW4STM32/startup_stm32f410xx.s|SW4STM32/startup_stm32f401xx.s|SW4STM32/startup_stm32f40_41xxx.s|STM32F4xx_StdPeriph_Driver/stm32f4xx_qspi.c|STM32F4xx_StdPeriph_Driver/stm32f4xx_fmc.c|STM32F4xx_StdPeriph_Driver/stm32f4xx_fsmc.c|STM32F4xx_StdPeriph_Driver/stm32f4xx_dsi.c|STM32F4xx_StdPeriph_Driver/stm32f4xx_cec.c|../../Libraries/STM32F4xx_StdPeriph_Driver/src/stm32f4xx_fsmc.c|../../Libraries/CMSIS/Device/ST/STM32F4xx/Source/Templates/iar/startup_stm32f427_437xx.s|../../Libraries/STM32F4xx_StdPeriph_Driver/src/stm32f4xx_fmc.c|../../Libraries/STM32F4xx_StdPeriph_Driver/src/stm32f4xx_cec.c|../../Libraries/CMSIS/Device/ST/STM32F4xx/Source/Templates/iar/startup_stm32f446xx.s|../../Libraries/CMSIS/Device/ST/STM32F4xx/Source/Templates/iar/startup_stm32f401xx.s|../../Libraries/CMSIS/Device/ST/STM32F4xx/Source/Templates/iar/startup_stm32f410xx.s|../../Libraries/CMSIS/Device/ST/STM32F4xx/Source/Templates/iar/startup_stm32f412xg.s|../../Libraries/STM32F4xx_StdPeriph_Driver/src/stm32f4xx_dsi.c|../../Libraries/CMSIS/Device/ST/STM32F4xx/Source/Templates/iar/startup_stm32f40_41xxx.s|../../Libraries/CMSIS/Device/ST/STM32F4xx/Source/Templates/iar/startup_stm32f429_439xx.s|../../Libraries/STM32F4xx_StdPeriph_Driver/src/stm32f4xx_qspi.c|../../Libraries/CMSIS/Device/ST/STM32F4xx/Source/Templates/iar/startup_stm32f469_479xx.s" flags="VALUE_WORKSPACE_PATH|RESOLVED" kind="sourcePath" name=""/>
					</sourceEntries>
				</configuration>
			</storageModule>
			<storageModule moduleId="org.eclipse.cdt.core.externalSettings"/>
		</cconfiguration>
	</storageModule>
	<storageModule moduleId="cdtBuildSystem" version="4.0.0">
		<project id="STM32F429_439xx.fr.ac6.managedbuild.target.gnu.cross.exe.1831174437" name="Executable" projectType="fr.ac6.managedbuild.target.gnu.cross.exe"/>
	</storageModule>
	<storageModule moduleId="scannerConfiguration">
		<autodiscovery enabled="true" problemReportingEnabled="true" selectedProfileId=""/>
		<scannerConfigBuildInfo instanceId="fr.ac6.managedbuild.config.gnu.cross.exe.debug.185059192;fr.ac6.managedbuild.config.gnu.cross.exe.debug.185059192.;fr.ac6.managedbuild.tool.gnu.cross.c.compiler.1195731775;fr.ac6.managedbuild.tool.gnu.cross.c.compiler.input.c.1811388834">
			<autodiscovery enabled="false" problemReportingEnabled="true" selectedProfileId=""/>
		</scannerConfigBuildInfo>
		<!--scannerConfigBuildInfo instanceId="fr.ac6.managedbuild.config.gnu.cross.exe.release.$(RELEASE_CONFIG_UID);fr.ac6.managedbuild.config.gnu.cross.exe.release.$(RELEASE_CONFIG_UID).;fr.ac6.managedbuild.tool.gnu.cross.c.compiler.$(RELEASE_TOOL_COMPILER_UID);cdt.managedbuild.tool.gnu.c.compiler.input.$(RELEASE_TOOL_COMPILER_INPUT_UID)">
			<autodiscovery enabled="false" problemReportingEnabled="true" selectedProfileId=""/>
		</scannerConfigBuildInfo-->
	</storageModule>
	<storageModule moduleId="org.eclipse.cdt.core.LanguageSettingsProviders"/>
	<storageModule moduleId="refreshScope" versionNumber="2">
		<configuration artifactName="STM32F411xE" configurationName="Debug">
			<resource resourceType="PROJECT" workspacePath="STM32F411xE"/>
		</configuration>
	</storageModule>
</cproject>
