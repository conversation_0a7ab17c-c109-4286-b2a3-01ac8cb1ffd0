<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<?fileVersion 4.0.0?><cproject storage_type_id="org.eclipse.cdt.core.XmlProjectDescriptionStorage">
	<storageModule moduleId="org.eclipse.cdt.core.settings">
		<cconfiguration id="fr.ac6.managedbuild.config.gnu.cross.exe.debug.215932150">
			<storageModule buildSystemId="org.eclipse.cdt.managedbuilder.core.configurationDataProvider" id="fr.ac6.managedbuild.config.gnu.cross.exe.debug.215932150" moduleId="org.eclipse.cdt.core.settings" name="Debug">
				<externalSettings/>
				<extensions>
					<extension id="org.eclipse.cdt.core.ELF" point="org.eclipse.cdt.core.BinaryParser"/>
					<extension id="org.eclipse.cdt.core.GASErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
					<extension id="org.eclipse.cdt.core.GmakeErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
					<extension id="org.eclipse.cdt.core.GLDErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
					<extension id="org.eclipse.cdt.core.CWDLocator" point="org.eclipse.cdt.core.ErrorParser"/>
					<extension id="org.eclipse.cdt.core.GCCErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
				</extensions>
			</storageModule>
			<storageModule moduleId="cdtBuildSystem" version="4.0.0">
				<configuration artifactExtension="elf" artifactName="STM32F429_439xx" buildArtefactType="org.eclipse.cdt.build.core.buildArtefactType.exe" buildProperties="org.eclipse.cdt.build.core.buildArtefactType=org.eclipse.cdt.build.core.buildArtefactType.exe,org.eclipse.cdt.build.core.buildType=org.eclipse.cdt.build.core.buildType.debug" cleanCommand="rm -rf" description="" id="fr.ac6.managedbuild.config.gnu.cross.exe.debug.215932150" name="Debug" parent="fr.ac6.managedbuild.config.gnu.cross.exe.debug" postannouncebuildStep="Generating binary and Printing size information:" postbuildStep="arm-none-eabi-objcopy -O binary &quot;${BuildArtifactFileBaseName}.elf&quot; &quot;${BuildArtifactFileBaseName}.bin&quot; &amp;&amp; arm-none-eabi-size &quot;${BuildArtifactFileName}&quot;">
					<folderInfo id="fr.ac6.managedbuild.config.gnu.cross.exe.debug.215932150." name="/" resourcePath="">
						<toolChain id="fr.ac6.managedbuild.toolchain.gnu.cross.exe.debug.706391767" name="Ac6 STM32 MCU GCC" superClass="fr.ac6.managedbuild.toolchain.gnu.cross.exe.debug">
							<option id="fr.ac6.managedbuild.option.gnu.cross.prefix.1331339956" name="Prefix" superClass="fr.ac6.managedbuild.option.gnu.cross.prefix" value="arm-none-eabi-" valueType="string"/>
							<option id="fr.ac6.managedbuild.option.gnu.cross.mcu.1171692454" name="Mcu" superClass="fr.ac6.managedbuild.option.gnu.cross.mcu" value="STM32F439NIHx" valueType="string"/>
							<option id="fr.ac6.managedbuild.option.gnu.cross.board.1388491525" name="Board" superClass="fr.ac6.managedbuild.option.gnu.cross.board" value="STM32439I-EVAL2" valueType="string"/>
							<option id="fr.ac6.managedbuild.option.gnu.cross.instructionSet.291746259" name="Instruction Set" superClass="fr.ac6.managedbuild.option.gnu.cross.instructionSet" value="fr.ac6.managedbuild.option.gnu.cross.instructionSet.thumbII" valueType="enumerated"/>
							<option id="fr.ac6.managedbuild.option.gnu.cross.fpu.1563282235" name="Floating point hardware" superClass="fr.ac6.managedbuild.option.gnu.cross.fpu" value="fr.ac6.managedbuild.option.gnu.cross.fpu.fpv4-sp-d16" valueType="enumerated"/>
							<option id="fr.ac6.managedbuild.option.gnu.cross.floatabi.1661157259" name="Floating-point ABI" superClass="fr.ac6.managedbuild.option.gnu.cross.floatabi" value="fr.ac6.managedbuild.option.gnu.cross.floatabi.hard" valueType="enumerated"/>
							<targetPlatform archList="all" binaryParser="org.eclipse.cdt.core.ELF" id="fr.ac6.managedbuild.targetPlatform.gnu.cross.862819642" isAbstract="false" osList="all" superClass="fr.ac6.managedbuild.targetPlatform.gnu.cross"/>
							<builder buildPath="${workspace_loc:/STM32F429_439xx}/Debug" id="fr.ac6.managedbuild.builder.gnu.cross.592042756" keepEnvironmentInBuildfile="false" managedBuildOn="true" name="Gnu Make Builder" superClass="fr.ac6.managedbuild.builder.gnu.cross">
								<outputEntries>
									<entry flags="VALUE_WORKSPACE_PATH|RESOLVED" kind="outputPath" name="Debug"/>
								</outputEntries>
							</builder>
							<tool id="fr.ac6.managedbuild.tool.gnu.cross.c.compiler.817309972" name="MCU GCC Compiler" superClass="fr.ac6.managedbuild.tool.gnu.cross.c.compiler">
								<option defaultValue="gnu.c.optimization.level.none" id="fr.ac6.managedbuild.gnu.c.compiler.option.optimization.level.1632413685" name="Optimization Level" superClass="fr.ac6.managedbuild.gnu.c.compiler.option.optimization.level" useByScannerDiscovery="false" value="fr.ac6.managedbuild.gnu.c.optimization.level.size" valueType="enumerated"/>
								<option id="gnu.c.compiler.option.debugging.level.1110556798" name="Debug Level" superClass="gnu.c.compiler.option.debugging.level" useByScannerDiscovery="false" value="gnu.c.debugging.level.max" valueType="enumerated"/>
								<option id="gnu.c.compiler.option.include.paths.617307317" name="Include paths (-I)" superClass="gnu.c.compiler.option.include.paths" useByScannerDiscovery="false" valueType="includePath">
									<listOptionValue builtIn="false" value="../../../"/>
									<listOptionValue builtIn="false" value="../../../../../Libraries/CMSIS/Device/ST/STM32F4xx/Include"/>
									<listOptionValue builtIn="false" value="../../../../../Libraries/STM32F4xx_StdPeriph_Driver/inc"/>
									<listOptionValue builtIn="false" value="../../../../../Utilities/STM32_EVAL/Common"/>
									<listOptionValue builtIn="false" value="../../../../../Utilities/STM32_EVAL/STM324x9I_EVAL"/>
									<listOptionValue builtIn="false" value="../../../../../Libraries/CMSIS/Include"/>
								</option>
								<option id="gnu.c.compiler.option.preprocessor.def.symbols.1920837038" name="Defined symbols (-D)" superClass="gnu.c.compiler.option.preprocessor.def.symbols" useByScannerDiscovery="false" valueType="definedSymbols">
									<listOptionValue builtIn="false" value="USE_STDPERIPH_DRIVER"/>
									<listOptionValue builtIn="false" value="STM32F429_439xx"/>
									<listOptionValue builtIn="false" value="USE_STM324x9I_EVAL"/>
								</option>
								<option id="fr.ac6.managedbuild.gnu.c.compiler.option.misc.other.162038350" superClass="fr.ac6.managedbuild.gnu.c.compiler.option.misc.other" useByScannerDiscovery="false" value="-fmessage-length=0" valueType="string"/>
								<option id="gnu.c.compiler.option.dialect.std.1995097698" name="Language standard" superClass="gnu.c.compiler.option.dialect.std" useByScannerDiscovery="true" value="gnu.c.compiler.dialect.default" valueType="enumerated"/>
								<inputType id="fr.ac6.managedbuild.tool.gnu.cross.c.compiler.input.c.243359348" superClass="fr.ac6.managedbuild.tool.gnu.cross.c.compiler.input.c"/>
								<inputType id="fr.ac6.managedbuild.tool.gnu.cross.c.compiler.input.s.1287177539" superClass="fr.ac6.managedbuild.tool.gnu.cross.c.compiler.input.s"/>
							</tool>
							<tool id="fr.ac6.managedbuild.tool.gnu.cross.cpp.compiler.1662547489" name="MCU G++ Compiler" superClass="fr.ac6.managedbuild.tool.gnu.cross.cpp.compiler">
								<option id="gnu.cpp.compiler.option.optimization.level.1960748917" name="Optimization Level" superClass="gnu.cpp.compiler.option.optimization.level" useByScannerDiscovery="false" value="gnu.cpp.compiler.optimization.level.none" valueType="enumerated"/>
								<option id="gnu.cpp.compiler.option.debugging.level.824700628" name="Debug Level" superClass="gnu.cpp.compiler.option.debugging.level" useByScannerDiscovery="false" value="gnu.cpp.compiler.debugging.level.max" valueType="enumerated"/>
							</tool>
							<tool id="fr.ac6.managedbuild.tool.gnu.cross.c.linker.442722508" name="MCU GCC Linker" superClass="fr.ac6.managedbuild.tool.gnu.cross.c.linker">
								<option id="fr.ac6.managedbuild.tool.gnu.cross.c.linker.script.1202028834" name="Linker Script (-T)" superClass="fr.ac6.managedbuild.tool.gnu.cross.c.linker.script" value="../STM32F439NIHx_FLASH.ld" valueType="string"/>
								<option id="gnu.c.link.option.libs.570493171" name="Libraries (-l)" superClass="gnu.c.link.option.libs"/>
								<option id="gnu.c.link.option.paths.1714869540" name="Library search path (-L)" superClass="gnu.c.link.option.paths"/>
								<option id="gnu.c.link.option.ldflags.660353574" name="Linker flags" superClass="gnu.c.link.option.ldflags" value="-specs=nosys.specs -specs=nano.specs" valueType="string"/>
								<inputType id="cdt.managedbuild.tool.gnu.c.linker.input.1683054170" superClass="cdt.managedbuild.tool.gnu.c.linker.input">
									<additionalInput kind="additionalinputdependency" paths="$(USER_OBJS)"/>
									<additionalInput kind="additionalinput" paths="$(LIBS)"/>
								</inputType>
							</tool>
							<tool id="fr.ac6.managedbuild.tool.gnu.cross.cpp.linker.1557737611" name="MCU G++ Linker" superClass="fr.ac6.managedbuild.tool.gnu.cross.cpp.linker"/>
							<tool id="fr.ac6.managedbuild.tool.gnu.archiver.1272728132" name="MCU GCC Archiver" superClass="fr.ac6.managedbuild.tool.gnu.archiver"/>
							<tool id="fr.ac6.managedbuild.tool.gnu.cross.assembler.983174931" name="MCU GCC Assembler" superClass="fr.ac6.managedbuild.tool.gnu.cross.assembler">
								<option id="gnu.both.asm.option.include.paths.1389979734" name="Include paths (-I)" superClass="gnu.both.asm.option.include.paths"/>
								<inputType id="cdt.managedbuild.tool.gnu.assembler.input.1176204477" superClass="cdt.managedbuild.tool.gnu.assembler.input"/>
								<inputType id="fr.ac6.managedbuild.tool.gnu.cross.assembler.input.130391261" superClass="fr.ac6.managedbuild.tool.gnu.cross.assembler.input"/>
							</tool>
						</toolChain>
					</folderInfo>
					<sourceEntries>
						<entry excluding="SW4STM32/startup_stm32f412xg.s|SW4STM32/startup_stm32f40_41xxx.s|STM32F4xx_StdPeriph_Driver/stm32f4xx_qspi.c|STM32F4xx_StdPeriph_Driver/stm32f4xx_dsi.c|STM32F4xx_StdPeriph_Driver/stm32f4xx_fsmc.c|STM32F4xx_StdPeriph_Driver/stm32f4xx_cec.c|STM32F4xx_StdPeriph_Driver/stm32f4xx_flash_ramfunc.c|SW4STM32/startup_stm32f469_479xx.s|SW4STM32/startup_stm32f446xx.s|SW4STM32/startup_stm32f427_437xx.s|SW4STM32/startup_stm32f412xg.s|SW4STM32/startup_stm32f411xe.s|SW4STM32/startup_stm32f410xx.s|SW4STM32/startup_stm32f401xx.s|../../Libraries/CMSIS/Device/ST/STM32F4xx/Source/Templates/iar/startup_stm32f469_479xx.s|../../Libraries/CMSIS/Device/ST/STM32F4xx/Source/Templates/iar/startup_stm32f446xx.s|../../Libraries/CMSIS/Device/ST/STM32F4xx/Source/Templates/iar/startup_stm32f411xe.s|../../Libraries/CMSIS/Device/ST/STM32F4xx/Source/Templates/iar/startup_stm32f40_41xxx.s|../../Libraries/STM32F4xx_StdPeriph_Driver/src/stm32f4xx_dsi.c|../../Libraries/STM32F4xx_StdPeriph_Driver/src/stm32f4xx_qspi.c|../../Libraries/STM32F4xx_StdPeriph_Driver/src/stm32f4xx_flash_ramfunc.c|../../Libraries/CMSIS/Device/ST/STM32F4xx/Source/Templates/iar/startup_stm32f401xx.s|../../Libraries/CMSIS/Device/ST/STM32F4xx/Source/Templates/iar/startup_stm32f429_439xx.s|../../Libraries/STM32F4xx_StdPeriph_Driver/src/stm32f4xx_fsmc.c|../../Libraries/STM32F4xx_StdPeriph_Driver/src/stm32f4xx_cec.c|../../Libraries/CMSIS/Device/ST/STM32F4xx/Source/Templates/iar/startup_stm32f410xx.s|../../Libraries/CMSIS/Device/ST/STM32F4xx/Source/Templates/iar/startup_stm32f427_437xx.s" flags="VALUE_WORKSPACE_PATH|RESOLVED" kind="sourcePath" name=""/>
					</sourceEntries>
				</configuration>
			</storageModule>
			<storageModule moduleId="org.eclipse.cdt.core.externalSettings"/>
		</cconfiguration>
	</storageModule>
	<storageModule moduleId="cdtBuildSystem" version="4.0.0">
		<project id="STM32F429_439xx.fr.ac6.managedbuild.target.gnu.cross.exe.1793168462" name="Executable" projectType="fr.ac6.managedbuild.target.gnu.cross.exe"/>
	</storageModule>
	<storageModule moduleId="scannerConfiguration">
		<autodiscovery enabled="true" problemReportingEnabled="true" selectedProfileId=""/>
		<scannerConfigBuildInfo instanceId="fr.ac6.managedbuild.config.gnu.cross.exe.debug.215932150;fr.ac6.managedbuild.config.gnu.cross.exe.debug.215932150.;fr.ac6.managedbuild.tool.gnu.cross.c.compiler.817309972;fr.ac6.managedbuild.tool.gnu.cross.c.compiler.input.c.243359348">
			<autodiscovery enabled="false" problemReportingEnabled="true" selectedProfileId=""/>
		</scannerConfigBuildInfo>
		<!--scannerConfigBuildInfo instanceId="fr.ac6.managedbuild.config.gnu.cross.exe.release.$(RELEASE_CONFIG_UID);fr.ac6.managedbuild.config.gnu.cross.exe.release.$(RELEASE_CONFIG_UID).;fr.ac6.managedbuild.tool.gnu.cross.c.compiler.$(RELEASE_TOOL_COMPILER_UID);cdt.managedbuild.tool.gnu.c.compiler.input.$(RELEASE_TOOL_COMPILER_INPUT_UID)">
			<autodiscovery enabled="false" problemReportingEnabled="true" selectedProfileId=""/>
		</scannerConfigBuildInfo-->
	</storageModule>
	<storageModule moduleId="org.eclipse.cdt.core.LanguageSettingsProviders"/>
	<storageModule moduleId="refreshScope" versionNumber="2">
		<configuration artifactName="STM32F429_439xx" configurationName="Debug">
			<resource resourceType="PROJECT" workspacePath="STM32F429_439xx"/>
		</configuration>
	</storageModule>
	<storageModule moduleId="org.eclipse.cdt.internal.ui.text.commentOwnerProjectMappings"/>
</cproject>
