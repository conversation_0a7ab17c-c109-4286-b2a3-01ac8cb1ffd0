<?xml version="1.0" encoding="UTF-8"?>
<projectDescription>
	<name>STM32F410xx</name>
	<comment></comment>
	<projects>
	</projects>
	<buildSpec>
		<buildCommand>
			<name>org.eclipse.cdt.managedbuilder.core.genmakebuilder</name>
			<triggers>clean,full,incremental,</triggers>
			<arguments>
			</arguments>
		</buildCommand>
		<buildCommand>
			<name>org.eclipse.cdt.managedbuilder.core.ScannerConfigBuilder</name>
			<triggers>full,incremental,</triggers>
			<arguments>
			</arguments>
		</buildCommand>
	</buildSpec>
	<natures>
		<nature>org.eclipse.cdt.core.cnature</nature>
		<nature>org.eclipse.cdt.managedbuilder.core.managedBuildNature</nature>
		<nature>org.eclipse.cdt.managedbuilder.core.ScannerConfigNature</nature>
		<nature>fr.ac6.mcu.ide.core.MCUProjectNature</nature>
	</natures>
	<linkedResources>
		<link>
			<name>CMSIS/system_stm32f4xx.c</name>
			<type>1</type>
			<locationURI>PARENT-2-PROJECT_LOC/system_stm32f4xx.c</locationURI>
		</link>
		<link>
			<name>Doc/readme.txt</name>
			<type>1</type>
			<locationURI>PARENT-2-PROJECT_LOC/readme.txt</locationURI>
		</link>
		<link>
			<name>STM32F4xx_StdPeriph_Driver/misc.c</name>
			<type>1</type>
			<locationURI>PARENT-4-PROJECT_LOC/Libraries/STM32F4xx_StdPeriph_Driver/src/misc.c</locationURI>
		</link>
		<link>
			<name>STM32F4xx_StdPeriph_Driver/stm32f4xx_adc.c</name>
			<type>1</type>
			<locationURI>PARENT-4-PROJECT_LOC/Libraries/STM32F4xx_StdPeriph_Driver/src/stm32f4xx_adc.c</locationURI>
		</link>
		<link>
			<name>STM32F4xx_StdPeriph_Driver/stm32f4xx_can.c</name>
			<type>1</type>
			<locationURI>PARENT-4-PROJECT_LOC/Libraries/STM32F4xx_StdPeriph_Driver/src/stm32f4xx_can.c</locationURI>
		</link>
		<link>
			<name>STM32F4xx_StdPeriph_Driver/stm32f4xx_cec.c</name>
			<type>1</type>
			<locationURI>PARENT-4-PROJECT_LOC/Libraries/STM32F4xx_StdPeriph_Driver/src/stm32f4xx_cec.c</locationURI>
		</link>
		<link>
			<name>STM32F4xx_StdPeriph_Driver/stm32f4xx_crc.c</name>
			<type>1</type>
			<locationURI>PARENT-4-PROJECT_LOC/Libraries/STM32F4xx_StdPeriph_Driver/src/stm32f4xx_crc.c</locationURI>
		</link>
		<link>
			<name>STM32F4xx_StdPeriph_Driver/stm32f4xx_cryp.c</name>
			<type>1</type>
			<locationURI>PARENT-4-PROJECT_LOC/Libraries/STM32F4xx_StdPeriph_Driver/src/stm32f4xx_cryp.c</locationURI>
		</link>
		<link>
			<name>STM32F4xx_StdPeriph_Driver/stm32f4xx_cryp_aes.c</name>
			<type>1</type>
			<locationURI>PARENT-4-PROJECT_LOC/Libraries/STM32F4xx_StdPeriph_Driver/src/stm32f4xx_cryp_aes.c</locationURI>
		</link>
		<link>
			<name>STM32F4xx_StdPeriph_Driver/stm32f4xx_cryp_des.c</name>
			<type>1</type>
			<locationURI>PARENT-4-PROJECT_LOC/Libraries/STM32F4xx_StdPeriph_Driver/src/stm32f4xx_cryp_des.c</locationURI>
		</link>
		<link>
			<name>STM32F4xx_StdPeriph_Driver/stm32f4xx_cryp_tdes.c</name>
			<type>1</type>
			<locationURI>PARENT-4-PROJECT_LOC/Libraries/STM32F4xx_StdPeriph_Driver/src/stm32f4xx_cryp_tdes.c</locationURI>
		</link>
		<link>
			<name>STM32F4xx_StdPeriph_Driver/stm32f4xx_dac.c</name>
			<type>1</type>
			<locationURI>PARENT-4-PROJECT_LOC/Libraries/STM32F4xx_StdPeriph_Driver/src/stm32f4xx_dac.c</locationURI>
		</link>
		<link>
			<name>STM32F4xx_StdPeriph_Driver/stm32f4xx_dbgmcu.c</name>
			<type>1</type>
			<locationURI>PARENT-4-PROJECT_LOC/Libraries/STM32F4xx_StdPeriph_Driver/src/stm32f4xx_dbgmcu.c</locationURI>
		</link>
		<link>
			<name>STM32F4xx_StdPeriph_Driver/stm32f4xx_dcmi.c</name>
			<type>1</type>
			<locationURI>PARENT-4-PROJECT_LOC/Libraries/STM32F4xx_StdPeriph_Driver/src/stm32f4xx_dcmi.c</locationURI>
		</link>
		<link>
			<name>STM32F4xx_StdPeriph_Driver/stm32f4xx_dfsdm.c</name>
			<type>1</type>
			<locationURI>PARENT-4-PROJECT_LOC/Libraries/STM32F4xx_StdPeriph_Driver/src/stm32f4xx_dfsdm.c</locationURI>
		</link>
		<link>
			<name>STM32F4xx_StdPeriph_Driver/stm32f4xx_dma.c</name>
			<type>1</type>
			<locationURI>PARENT-4-PROJECT_LOC/Libraries/STM32F4xx_StdPeriph_Driver/src/stm32f4xx_dma.c</locationURI>
		</link>
		<link>
			<name>STM32F4xx_StdPeriph_Driver/stm32f4xx_dma2d.c</name>
			<type>1</type>
			<locationURI>PARENT-4-PROJECT_LOC/Libraries/STM32F4xx_StdPeriph_Driver/src/stm32f4xx_dma2d.c</locationURI>
		</link>
		<link>
			<name>STM32F4xx_StdPeriph_Driver/stm32f4xx_dsi.c</name>
			<type>1</type>
			<locationURI>PARENT-4-PROJECT_LOC/Libraries/STM32F4xx_StdPeriph_Driver/src/stm32f4xx_dsi.c</locationURI>
		</link>
		<link>
			<name>STM32F4xx_StdPeriph_Driver/stm32f4xx_exti.c</name>
			<type>1</type>
			<locationURI>PARENT-4-PROJECT_LOC/Libraries/STM32F4xx_StdPeriph_Driver/src/stm32f4xx_exti.c</locationURI>
		</link>
		<link>
			<name>STM32F4xx_StdPeriph_Driver/stm32f4xx_flash.c</name>
			<type>1</type>
			<locationURI>PARENT-4-PROJECT_LOC/Libraries/STM32F4xx_StdPeriph_Driver/src/stm32f4xx_flash.c</locationURI>
		</link>
		<link>
			<name>STM32F4xx_StdPeriph_Driver/stm32f4xx_flash_ramfunc.c</name>
			<type>1</type>
			<locationURI>PARENT-4-PROJECT_LOC/Libraries/STM32F4xx_StdPeriph_Driver/src/stm32f4xx_flash_ramfunc.c</locationURI>
		</link>
		<link>
			<name>STM32F4xx_StdPeriph_Driver/stm32f4xx_fmc.c</name>
			<type>1</type>
			<locationURI>PARENT-4-PROJECT_LOC/Libraries/STM32F4xx_StdPeriph_Driver/src/stm32f4xx_fmc.c</locationURI>
		</link>
		<link>
			<name>STM32F4xx_StdPeriph_Driver/stm32f4xx_fmpi2c.c</name>
			<type>1</type>
			<locationURI>PARENT-4-PROJECT_LOC/Libraries/STM32F4xx_StdPeriph_Driver/src/stm32f4xx_fmpi2c.c</locationURI>
		</link>
		<link>
			<name>STM32F4xx_StdPeriph_Driver/stm32f4xx_fsmc.c</name>
			<type>1</type>
			<locationURI>PARENT-4-PROJECT_LOC/Libraries/STM32F4xx_StdPeriph_Driver/src/stm32f4xx_fsmc.c</locationURI>
		</link>
		<link>
			<name>STM32F4xx_StdPeriph_Driver/stm32f4xx_gpio.c</name>
			<type>1</type>
			<locationURI>PARENT-4-PROJECT_LOC/Libraries/STM32F4xx_StdPeriph_Driver/src/stm32f4xx_gpio.c</locationURI>
		</link>
		<link>
			<name>STM32F4xx_StdPeriph_Driver/stm32f4xx_hash.c</name>
			<type>1</type>
			<locationURI>PARENT-4-PROJECT_LOC/Libraries/STM32F4xx_StdPeriph_Driver/src/stm32f4xx_hash.c</locationURI>
		</link>
		<link>
			<name>STM32F4xx_StdPeriph_Driver/stm32f4xx_hash_md5.c</name>
			<type>1</type>
			<locationURI>PARENT-4-PROJECT_LOC/Libraries/STM32F4xx_StdPeriph_Driver/src/stm32f4xx_hash_md5.c</locationURI>
		</link>
		<link>
			<name>STM32F4xx_StdPeriph_Driver/stm32f4xx_hash_sha1.c</name>
			<type>1</type>
			<locationURI>PARENT-4-PROJECT_LOC/Libraries/STM32F4xx_StdPeriph_Driver/src/stm32f4xx_hash_sha1.c</locationURI>
		</link>
		<link>
			<name>STM32F4xx_StdPeriph_Driver/stm32f4xx_i2c.c</name>
			<type>1</type>
			<locationURI>PARENT-4-PROJECT_LOC/Libraries/STM32F4xx_StdPeriph_Driver/src/stm32f4xx_i2c.c</locationURI>
		</link>
		<link>
			<name>STM32F4xx_StdPeriph_Driver/stm32f4xx_iwdg.c</name>
			<type>1</type>
			<locationURI>PARENT-4-PROJECT_LOC/Libraries/STM32F4xx_StdPeriph_Driver/src/stm32f4xx_iwdg.c</locationURI>
		</link>
		<link>
			<name>STM32F4xx_StdPeriph_Driver/stm32f4xx_lptim.c</name>
			<type>1</type>
			<locationURI>PARENT-4-PROJECT_LOC/Libraries/STM32F4xx_StdPeriph_Driver/src/stm32f4xx_lptim.c</locationURI>
		</link>
		<link>
			<name>STM32F4xx_StdPeriph_Driver/stm32f4xx_ltdc.c</name>
			<type>1</type>
			<locationURI>PARENT-4-PROJECT_LOC/Libraries/STM32F4xx_StdPeriph_Driver/src/stm32f4xx_ltdc.c</locationURI>
		</link>
		<link>
			<name>STM32F4xx_StdPeriph_Driver/stm32f4xx_pwr.c</name>
			<type>1</type>
			<locationURI>PARENT-4-PROJECT_LOC/Libraries/STM32F4xx_StdPeriph_Driver/src/stm32f4xx_pwr.c</locationURI>
		</link>
		<link>
			<name>STM32F4xx_StdPeriph_Driver/stm32f4xx_qspi.c</name>
			<type>1</type>
			<locationURI>PARENT-4-PROJECT_LOC/Libraries/STM32F4xx_StdPeriph_Driver/src/stm32f4xx_qspi.c</locationURI>
		</link>
		<link>
			<name>STM32F4xx_StdPeriph_Driver/stm32f4xx_rcc.c</name>
			<type>1</type>
			<locationURI>PARENT-4-PROJECT_LOC/Libraries/STM32F4xx_StdPeriph_Driver/src/stm32f4xx_rcc.c</locationURI>
		</link>
		<link>
			<name>STM32F4xx_StdPeriph_Driver/stm32f4xx_rng.c</name>
			<type>1</type>
			<locationURI>PARENT-4-PROJECT_LOC/Libraries/STM32F4xx_StdPeriph_Driver/src/stm32f4xx_rng.c</locationURI>
		</link>
		<link>
			<name>STM32F4xx_StdPeriph_Driver/stm32f4xx_rtc.c</name>
			<type>1</type>
			<locationURI>PARENT-4-PROJECT_LOC/Libraries/STM32F4xx_StdPeriph_Driver/src/stm32f4xx_rtc.c</locationURI>
		</link>
		<link>
			<name>STM32F4xx_StdPeriph_Driver/stm32f4xx_sai.c</name>
			<type>1</type>
			<locationURI>PARENT-4-PROJECT_LOC/Libraries/STM32F4xx_StdPeriph_Driver/src/stm32f4xx_sai.c</locationURI>
		</link>
		<link>
			<name>STM32F4xx_StdPeriph_Driver/stm32f4xx_sdio.c</name>
			<type>1</type>
			<locationURI>PARENT-4-PROJECT_LOC/Libraries/STM32F4xx_StdPeriph_Driver/src/stm32f4xx_sdio.c</locationURI>
		</link>
		<link>
			<name>STM32F4xx_StdPeriph_Driver/stm32f4xx_spdifrx.c</name>
			<type>1</type>
			<locationURI>PARENT-4-PROJECT_LOC/Libraries/STM32F4xx_StdPeriph_Driver/src/stm32f4xx_spdifrx.c</locationURI>
		</link>
		<link>
			<name>STM32F4xx_StdPeriph_Driver/stm32f4xx_spi.c</name>
			<type>1</type>
			<locationURI>PARENT-4-PROJECT_LOC/Libraries/STM32F4xx_StdPeriph_Driver/src/stm32f4xx_spi.c</locationURI>
		</link>
		<link>
			<name>STM32F4xx_StdPeriph_Driver/stm32f4xx_syscfg.c</name>
			<type>1</type>
			<locationURI>PARENT-4-PROJECT_LOC/Libraries/STM32F4xx_StdPeriph_Driver/src/stm32f4xx_syscfg.c</locationURI>
		</link>
		<link>
			<name>STM32F4xx_StdPeriph_Driver/stm32f4xx_tim.c</name>
			<type>1</type>
			<locationURI>PARENT-4-PROJECT_LOC/Libraries/STM32F4xx_StdPeriph_Driver/src/stm32f4xx_tim.c</locationURI>
		</link>
		<link>
			<name>STM32F4xx_StdPeriph_Driver/stm32f4xx_usart.c</name>
			<type>1</type>
			<locationURI>PARENT-4-PROJECT_LOC/Libraries/STM32F4xx_StdPeriph_Driver/src/stm32f4xx_usart.c</locationURI>
		</link>
		<link>
			<name>STM32F4xx_StdPeriph_Driver/stm32f4xx_wwdg.c</name>
			<type>1</type>
			<locationURI>PARENT-4-PROJECT_LOC/Libraries/STM32F4xx_StdPeriph_Driver/src/stm32f4xx_wwdg.c</locationURI>
		</link>
		<link>
			<name>SW4STM32/startup_stm32f401xx.s</name>
			<type>1</type>
			<locationURI>PARENT-4-PROJECT_LOC/Libraries/CMSIS/Device/ST/STM32F4xx/Source/Templates/SW4STM32/startup_stm32f401xx.s</locationURI>
		</link>
		<link>
			<name>SW4STM32/startup_stm32f40_41xxx.s</name>
			<type>1</type>
			<locationURI>PARENT-4-PROJECT_LOC/Libraries/CMSIS/Device/ST/STM32F4xx/Source/Templates/SW4STM32/startup_stm32f40_41xxx.s</locationURI>
		</link>
		<link>
			<name>SW4STM32/startup_stm32f410xx.s</name>
			<type>1</type>
			<locationURI>PARENT-4-PROJECT_LOC/Libraries/CMSIS/Device/ST/STM32F4xx/Source/Templates/SW4STM32/startup_stm32f410xx.s</locationURI>
		</link>
		<link>
			<name>SW4STM32/startup_stm32f411xe.s</name>
			<type>1</type>
			<locationURI>PARENT-4-PROJECT_LOC/Libraries/CMSIS/Device/ST/STM32F4xx/Source/Templates/SW4STM32/startup_stm32f411xe.s</locationURI>
		</link>
		<link>
			<name>SW4STM32/startup_stm32f412xg.s</name>
			<type>1</type>
			<locationURI>PARENT-4-PROJECT_LOC/Libraries/CMSIS/Device/ST/STM32F4xx/Source/Templates/SW4STM32/startup_stm32f412xg.s</locationURI>
		</link>
		<link>
			<name>SW4STM32/startup_stm32f427_437xx.s</name>
			<type>1</type>
			<locationURI>PARENT-4-PROJECT_LOC/Libraries/CMSIS/Device/ST/STM32F4xx/Source/Templates/SW4STM32/startup_stm32f427_437xx.s</locationURI>
		</link>
		<link>
			<name>SW4STM32/startup_stm32f429_439xx.s</name>
			<type>1</type>
			<locationURI>PARENT-4-PROJECT_LOC/Libraries/CMSIS/Device/ST/STM32F4xx/Source/Templates/SW4STM32/startup_stm32f429_439xx.s</locationURI>
		</link>
		<link>
			<name>SW4STM32/startup_stm32f446xx.s</name>
			<type>1</type>
			<locationURI>PARENT-4-PROJECT_LOC/Libraries/CMSIS/Device/ST/STM32F4xx/Source/Templates/SW4STM32/startup_stm32f446xx.s</locationURI>
		</link>
		<link>
			<name>SW4STM32/startup_stm32f469_479xx.s</name>
			<type>1</type>
			<locationURI>PARENT-4-PROJECT_LOC/Libraries/CMSIS/Device/ST/STM32F4xx/Source/Templates/SW4STM32/startup_stm32f469_479xx.s</locationURI>
		</link>
		<link>
			<name>User/main.c</name>
			<type>1</type>
			<locationURI>PARENT-2-PROJECT_LOC/main.c</locationURI>
		</link>
		<link>
			<name>User/stm32f4xx_it.c</name>
			<type>1</type>
			<locationURI>PARENT-2-PROJECT_LOC/stm32f4xx_it.c</locationURI>
		</link>
	</linkedResources>
</projectDescription>
